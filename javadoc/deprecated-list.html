<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Deprecated List (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Deprecated List (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#interface">Deprecated Interfaces</a></li>
<li><a href="#method">Deprecated Methods</a></li>
<li><a href="#constructor">Deprecated Constructors</a></li>
</ul>
</div>
<div class="contentContainer"><a name="interface">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Interfaces table, listing deprecated interfaces, and an explanation">
<caption><span>Deprecated Interfaces</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Interface and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="robocode/control/RobocodeListener.html" title="interface in robocode.control">robocode.control.RobocodeListener</a>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the <a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a> instead.
 <p>
 A listener interface for receiving callbacks from the <a href="robocode/control/RobocodeEngine.html" title="class in robocode.control"><code>RobocodeEngine</code></a>.</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Methods table, listing deprecated methods, and an explanation">
<caption><span>Deprecated Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="robocode/control/RobocodeListener.html#battleAborted-robocode.control.BattleSpecification-">robocode.control.RobocodeListener.battleAborted(BattleSpecification)</a>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>IBattleListener.onBattleFinished()</code></a> instead.
 <p>
 This method is called when a battle has been aborted.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/control/RobocodeListener.html#battleComplete-robocode.control.BattleSpecification-robocode.control.RobotResults:A-">robocode.control.RobocodeListener.battleComplete(BattleSpecification, RobotResults[])</a>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>IBattleListener.onBattleCompleted()</code></a> instead.
 <p>
 This method is called when a battle completes successfully.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/control/RobocodeListener.html#battleMessage-java.lang.String-">robocode.control.RobocodeListener.battleMessage(String)</a>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-"><code>IBattleListener.onBattleMessage()</code></a> instead.
 <p>
 This method is called when the game logs messages that is normally
 written out to the console.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#endTurn--">robocode._AdvancedRobot.endTurn()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#execute--"><code>execute</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_Robot.html#getBattleNum--">robocode._Robot.getBattleNum()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getRoundNum--"><code>getRoundNum()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/HitRobotEvent.html#getBearingDegrees--">robocode.HitRobotEvent.getBearingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitRobotEvent.html#getBearing--"><code>HitRobotEvent.getBearing()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/HitWallEvent.html#getBearingDegrees--">robocode.HitWallEvent.getBearingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitWallEvent.html#getBearing--"><code>HitWallEvent.getBearing()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_Robot.html#getGunCharge--">robocode._Robot.getGunCharge()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#getGunHeadingDegrees--">robocode._AdvancedRobot.getGunHeadingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getGunHeading--"><code>getGunHeading()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_Robot.html#getGunImageName--">robocode._Robot.getGunImageName()</a>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/HitByBulletEvent.html#getHeadingDegrees--">robocode.HitByBulletEvent.getHeadingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitByBulletEvent.html#getHeading--"><code>HitByBulletEvent.getHeading()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#getHeadingDegrees--">robocode._AdvancedRobot.getHeadingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getHeading--"><code>getHeading()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/BulletHitEvent.html#getLife--">robocode.BulletHitEvent.getLife()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/BulletHitEvent.html#getEnergy--"><code>BulletHitEvent.getEnergy()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getLife--">robocode.ScannedRobotEvent.getLife()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getEnergy--"><code>ScannedRobotEvent.getEnergy()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_Robot.html#getLife--">robocode._Robot.getLife()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getEnergy--"><code>getEnergy()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#getMaxWaitCount--">robocode._AdvancedRobot.getMaxWaitCount()</a>
<div class="block"><span class="deprecationComment">This method is no longer functional.
             Use <a href="robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-"><code>AdvancedRobot.onSkippedTurn(SkippedTurnEvent)</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_Robot.html#getNumBattles--">robocode._Robot.getNumBattles()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getNumRounds--"><code>getNumRounds()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#getRadarHeadingDegrees--">robocode._AdvancedRobot.getRadarHeadingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getRadarHeading--"><code>getRadarHeading()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_Robot.html#getRadarImageName--">robocode._Robot.getRadarImageName()</a>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotBearing--">robocode.ScannedRobotEvent.getRobotBearing()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getBearing--"><code>ScannedRobotEvent.getBearing()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotBearingDegrees--">robocode.ScannedRobotEvent.getRobotBearingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getBearing--"><code>ScannedRobotEvent.getBearing()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotBearingRadians--">robocode.ScannedRobotEvent.getRobotBearingRadians()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getBearingRadians--"><code>ScannedRobotEvent.getBearingRadians()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotDistance--">robocode.ScannedRobotEvent.getRobotDistance()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getDistance--"><code>ScannedRobotEvent.getDistance()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotHeading--">robocode.ScannedRobotEvent.getRobotHeading()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getHeading--"><code>ScannedRobotEvent.getHeading()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotHeadingDegrees--">robocode.ScannedRobotEvent.getRobotHeadingDegrees()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getHeading--"><code>ScannedRobotEvent.getHeading()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotHeadingRadians--">robocode.ScannedRobotEvent.getRobotHeadingRadians()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getHeadingRadians--"><code>ScannedRobotEvent.getHeadingRadians()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_Robot.html#getRobotImageName--">robocode._Robot.getRobotImageName()</a>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/BulletHitEvent.html#getRobotLife--">robocode.BulletHitEvent.getRobotLife()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/BulletHitEvent.html#getEnergy--"><code>BulletHitEvent.getEnergy()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotLife--">robocode.ScannedRobotEvent.getRobotLife()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getEnergy--"><code>ScannedRobotEvent.getEnergy()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/BulletHitEvent.html#getRobotName--">robocode.BulletHitEvent.getRobotName()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/BulletHitEvent.html#getName--"><code>BulletHitEvent.getName()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/HitRobotEvent.html#getRobotName--">robocode.HitRobotEvent.getRobotName()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitRobotEvent.html#getName--"><code>HitRobotEvent.getName()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/RobotDeathEvent.html#getRobotName--">robocode.RobotDeathEvent.getRobotName()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/RobotDeathEvent.html#getName--"><code>RobotDeathEvent.getName()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotName--">robocode.ScannedRobotEvent.getRobotName()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getName--"><code>ScannedRobotEvent.getName()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#getRobotVelocity--">robocode.ScannedRobotEvent.getRobotVelocity()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getVelocity--"><code>ScannedRobotEvent.getVelocity()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#getWaitCount--">robocode._AdvancedRobot.getWaitCount()</a>
<div class="block"><span class="deprecationComment">This method is no longer functional.
             Use <a href="robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-"><code>AdvancedRobot.onSkippedTurn(SkippedTurnEvent)</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_Robot.html#setGunImageName-java.lang.String-">robocode._Robot.setGunImageName(String)</a>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_Robot.html#setRadarImageName-java.lang.String-">robocode._Robot.setRadarImageName(String)</a>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_Robot.html#setRobotImageName-java.lang.String-">robocode._Robot.setRobotImageName(String)</a>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#setTurnGunLeftDegrees-double-">robocode._AdvancedRobot.setTurnGunLeftDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnGunLeft-double-"><code>setTurnGunLeft</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#setTurnGunRightDegrees-double-">robocode._AdvancedRobot.setTurnGunRightDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnGunRight-double-"><code>setTurnGunRight</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#setTurnLeftDegrees-double-">robocode._AdvancedRobot.setTurnLeftDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnLeft-double-"><code>setTurnLeft(double)</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#setTurnRadarLeftDegrees-double-">robocode._AdvancedRobot.setTurnRadarLeftDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnRadarLeft-double-"><code>setTurnRadarLeft(double)</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#setTurnRadarRightDegrees-double-">robocode._AdvancedRobot.setTurnRadarRightDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnRadarRight-double-"><code>setTurnRadarRight</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#setTurnRightDegrees-double-">robocode._AdvancedRobot.setTurnRightDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnRight-double-"><code>setTurnRight(double)</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#turnGunLeftDegrees-double-">robocode._AdvancedRobot.turnGunLeftDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#turnGunRightDegrees-double-">robocode._AdvancedRobot.turnGunRightDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnGunRight-double-"><code>turnGunRight</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#turnLeftDegrees-double-">robocode._AdvancedRobot.turnLeftDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#turnRadarLeftDegrees-double-">robocode._AdvancedRobot.turnRadarLeftDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#turnRadarRightDegrees-double-">robocode._AdvancedRobot.turnRadarRightDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight</code></a>
             instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/_AdvancedRobot.html#turnRightDegrees-double-">robocode._AdvancedRobot.turnRightDegrees(double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a> instead.</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="constructor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Constructors table, listing deprecated constructors, and an explanation">
<caption><span>Deprecated Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="robocode/control/RobocodeEngine.html#RobocodeEngine-java.io.File-robocode.control.RobocodeListener-">robocode.control.RobocodeEngine(File, RobocodeListener)</a>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use <a href="robocode/control/RobocodeEngine.html#RobocodeEngine-java.io.File-"><code>RobocodeEngine.RobocodeEngine(File)</code></a> and
 <a href="robocode/control/RobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-"><code>addBattleListener()</code></a> instead.
 <p>
 Creates a new RobocodeEngine for controlling Robocode.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/control/RobocodeEngine.html#RobocodeEngine-robocode.control.RobocodeListener-">robocode.control.RobocodeEngine(RobocodeListener)</a>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use <a href="robocode/control/RobocodeEngine.html#RobocodeEngine--"><code>RobocodeEngine.RobocodeEngine()</code></a> and
 <a href="robocode/control/RobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-"><code>addBattleListener()</code></a> instead.
 <p>
 Creates a new RobocodeEngine for controlling Robocode. The JAR file of
 Robocode is used to determine the root directory of Robocode.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/RobocodeFileOutputStream.html#RobocodeFileOutputStream-java.io.FileDescriptor-">robocode.RobocodeFileOutputStream(FileDescriptor)</a></td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent--">robocode.ScannedRobotEvent()</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent-java.lang.String-double-double-double-double-double-boolean-"><code>ScannedRobotEvent.ScannedRobotEvent(String, double, double, double, double, double, boolean)</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent-java.lang.String-double-double-double-double-double-">robocode.ScannedRobotEvent(String, double, double, double, double, double)</a>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent-java.lang.String-double-double-double-double-double-boolean-"><code>ScannedRobotEvent.ScannedRobotEvent(String, double, double, double, double, double, boolean)</code></a> instead.</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
