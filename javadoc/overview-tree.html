<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class Hierarchy (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Class Hierarchy (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">Frames</a></li>
<li><a href="overview-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For All Packages</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="robocode/package-tree.html">robocode</a>, </li>
<li><a href="robocode/annotation/package-tree.html">robocode.annotation</a>, </li>
<li><a href="robocode/control/package-tree.html">robocode.control</a>, </li>
<li><a href="robocode/control/events/package-tree.html">robocode.control.events</a>, </li>
<li><a href="robocode/control/snapshot/package-tree.html">robocode.control.snapshot</a>, </li>
<li><a href="robocode/robotinterfaces/package-tree.html">robocode.robotinterfaces</a>, </li>
<li><a href="robocode/robotinterfaces/peer/package-tree.html">robocode.robotinterfaces.peer</a>, </li>
<li><a href="robocode/util/package-tree.html">robocode.util</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/_RobotBase.html" title="class in robocode"><span class="typeNameLink">_RobotBase</span></a> (implements robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>)
<ul>
<li type="circle">robocode.<a href="robocode/_Robot.html" title="class in robocode"><span class="typeNameLink">_Robot</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/Robot.html" title="class in robocode"><span class="typeNameLink">Robot</span></a> (implements robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, robocode.robotinterfaces.<a href="robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>, robocode.robotinterfaces.<a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a>)
<ul>
<li type="circle">robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">_AdvancedRobot</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode"><span class="typeNameLink">_AdvancedRadiansRobot</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">AdvancedRobot</span></a> (implements robocode.robotinterfaces.<a href="robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a>, robocode.robotinterfaces.<a href="robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a>)
<ul>
<li type="circle">robocode.<a href="robocode/TeamRobot.html" title="class in robocode"><span class="typeNameLink">TeamRobot</span></a> (implements robocode.robotinterfaces.<a href="robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a>, robocode.robotinterfaces.<a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a>)
<ul>
<li type="circle">robocode.<a href="robocode/RateControlRobot.html" title="class in robocode"><span class="typeNameLink">RateControlRobot</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">robocode.<a href="robocode/JuniorRobot.html" title="class in robocode"><span class="typeNameLink">JuniorRobot</span></a> (implements robocode.robotinterfaces.<a href="robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces">IJuniorRobot</a>)</li>
</ul>
</li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events"><span class="typeNameLink">BattleAdaptor</span></a> (implements robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a>)
<ul>
<li type="circle">robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control"><span class="typeNameLink">RobotTestBed</span></a>&lt;R&gt;</li>
</ul>
</li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleEvent</span></a>
<ul>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleCompletedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleErrorEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleFinishedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleMessageEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattlePausedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleResumedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleStartedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">RoundEndedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">RoundStartedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">TurnEndedEvent</span></a></li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">TurnStartedEvent</span></a></li>
</ul>
</li>
<li type="circle">robocode.control.<a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control"><span class="typeNameLink">BattlefieldSpecification</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="robocode/BattleResults.html" title="class in robocode"><span class="typeNameLink">BattleResults</span></a> (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)
<ul>
<li type="circle">robocode.control.<a href="robocode/control/RobotResults.html" title="class in robocode.control"><span class="typeNameLink">RobotResults</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="robocode/BattleRules.html" title="class in robocode"><span class="typeNameLink">BattleRules</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control"><span class="typeNameLink">BattleSpecification</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="robocode/Bullet.html" title="class in robocode"><span class="typeNameLink">Bullet</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="robocode/Condition.html" title="class in robocode"><span class="typeNameLink">Condition</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/GunTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">GunTurnCompleteCondition</span></a></li>
<li type="circle">robocode.<a href="robocode/MoveCompleteCondition.html" title="class in robocode"><span class="typeNameLink">MoveCompleteCondition</span></a></li>
<li type="circle">robocode.<a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">RadarTurnCompleteCondition</span></a></li>
<li type="circle">robocode.<a href="robocode/TurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">TurnCompleteCondition</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="robocode/Event.html" title="class in robocode"><span class="typeNameLink">Event</span></a> (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)
<ul>
<li type="circle">robocode.<a href="robocode/BattleEndedEvent.html" title="class in robocode"><span class="typeNameLink">BattleEndedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/BulletHitBulletEvent.html" title="class in robocode"><span class="typeNameLink">BulletHitBulletEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode"><span class="typeNameLink">BulletHitEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/BulletMissedEvent.html" title="class in robocode"><span class="typeNameLink">BulletMissedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/CustomEvent.html" title="class in robocode"><span class="typeNameLink">CustomEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/DeathEvent.html" title="class in robocode"><span class="typeNameLink">DeathEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode"><span class="typeNameLink">HitByBulletEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode"><span class="typeNameLink">HitRobotEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/HitWallEvent.html" title="class in robocode"><span class="typeNameLink">HitWallEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/KeyEvent.html" title="class in robocode"><span class="typeNameLink">KeyEvent</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/KeyPressedEvent.html" title="class in robocode"><span class="typeNameLink">KeyPressedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/KeyReleasedEvent.html" title="class in robocode"><span class="typeNameLink">KeyReleasedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/KeyTypedEvent.html" title="class in robocode"><span class="typeNameLink">KeyTypedEvent</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="robocode/MessageEvent.html" title="class in robocode"><span class="typeNameLink">MessageEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MouseEvent.html" title="class in robocode"><span class="typeNameLink">MouseEvent</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/MouseClickedEvent.html" title="class in robocode"><span class="typeNameLink">MouseClickedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MouseDraggedEvent.html" title="class in robocode"><span class="typeNameLink">MouseDraggedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MouseEnteredEvent.html" title="class in robocode"><span class="typeNameLink">MouseEnteredEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MouseExitedEvent.html" title="class in robocode"><span class="typeNameLink">MouseExitedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MouseMovedEvent.html" title="class in robocode"><span class="typeNameLink">MouseMovedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MousePressedEvent.html" title="class in robocode"><span class="typeNameLink">MousePressedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MouseReleasedEvent.html" title="class in robocode"><span class="typeNameLink">MouseReleasedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/MouseWheelMovedEvent.html" title="class in robocode"><span class="typeNameLink">MouseWheelMovedEvent</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="robocode/PaintEvent.html" title="class in robocode"><span class="typeNameLink">PaintEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/RobotDeathEvent.html" title="class in robocode"><span class="typeNameLink">RobotDeathEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/RoundEndedEvent.html" title="class in robocode"><span class="typeNameLink">RoundEndedEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode"><span class="typeNameLink">ScannedRobotEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/SkippedTurnEvent.html" title="class in robocode"><span class="typeNameLink">SkippedTurnEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/StatusEvent.html" title="class in robocode"><span class="typeNameLink">StatusEvent</span></a></li>
<li type="circle">robocode.<a href="robocode/WinEvent.html" title="class in robocode"><span class="typeNameLink">WinEvent</span></a></li>
</ul>
</li>
<li type="circle">java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io"><span class="typeNameLink">OutputStream</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</a>, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Flushable.html?is-external=true" title="class or interface in java.io">Flushable</a>)
<ul>
<li type="circle">robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode"><span class="typeNameLink">RobocodeFileOutputStream</span></a></li>
</ul>
</li>
<li type="circle">robocode.control.<a href="robocode/control/RandomFactory.html" title="class in robocode.control"><span class="typeNameLink">RandomFactory</span></a></li>
<li type="circle">robocode.<a href="robocode/Robocode.html" title="class in robocode"><span class="typeNameLink">Robocode</span></a></li>
<li type="circle">robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control"><span class="typeNameLink">RobocodeEngine</span></a> (implements robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a>)</li>
<li type="circle">robocode.control.<a href="robocode/control/RobotSetup.html" title="class in robocode.control"><span class="typeNameLink">RobotSetup</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control"><span class="typeNameLink">RobotSpecification</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="robocode/RobotStatus.html" title="class in robocode"><span class="typeNameLink">RobotStatus</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="robocode/Rules.html" title="class in robocode"><span class="typeNameLink">Rules</span></a></li>
<li type="circle">robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util"><span class="typeNameLink">Utils</span></a></li>
<li type="circle">java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true" title="class or interface in java.io"><span class="typeNameLink">Writer</span></a> (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Appendable.html?is-external=true" title="class or interface in java.lang">Appendable</a>, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</a>, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Flushable.html?is-external=true" title="class or interface in java.io">Flushable</a>)
<ul>
<li type="circle">java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true" title="class or interface in java.io"><span class="typeNameLink">OutputStreamWriter</span></a>
<ul>
<li type="circle">robocode.<a href="robocode/RobocodeFileWriter.html" title="class in robocode"><span class="typeNameLink">RobocodeFileWriter</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">robocode.<a href="robocode/BorderSentry.html" title="interface in robocode"><span class="typeNameLink">BorderSentry</span></a></li>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Comparable</span></a>&lt;T&gt;
<ul>
<li type="circle">robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IScoreSnapshot</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="robocode/Droid.html" title="interface in robocode"><span class="typeNameLink">Droid</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IAdvancedEvents</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicEvents</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicEvents2</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicEvents3</span></a></li>
</ul>
</li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicEvents3</span></a></li>
</ul>
</li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicRobot</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IAdvancedRobot</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">ITeamRobot</span></a></li>
</ul>
</li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IInteractiveRobot</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IJuniorRobot</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IPaintRobot</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">ITeamRobot</span></a></li>
</ul>
</li>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IBasicRobotPeer</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IAdvancedRobotPeer</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">ITeamRobotPeer</span></a></li>
</ul>
</li>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IJuniorRobotPeer</span></a></li>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IStandardRobotPeer</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IAdvancedRobotPeer</span></a>
<ul>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">ITeamRobotPeer</span></a></li>
</ul>
</li>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">ITeamRobotPeer</span></a></li>
</ul>
</li>
<li type="circle">robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">ITeamRobotPeer</span></a></li>
</ul>
</li>
<li type="circle">robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><span class="typeNameLink">IBattleListener</span></a></li>
<li type="circle">robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IBulletSnapshot</span></a></li>
<li type="circle">robocode.control.snapshot.<a href="robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IDebugProperty</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IInteractiveEvents</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IPaintEvents</span></a></li>
<li type="circle">robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control"><span class="typeNameLink">IRobocodeEngine</span></a></li>
<li type="circle">robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IRobotSnapshot</span></a></li>
<li type="circle">robocode.robotinterfaces.<a href="robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">ITeamEvents</span></a></li>
<li type="circle">robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">ITurnSnapshot</span></a></li>
<li type="circle">robocode.control.<a href="robocode/control/RobocodeListener.html" title="interface in robocode.control"><span class="typeNameLink">RobocodeListener</span></a></li>
</ul>
<h2 title="Annotation Type Hierarchy">Annotation Type Hierarchy</h2>
<ul>
<li type="circle">robocode.annotation.<a href="robocode/annotation/SafeStatic.html" title="annotation in robocode.annotation"><span class="typeNameLink">SafeStatic</span></a> (implements java.lang.annotation.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/annotation/Annotation.html?is-external=true" title="class or interface in java.lang.annotation">Annotation</a>)</li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Enum</span></a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)
<ul>
<li type="circle">robocode.control.snapshot.<a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">BulletState</span></a></li>
<li type="circle">robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">RobotState</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-tree.html" target="_top">Frames</a></li>
<li><a href="overview-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
