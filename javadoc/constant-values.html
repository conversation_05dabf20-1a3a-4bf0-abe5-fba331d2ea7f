<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Constant Field Values (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Constant Field Values (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#robocode">robocode.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="robocode">
<!--   -->
</a>
<h2 title="robocode">robocode.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="robocode.BattleResults.serialVersionUID">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;long</code></td>
<td><code><a href="robocode/BattleResults.html#serialVersionUID">serialVersionUID</a></code></td>
<td class="colLast"><code>1L</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="robocode.JuniorRobot.black">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#black">black</a></code></td>
<td class="colLast"><code>0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.JuniorRobot.blue">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#blue">blue</a></code></td>
<td class="colLast"><code>255</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.JuniorRobot.brown">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#brown">brown</a></code></td>
<td class="colLast"><code>9127187</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.JuniorRobot.gray">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#gray">gray</a></code></td>
<td class="colLast"><code>8421504</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.JuniorRobot.green">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#green">green</a></code></td>
<td class="colLast"><code>32768</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.JuniorRobot.orange">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#orange">orange</a></code></td>
<td class="colLast"><code>16753920</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.JuniorRobot.purple">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#purple">purple</a></code></td>
<td class="colLast"><code>8388736</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.JuniorRobot.red">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#red">red</a></code></td>
<td class="colLast"><code>16711680</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.JuniorRobot.white">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#white">white</a></code></td>
<td class="colLast"><code>16777215</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.JuniorRobot.yellow">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="robocode/JuniorRobot.html#yellow">yellow</a></code></td>
<td class="colLast"><code>16776960</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="robocode.Rules.ACCELERATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#ACCELERATION">ACCELERATION</a></code></td>
<td class="colLast"><code>1.0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.Rules.DECELERATION">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#DECELERATION">DECELERATION</a></code></td>
<td class="colLast"><code>2.0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.Rules.GUN_TURN_RATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#GUN_TURN_RATE">GUN_TURN_RATE</a></code></td>
<td class="colLast"><code>20.0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.Rules.MAX_BULLET_POWER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#MAX_BULLET_POWER">MAX_BULLET_POWER</a></code></td>
<td class="colLast"><code>3.0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.Rules.MAX_TURN_RATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#MAX_TURN_RATE">MAX_TURN_RATE</a></code></td>
<td class="colLast"><code>10.0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.Rules.MAX_VELOCITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#MAX_VELOCITY">MAX_VELOCITY</a></code></td>
<td class="colLast"><code>8.0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.Rules.MIN_BULLET_POWER">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#MIN_BULLET_POWER">MIN_BULLET_POWER</a></code></td>
<td class="colLast"><code>0.1</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.Rules.RADAR_SCAN_RADIUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#RADAR_SCAN_RADIUS">RADAR_SCAN_RADIUS</a></code></td>
<td class="colLast"><code>1200.0</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.Rules.RADAR_TURN_RATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#RADAR_TURN_RATE">RADAR_TURN_RATE</a></code></td>
<td class="colLast"><code>45.0</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="robocode.Rules.ROBOT_HIT_BONUS">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#ROBOT_HIT_BONUS">ROBOT_HIT_BONUS</a></code></td>
<td class="colLast"><code>1.2</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="robocode.Rules.ROBOT_HIT_DAMAGE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/Rules.html#ROBOT_HIT_DAMAGE">ROBOT_HIT_DAMAGE</a></code></td>
<td class="colLast"><code>0.6</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="robocode.util">
<!--   -->
</a>
<h2 title="robocode.util">robocode.util.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="Constant Field Values table, listing constant fields, and values">
<caption><span>robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th scope="col">Constant Field</th>
<th class="colLast" scope="col">Value</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="robocode.util.Utils.NEAR_DELTA">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;double</code></td>
<td><code><a href="robocode/util/Utils.html#NEAR_DELTA">NEAR_DELTA</a></code></td>
<td class="colLast"><code>1.0E-5</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">Frames</a></li>
<li><a href="constant-values.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
