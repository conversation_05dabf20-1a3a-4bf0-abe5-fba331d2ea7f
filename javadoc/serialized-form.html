<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Serialized Form (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Serialized Form (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<div class="serializedFormContainer">
<ul class="blockList">
<li class="blockList">
<h2 title="Package">Package&nbsp;robocode</h2>
<ul class="blockList">
<li class="blockList"><a name="robocode.BattleEndedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/BattleEndedEvent.html" title="class in robocode">robocode.BattleEndedEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>aborted</h4>
<pre>boolean aborted</pre>
</li>
<li class="blockListLast">
<h4>results</h4>
<pre><a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a> results</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.BattleResults">
<!--   -->
</a>
<h3>Class <a href="robocode/BattleResults.html" title="class in robocode">robocode.BattleResults</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>teamLeaderName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> teamLeaderName</pre>
</li>
<li class="blockList">
<h4>rank</h4>
<pre>int rank</pre>
</li>
<li class="blockList">
<h4>score</h4>
<pre>double score</pre>
</li>
<li class="blockList">
<h4>survival</h4>
<pre>double survival</pre>
</li>
<li class="blockList">
<h4>lastSurvivorBonus</h4>
<pre>double lastSurvivorBonus</pre>
</li>
<li class="blockList">
<h4>bulletDamage</h4>
<pre>double bulletDamage</pre>
</li>
<li class="blockList">
<h4>bulletDamageBonus</h4>
<pre>double bulletDamageBonus</pre>
</li>
<li class="blockList">
<h4>ramDamage</h4>
<pre>double ramDamage</pre>
</li>
<li class="blockList">
<h4>ramDamageBonus</h4>
<pre>double ramDamageBonus</pre>
</li>
<li class="blockList">
<h4>firsts</h4>
<pre>int firsts</pre>
</li>
<li class="blockList">
<h4>seconds</h4>
<pre>int seconds</pre>
</li>
<li class="blockListLast">
<h4>thirds</h4>
<pre>int thirds</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.BattleRules">
<!--   -->
</a>
<h3>Class <a href="robocode/BattleRules.html" title="class in robocode">robocode.BattleRules</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>battlefieldWidth</h4>
<pre>int battlefieldWidth</pre>
</li>
<li class="blockList">
<h4>battlefieldHeight</h4>
<pre>int battlefieldHeight</pre>
</li>
<li class="blockList">
<h4>numRounds</h4>
<pre>int numRounds</pre>
</li>
<li class="blockList">
<h4>gunCoolingRate</h4>
<pre>double gunCoolingRate</pre>
</li>
<li class="blockList">
<h4>inactivityTime</h4>
<pre>long inactivityTime</pre>
</li>
<li class="blockList">
<h4>hideEnemyNames</h4>
<pre>boolean hideEnemyNames</pre>
</li>
<li class="blockListLast">
<h4>sentryBorderSize</h4>
<pre>int sentryBorderSize</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.Bullet">
<!--   -->
</a>
<h3>Class <a href="robocode/Bullet.html" title="class in robocode">robocode.Bullet</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>headingRadians</h4>
<pre>double headingRadians</pre>
</li>
<li class="blockList">
<h4>x</h4>
<pre>double x</pre>
</li>
<li class="blockList">
<h4>y</h4>
<pre>double y</pre>
</li>
<li class="blockList">
<h4>power</h4>
<pre>double power</pre>
</li>
<li class="blockList">
<h4>ownerName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> ownerName</pre>
</li>
<li class="blockList">
<h4>victimName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> victimName</pre>
</li>
<li class="blockList">
<h4>isActive</h4>
<pre>boolean isActive</pre>
</li>
<li class="blockListLast">
<h4>bulletId</h4>
<pre>int bulletId</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.BulletHitBulletEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/BulletHitBulletEvent.html" title="class in robocode">robocode.BulletHitBulletEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>bullet</h4>
<pre><a href="robocode/Bullet.html" title="class in robocode">Bullet</a> bullet</pre>
</li>
<li class="blockListLast">
<h4>hitBullet</h4>
<pre><a href="robocode/Bullet.html" title="class in robocode">Bullet</a> hitBullet</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.BulletHitEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/BulletHitEvent.html" title="class in robocode">robocode.BulletHitEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
<li class="blockList">
<h4>energy</h4>
<pre>double energy</pre>
</li>
<li class="blockListLast">
<h4>bullet</h4>
<pre><a href="robocode/Bullet.html" title="class in robocode">Bullet</a> bullet</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.BulletMissedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/BulletMissedEvent.html" title="class in robocode">robocode.BulletMissedEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>bullet</h4>
<pre><a href="robocode/Bullet.html" title="class in robocode">Bullet</a> bullet</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.CustomEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/CustomEvent.html" title="class in robocode">robocode.CustomEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.DeathEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/DeathEvent.html" title="class in robocode">robocode.DeathEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.Event">
<!--   -->
</a>
<h3>Class <a href="robocode/Event.html" title="class in robocode">robocode.Event</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>time</h4>
<pre>long time</pre>
</li>
<li class="blockListLast">
<h4>priority</h4>
<pre>int priority</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.HitByBulletEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/HitByBulletEvent.html" title="class in robocode">robocode.HitByBulletEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>bearing</h4>
<pre>double bearing</pre>
</li>
<li class="blockListLast">
<h4>bullet</h4>
<pre><a href="robocode/Bullet.html" title="class in robocode">Bullet</a> bullet</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.HitRobotEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/HitRobotEvent.html" title="class in robocode">robocode.HitRobotEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>robotName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> robotName</pre>
</li>
<li class="blockList">
<h4>bearing</h4>
<pre>double bearing</pre>
</li>
<li class="blockList">
<h4>energy</h4>
<pre>double energy</pre>
</li>
<li class="blockListLast">
<h4>atFault</h4>
<pre>boolean atFault</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.HitWallEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/HitWallEvent.html" title="class in robocode">robocode.HitWallEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>bearing</h4>
<pre>double bearing</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.KeyEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/KeyEvent.html" title="class in robocode">robocode.KeyEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>source</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a> source</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.KeyPressedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/KeyPressedEvent.html" title="class in robocode">robocode.KeyPressedEvent</a> extends <a href="robocode/KeyEvent.html" title="class in robocode">KeyEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.KeyReleasedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/KeyReleasedEvent.html" title="class in robocode">robocode.KeyReleasedEvent</a> extends <a href="robocode/KeyEvent.html" title="class in robocode">KeyEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.KeyTypedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/KeyTypedEvent.html" title="class in robocode">robocode.KeyTypedEvent</a> extends <a href="robocode/KeyEvent.html" title="class in robocode">KeyEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MessageEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MessageEvent.html" title="class in robocode">robocode.MessageEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>sender</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> sender</pre>
</li>
<li class="blockListLast">
<h4>message</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a> message</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.MouseClickedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseClickedEvent.html" title="class in robocode">robocode.MouseClickedEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MouseDraggedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseDraggedEvent.html" title="class in robocode">robocode.MouseDraggedEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MouseEnteredEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseEnteredEvent.html" title="class in robocode">robocode.MouseEnteredEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MouseEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseEvent.html" title="class in robocode">robocode.MouseEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>source</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a> source</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.MouseExitedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseExitedEvent.html" title="class in robocode">robocode.MouseExitedEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MouseMovedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseMovedEvent.html" title="class in robocode">robocode.MouseMovedEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MousePressedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MousePressedEvent.html" title="class in robocode">robocode.MousePressedEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MouseReleasedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseReleasedEvent.html" title="class in robocode">robocode.MouseReleasedEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.MouseWheelMovedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/MouseWheelMovedEvent.html" title="class in robocode">robocode.MouseWheelMovedEvent</a> extends <a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.PaintEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/PaintEvent.html" title="class in robocode">robocode.PaintEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
<li class="blockList"><a name="robocode.RobotDeathEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/RobotDeathEvent.html" title="class in robocode">robocode.RobotDeathEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>robotName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> robotName</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.RobotStatus">
<!--   -->
</a>
<h3>Class <a href="robocode/RobotStatus.html" title="class in robocode">robocode.RobotStatus</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>energy</h4>
<pre>double energy</pre>
</li>
<li class="blockList">
<h4>x</h4>
<pre>double x</pre>
</li>
<li class="blockList">
<h4>y</h4>
<pre>double y</pre>
</li>
<li class="blockList">
<h4>bodyHeading</h4>
<pre>double bodyHeading</pre>
</li>
<li class="blockList">
<h4>gunHeading</h4>
<pre>double gunHeading</pre>
</li>
<li class="blockList">
<h4>radarHeading</h4>
<pre>double radarHeading</pre>
</li>
<li class="blockList">
<h4>velocity</h4>
<pre>double velocity</pre>
</li>
<li class="blockList">
<h4>bodyTurnRemaining</h4>
<pre>double bodyTurnRemaining</pre>
</li>
<li class="blockList">
<h4>radarTurnRemaining</h4>
<pre>double radarTurnRemaining</pre>
</li>
<li class="blockList">
<h4>gunTurnRemaining</h4>
<pre>double gunTurnRemaining</pre>
</li>
<li class="blockList">
<h4>distanceRemaining</h4>
<pre>double distanceRemaining</pre>
</li>
<li class="blockList">
<h4>gunHeat</h4>
<pre>double gunHeat</pre>
</li>
<li class="blockList">
<h4>others</h4>
<pre>int others</pre>
</li>
<li class="blockList">
<h4>numSentries</h4>
<pre>int numSentries</pre>
</li>
<li class="blockList">
<h4>roundNum</h4>
<pre>int roundNum</pre>
</li>
<li class="blockList">
<h4>numRounds</h4>
<pre>int numRounds</pre>
</li>
<li class="blockListLast">
<h4>time</h4>
<pre>long time</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.RoundEndedEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/RoundEndedEvent.html" title="class in robocode">robocode.RoundEndedEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>round</h4>
<pre>int round</pre>
</li>
<li class="blockList">
<h4>turns</h4>
<pre>int turns</pre>
</li>
<li class="blockListLast">
<h4>totalTurns</h4>
<pre>int totalTurns</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.ScannedRobotEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/ScannedRobotEvent.html" title="class in robocode">robocode.ScannedRobotEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
<li class="blockList">
<h4>energy</h4>
<pre>double energy</pre>
</li>
<li class="blockList">
<h4>heading</h4>
<pre>double heading</pre>
</li>
<li class="blockList">
<h4>bearing</h4>
<pre>double bearing</pre>
</li>
<li class="blockList">
<h4>distance</h4>
<pre>double distance</pre>
</li>
<li class="blockList">
<h4>velocity</h4>
<pre>double velocity</pre>
</li>
<li class="blockListLast">
<h4>isSentryRobot</h4>
<pre>boolean isSentryRobot</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.SkippedTurnEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/SkippedTurnEvent.html" title="class in robocode">robocode.SkippedTurnEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>skippedTurn</h4>
<pre>long skippedTurn</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.StatusEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/StatusEvent.html" title="class in robocode">robocode.StatusEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>status</h4>
<pre><a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a> status</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.WinEvent">
<!--   -->
</a>
<h3>Class <a href="robocode/WinEvent.html" title="class in robocode">robocode.WinEvent</a> extends <a href="robocode/Event.html" title="class in robocode">Event</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="Package">Package&nbsp;robocode.control</h2>
<ul class="blockList">
<li class="blockList"><a name="robocode.control.BattlefieldSpecification">
<!--   -->
</a>
<h3>Class <a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control">robocode.control.BattlefieldSpecification</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>width</h4>
<pre>int width</pre>
</li>
<li class="blockListLast">
<h4>height</h4>
<pre>int height</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.control.BattleSpecification">
<!--   -->
</a>
<h3>Class <a href="robocode/control/BattleSpecification.html" title="class in robocode.control">robocode.control.BattleSpecification</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>battlefieldWidth</h4>
<pre>int battlefieldWidth</pre>
</li>
<li class="blockList">
<h4>battlefieldHeight</h4>
<pre>int battlefieldHeight</pre>
</li>
<li class="blockList">
<h4>numRounds</h4>
<pre>int numRounds</pre>
</li>
<li class="blockList">
<h4>gunCoolingRate</h4>
<pre>double gunCoolingRate</pre>
</li>
<li class="blockList">
<h4>inactivityTime</h4>
<pre>long inactivityTime</pre>
</li>
<li class="blockList">
<h4>hideEnemyNames</h4>
<pre>boolean hideEnemyNames</pre>
</li>
<li class="blockList">
<h4>sentryBorderSize</h4>
<pre>int sentryBorderSize</pre>
</li>
<li class="blockList">
<h4>robots</h4>
<pre><a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[] robots</pre>
</li>
<li class="blockListLast">
<h4>initialSetups</h4>
<pre><a href="robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a>[] initialSetups</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.control.RobotResults">
<!--   -->
</a>
<h3>Class <a href="robocode/control/RobotResults.html" title="class in robocode.control">robocode.control.RobotResults</a> extends <a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>2L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>robot</h4>
<pre><a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a> robot</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.control.RobotSetup">
<!--   -->
</a>
<h3>Class <a href="robocode/control/RobotSetup.html" title="class in robocode.control">robocode.control.RobotSetup</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>x</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> x</pre>
</li>
<li class="blockList">
<h4>y</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> y</pre>
</li>
<li class="blockListLast">
<h4>heading</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> heading</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="robocode.control.RobotSpecification">
<!--   -->
</a>
<h3>Class <a href="robocode/control/RobotSpecification.html" title="class in robocode.control">robocode.control.RobotSpecification</a> extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> implements Serializable</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>Serialized Fields</h3>
<ul class="blockList">
<li class="blockList">
<h4>fileSpecification</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a> fileSpecification</pre>
</li>
<li class="blockList">
<h4>name</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
<li class="blockList">
<h4>author</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> author</pre>
</li>
<li class="blockList">
<h4>webpage</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> webpage</pre>
</li>
<li class="blockList">
<h4>version</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> version</pre>
</li>
<li class="blockList">
<h4>robocodeVersion</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> robocodeVersion</pre>
</li>
<li class="blockList">
<h4>jarFile</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> jarFile</pre>
</li>
<li class="blockList">
<h4>fullClassName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> fullClassName</pre>
</li>
<li class="blockList">
<h4>description</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> description</pre>
</li>
<li class="blockListLast">
<h4>teamId</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> teamId</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">Frames</a></li>
<li><a href="serialized-form.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
