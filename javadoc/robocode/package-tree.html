<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode Class Hierarchy (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="robocode Class Hierarchy (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li><a href="../robocode/annotation/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package robocode</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/_RobotBase.html" title="class in robocode"><span class="typeNameLink">_RobotBase</span></a> (implements robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>)
<ul>
<li type="circle">robocode.<a href="../robocode/_Robot.html" title="class in robocode"><span class="typeNameLink">_Robot</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/Robot.html" title="class in robocode"><span class="typeNameLink">Robot</span></a> (implements robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>, robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a>)
<ul>
<li type="circle">robocode.<a href="../robocode/_AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">_AdvancedRobot</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode"><span class="typeNameLink">_AdvancedRadiansRobot</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">AdvancedRobot</span></a> (implements robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a>, robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a>)
<ul>
<li type="circle">robocode.<a href="../robocode/TeamRobot.html" title="class in robocode"><span class="typeNameLink">TeamRobot</span></a> (implements robocode.robotinterfaces.<a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a>, robocode.robotinterfaces.<a href="../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a>)
<ul>
<li type="circle">robocode.<a href="../robocode/RateControlRobot.html" title="class in robocode"><span class="typeNameLink">RateControlRobot</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">robocode.<a href="../robocode/JuniorRobot.html" title="class in robocode"><span class="typeNameLink">JuniorRobot</span></a> (implements robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces">IJuniorRobot</a>)</li>
</ul>
</li>
<li type="circle">robocode.<a href="../robocode/BattleResults.html" title="class in robocode"><span class="typeNameLink">BattleResults</span></a> (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="../robocode/BattleRules.html" title="class in robocode"><span class="typeNameLink">BattleRules</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="../robocode/Bullet.html" title="class in robocode"><span class="typeNameLink">Bullet</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="../robocode/Condition.html" title="class in robocode"><span class="typeNameLink">Condition</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/GunTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">GunTurnCompleteCondition</span></a></li>
<li type="circle">robocode.<a href="../robocode/MoveCompleteCondition.html" title="class in robocode"><span class="typeNameLink">MoveCompleteCondition</span></a></li>
<li type="circle">robocode.<a href="../robocode/RadarTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">RadarTurnCompleteCondition</span></a></li>
<li type="circle">robocode.<a href="../robocode/TurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">TurnCompleteCondition</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="../robocode/Event.html" title="class in robocode"><span class="typeNameLink">Event</span></a> (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)
<ul>
<li type="circle">robocode.<a href="../robocode/BattleEndedEvent.html" title="class in robocode"><span class="typeNameLink">BattleEndedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><span class="typeNameLink">BulletHitBulletEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/BulletHitEvent.html" title="class in robocode"><span class="typeNameLink">BulletHitEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/BulletMissedEvent.html" title="class in robocode"><span class="typeNameLink">BulletMissedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/CustomEvent.html" title="class in robocode"><span class="typeNameLink">CustomEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/DeathEvent.html" title="class in robocode"><span class="typeNameLink">DeathEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/HitByBulletEvent.html" title="class in robocode"><span class="typeNameLink">HitByBulletEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/HitRobotEvent.html" title="class in robocode"><span class="typeNameLink">HitRobotEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/HitWallEvent.html" title="class in robocode"><span class="typeNameLink">HitWallEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/KeyEvent.html" title="class in robocode"><span class="typeNameLink">KeyEvent</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/KeyPressedEvent.html" title="class in robocode"><span class="typeNameLink">KeyPressedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/KeyReleasedEvent.html" title="class in robocode"><span class="typeNameLink">KeyReleasedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/KeyTypedEvent.html" title="class in robocode"><span class="typeNameLink">KeyTypedEvent</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="../robocode/MessageEvent.html" title="class in robocode"><span class="typeNameLink">MessageEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MouseEvent.html" title="class in robocode"><span class="typeNameLink">MouseEvent</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/MouseClickedEvent.html" title="class in robocode"><span class="typeNameLink">MouseClickedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MouseDraggedEvent.html" title="class in robocode"><span class="typeNameLink">MouseDraggedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MouseEnteredEvent.html" title="class in robocode"><span class="typeNameLink">MouseEnteredEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MouseExitedEvent.html" title="class in robocode"><span class="typeNameLink">MouseExitedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MouseMovedEvent.html" title="class in robocode"><span class="typeNameLink">MouseMovedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MousePressedEvent.html" title="class in robocode"><span class="typeNameLink">MousePressedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MouseReleasedEvent.html" title="class in robocode"><span class="typeNameLink">MouseReleasedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/MouseWheelMovedEvent.html" title="class in robocode"><span class="typeNameLink">MouseWheelMovedEvent</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="../robocode/PaintEvent.html" title="class in robocode"><span class="typeNameLink">PaintEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/RobotDeathEvent.html" title="class in robocode"><span class="typeNameLink">RobotDeathEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/RoundEndedEvent.html" title="class in robocode"><span class="typeNameLink">RoundEndedEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><span class="typeNameLink">ScannedRobotEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/SkippedTurnEvent.html" title="class in robocode"><span class="typeNameLink">SkippedTurnEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/StatusEvent.html" title="class in robocode"><span class="typeNameLink">StatusEvent</span></a></li>
<li type="circle">robocode.<a href="../robocode/WinEvent.html" title="class in robocode"><span class="typeNameLink">WinEvent</span></a></li>
</ul>
</li>
<li type="circle">java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io"><span class="typeNameLink">OutputStream</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</a>, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Flushable.html?is-external=true" title="class or interface in java.io">Flushable</a>)
<ul>
<li type="circle">robocode.<a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><span class="typeNameLink">RobocodeFileOutputStream</span></a></li>
</ul>
</li>
<li type="circle">robocode.<a href="../robocode/Robocode.html" title="class in robocode"><span class="typeNameLink">Robocode</span></a></li>
<li type="circle">robocode.<a href="../robocode/RobotStatus.html" title="class in robocode"><span class="typeNameLink">RobotStatus</span></a> (implements java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)</li>
<li type="circle">robocode.<a href="../robocode/Rules.html" title="class in robocode"><span class="typeNameLink">Rules</span></a></li>
<li type="circle">java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true" title="class or interface in java.io"><span class="typeNameLink">Writer</span></a> (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Appendable.html?is-external=true" title="class or interface in java.lang">Appendable</a>, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</a>, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Flushable.html?is-external=true" title="class or interface in java.io">Flushable</a>)
<ul>
<li type="circle">java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true" title="class or interface in java.io"><span class="typeNameLink">OutputStreamWriter</span></a>
<ul>
<li type="circle">robocode.<a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><span class="typeNameLink">RobocodeFileWriter</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">robocode.<a href="../robocode/BorderSentry.html" title="interface in robocode"><span class="typeNameLink">BorderSentry</span></a></li>
<li type="circle">robocode.<a href="../robocode/Droid.html" title="interface in robocode"><span class="typeNameLink">Droid</span></a></li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li><a href="../robocode/annotation/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
