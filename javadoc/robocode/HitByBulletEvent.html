<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HitByBulletEvent (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HitByBulletEvent (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":42,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/GunTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/HitRobotEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/HitByBulletEvent.html" target="_top">Frames</a></li>
<li><a href="HitByBulletEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class HitByBulletEvent" class="title">Class HitByBulletEvent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/Event.html" title="class in robocode">robocode.Event</a></li>
<li>
<ul class="inheritance">
<li>robocode.HitByBulletEvent</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/Event.html" title="class in robocode">Event</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">HitByBulletEvent</span>
extends <a href="../robocode/Event.html" title="class in robocode">Event</a></pre>
<div class="block">A HitByBulletEvent is sent to <a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-"><code>onHitByBullet()</code></a> when your robot has been hit by a bullet.
 You can use the information contained in this event to determine what to do.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../serialized-form.html#robocode.HitByBulletEvent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#HitByBulletEvent-double-robocode.Bullet-">HitByBulletEvent</a></span>(double&nbsp;bearing,
                <a href="../robocode/Bullet.html" title="class in robocode">Bullet</a>&nbsp;bullet)</code>
<div class="block">Called by the game to create a new HitByBulletEvent.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getBearing--">getBearing</a></span>()</code>
<div class="block">Returns the bearing to the bullet, relative to your robot's heading,
 in degrees (-180 &lt; getBearing() &lt;= 180).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getBearingRadians--">getBearingRadians</a></span>()</code>
<div class="block">Returns the bearing to the bullet, relative to your robot's heading,
 in radians (-Math.PI &lt; getBearingRadians() &lt;= Math.PI).</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../robocode/Bullet.html" title="class in robocode">Bullet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getBullet--">getBullet</a></span>()</code>
<div class="block">Returns the bullet that hit your robot.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getHeading--">getHeading</a></span>()</code>
<div class="block">Returns the heading of the bullet when it hit you, in degrees
 (0 &lt;= getHeading() &lt; 360).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getHeadingDegrees--">getHeadingDegrees</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use <a href="../robocode/HitByBulletEvent.html#getHeading--"><code>getHeading()</code></a> instead.</span></div>
</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getHeadingRadians--">getHeadingRadians</a></span>()</code>
<div class="block">Returns the heading of the bullet when it hit you, in radians
 (0 &lt;= getHeadingRadians() &lt; 2 * PI).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of the robot that fired the bullet.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getPower--">getPower</a></span>()</code>
<div class="block">Returns the power of this bullet.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitByBulletEvent.html#getVelocity--">getVelocity</a></span>()</code>
<div class="block">Returns the velocity of this bullet.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.Event">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/Event.html" title="class in robocode">Event</a></h3>
<code><a href="../robocode/Event.html#compareTo-robocode.Event-">compareTo</a>, <a href="../robocode/Event.html#getPriority--">getPriority</a>, <a href="../robocode/Event.html#getTime--">getTime</a>, <a href="../robocode/Event.html#setPriority-int-">setPriority</a>, <a href="../robocode/Event.html#setTime-long-">setTime</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HitByBulletEvent-double-robocode.Bullet-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HitByBulletEvent</h4>
<pre>public&nbsp;HitByBulletEvent(double&nbsp;bearing,
                        <a href="../robocode/Bullet.html" title="class in robocode">Bullet</a>&nbsp;bullet)</pre>
<div class="block">Called by the game to create a new HitByBulletEvent.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bearing</code> - the bearing of the bullet that hit your robot, in radians</dd>
<dd><code>bullet</code> - the bullet that has hit your robot</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBearing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBearing</h4>
<pre>public&nbsp;double&nbsp;getBearing()</pre>
<div class="block">Returns the bearing to the bullet, relative to your robot's heading,
 in degrees (-180 &lt; getBearing() &lt;= 180).
 <p>
 If you were to turnRight(event.getBearing()), you would be facing the
 direction the bullet came from. The calculation used here is:
 (bullet's heading in degrees + 180) - (your heading in degrees)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bearing to the bullet, in degrees</dd>
</dl>
</li>
</ul>
<a name="getBearingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBearingRadians</h4>
<pre>public&nbsp;double&nbsp;getBearingRadians()</pre>
<div class="block">Returns the bearing to the bullet, relative to your robot's heading,
 in radians (-Math.PI &lt; getBearingRadians() &lt;= Math.PI).
 <p>
 If you were to turnRightRadians(event.getBearingRadians()), you would be
 facing the direction the bullet came from. The calculation used here is:
 (bullet's heading in radians + Math.PI) - (your heading in radians)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bearing to the bullet, in radians</dd>
</dl>
</li>
</ul>
<a name="getBullet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBullet</h4>
<pre>public&nbsp;<a href="../robocode/Bullet.html" title="class in robocode">Bullet</a>&nbsp;getBullet()</pre>
<div class="block">Returns the bullet that hit your robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bullet that hit your robot</dd>
</dl>
</li>
</ul>
<a name="getHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeading</h4>
<pre>public&nbsp;double&nbsp;getHeading()</pre>
<div class="block">Returns the heading of the bullet when it hit you, in degrees
 (0 &lt;= getHeading() &lt; 360).
 <p>
 Note: This is not relative to the direction you are facing. The robot
 that fired the bullet was in the opposite direction of getHeading() when
 it fired the bullet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the heading of the bullet, in degrees</dd>
</dl>
</li>
</ul>
<a name="getHeadingDegrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeadingDegrees</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;double&nbsp;getHeadingDegrees()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use <a href="../robocode/HitByBulletEvent.html#getHeading--"><code>getHeading()</code></a> instead.</span></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the heading of the bullet, in degrees</dd>
</dl>
</li>
</ul>
<a name="getHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getHeadingRadians()</pre>
<div class="block">Returns the heading of the bullet when it hit you, in radians
 (0 &lt;= getHeadingRadians() &lt; 2 * PI).
 <p>
 Note: This is not relative to the direction you are facing. The robot
 that fired the bullet was in the opposite direction of
 getHeadingRadians() when it fired the bullet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the heading of the bullet, in radians</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Returns the name of the robot that fired the bullet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the robot that fired the bullet</dd>
</dl>
</li>
</ul>
<a name="getPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPower</h4>
<pre>public&nbsp;double&nbsp;getPower()</pre>
<div class="block">Returns the power of this bullet. The damage you take (in fact, already
 took) is 4 * power, plus 2 * (power-1) if power &gt; 1. The robot that fired
 the bullet receives 3 * power back.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the power of the bullet</dd>
</dl>
</li>
</ul>
<a name="getVelocity--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getVelocity</h4>
<pre>public&nbsp;double&nbsp;getVelocity()</pre>
<div class="block">Returns the velocity of this bullet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the velocity of the bullet</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/GunTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/HitRobotEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/HitByBulletEvent.html" target="_top">Frames</a></li>
<li><a href="HitByBulletEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
