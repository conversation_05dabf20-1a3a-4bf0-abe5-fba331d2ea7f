<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title><PERSON><PERSON>obot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="<PERSON>Robot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/HitWallEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/KeyEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/JuniorRobot.html" target="_top">Frames</a></li>
<li><a href="JuniorRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class JuniorRobot" class="title">Class JuniorRobot</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_RobotBase.html" title="class in robocode">robocode._RobotBase</a></li>
<li>
<ul class="inheritance">
<li>robocode.JuniorRobot</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, <a href="../robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces">IJuniorRobot</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">JuniorRobot</span>
extends <a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a>
implements <a href="../robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces">IJuniorRobot</a></pre>
<div class="block">This is the simplest robot type, which is simpler than the <a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a> and
 <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> classes. The JuniorRobot has a simplified model, in
 purpose of teaching programming skills to inexperienced in programming
 students. The simplified robot model will keep player from overwhelming of
 Robocode's rules, programming syntax and programming concept.
 <p>
 Instead of using getters and setters, public fields are provided for
 receiving information like the last scanned robot, the coordinate of the
 robot etc.
 <p>
 All methods on this class are blocking calls, i.e. they do not return before
 their action has been completed and will at least take one turn to execute.
 However, setting colors is executed immediately and does not cost a turn to
 perform.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Nutch Poovarawan from Cubic Creative (designer), Flemming N. Larsen (original), Pavel Savara (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>, 
<a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, 
<a href="../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>, 
<a href="../robocode/RateControlRobot.html" title="class in robocode"><code>RateControlRobot</code></a>, 
<a href="../robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>, 
<a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#black">black</a></span></code>
<div class="block">The color black (0x000000)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#blue">blue</a></span></code>
<div class="block">The color blue (0x0000FF)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#brown">brown</a></span></code>
<div class="block">The color brown (0x8B4513)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#energy">energy</a></span></code>
<div class="block">Current energy of this robot, where 100 means full energy and 0 means no energy (dead).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#fieldHeight">fieldHeight</a></span></code>
<div class="block">Contains the height of the battlefield.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#fieldWidth">fieldWidth</a></span></code>
<div class="block">Contains the width of the battlefield.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#gray">gray</a></span></code>
<div class="block">The color gray (0x808080)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#green">green</a></span></code>
<div class="block">The color green (0x008000)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#gunBearing">gunBearing</a></span></code>
<div class="block">Current gun heading angle of this robot compared to its body (in degrees).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#gunHeading">gunHeading</a></span></code>
<div class="block">Current gun heading angle of this robot (in degrees).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#gunReady">gunReady</a></span></code>
<div class="block">Flag specifying if the gun is ready to fire, i.e.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#heading">heading</a></span></code>
<div class="block">Current heading angle of this robot (in degrees).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#hitByBulletAngle">hitByBulletAngle</a></span></code>
<div class="block">Latest angle from where this robot was hit by a bullet (in degrees).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#hitByBulletBearing">hitByBulletBearing</a></span></code>
<div class="block">Latest angle from where this robot was hit by a bullet (in degrees)
 compared to the body of this robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#hitRobotAngle">hitRobotAngle</a></span></code>
<div class="block">Latest angle where this robot has hit another robot (in degrees).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#hitRobotBearing">hitRobotBearing</a></span></code>
<div class="block">Latest angle where this robot has hit another robot (in degrees)
 compared to the body of this robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#hitWallAngle">hitWallAngle</a></span></code>
<div class="block">Latest angle where this robot has hit a wall (in degrees).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#hitWallBearing">hitWallBearing</a></span></code>
<div class="block">Latest angle where this robot has hit a wall (in degrees)
 compared to the body of this robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#orange">orange</a></span></code>
<div class="block">The color orange (0xFFA500)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#others">others</a></span></code>
<div class="block">Current number of other robots on the battle field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#purple">purple</a></span></code>
<div class="block">The color purple (0x800080)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#red">red</a></span></code>
<div class="block">The color red  (0xFF0000)</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#robotX">robotX</a></span></code>
<div class="block">Current horizontal location of this robot (in pixels).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#robotY">robotY</a></span></code>
<div class="block">Current vertical location of this robot (in pixels).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#scannedAngle">scannedAngle</a></span></code>
<div class="block">Current angle to the scanned nearest other robot (in degrees).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#scannedBearing">scannedBearing</a></span></code>
<div class="block">Current angle to the scanned nearest other robot (in degrees) compared to
 the body of this robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#scannedDistance">scannedDistance</a></span></code>
<div class="block">Current distance to the scanned nearest other robot (in pixels).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#scannedEnergy">scannedEnergy</a></span></code>
<div class="block">Current energy of scanned nearest other robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#scannedHeading">scannedHeading</a></span></code>
<div class="block">Current heading of the scanned nearest other robot (in degrees).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#scannedVelocity">scannedVelocity</a></span></code>
<div class="block">Current velocity of the scanned nearest other robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#white">white</a></span></code>
<div class="block">The color white (0xFFFFFF)</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#yellow">yellow</a></span></code>
<div class="block">The color yellow (0xFFFF00)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#out">out</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#JuniorRobot--">JuniorRobot</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#ahead-int-">ahead</a></span>(int&nbsp;distance)</code>
<div class="block">Moves this robot forward by pixels.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#back-int-">back</a></span>(int&nbsp;distance)</code>
<div class="block">Moves this robot backward by pixels.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#bearGunTo-int-">bearGunTo</a></span>(int&nbsp;angle)</code>
<div class="block">Turns the gun to the specified angle (in degrees) relative to body of this robot.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#doNothing--">doNothing</a></span>()</code>
<div class="block">Skips a turn.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#doNothing-int-">doNothing</a></span>(int&nbsp;turns)</code>
<div class="block">Skips the specified number of turns.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#fire--">fire</a></span>()</code>
<div class="block">Fires a bullet with the default power of 1.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#fire-double-">fire</a></span>(double&nbsp;power)</code>
<div class="block">Fires a bullet with the specified bullet power, which is between 0.1 and 3
 where 3 is the maximum bullet power.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#getBasicEventListener--">getBasicEventListener</a></span>()</code>
<div class="block">Do not call this method!</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#getRobotRunnable--">getRobotRunnable</a></span>()</code>
<div class="block">Do not call this method!</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#onHitByBullet--">onHitByBullet</a></span>()</code>
<div class="block">This event methods is called from the game when this robot has been hit
 by another robot's bullet.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#onHitRobot--">onHitRobot</a></span>()</code>
<div class="block">This event methods is called from the game when a bullet from this robot
 has hit another robot.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#onHitWall--">onHitWall</a></span>()</code>
<div class="block">This event methods is called from the game when this robot has hit a wall.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#onScannedRobot--">onScannedRobot</a></span>()</code>
<div class="block">This event method is called from the game when the radar detects another
 robot.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#run--">run</a></span>()</code>
<div class="block">The main method in every robot.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#setColors-int-int-int-">setColors</a></span>(int&nbsp;bodyColor,
         int&nbsp;gunColor,
         int&nbsp;radarColor)</code>
<div class="block">Sets the colors of the robot.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#setColors-int-int-int-int-int-">setColors</a></span>(int&nbsp;bodyColor,
         int&nbsp;gunColor,
         int&nbsp;radarColor,
         int&nbsp;bulletColor,
         int&nbsp;scanArcColor)</code>
<div class="block">Sets the colors of the robot.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-">turnAheadLeft</a></span>(int&nbsp;distance,
             int&nbsp;degrees)</code>
<div class="block">Moves this robot forward by pixels and turns this robot left by degrees
 at the same time.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-">turnAheadRight</a></span>(int&nbsp;distance,
              int&nbsp;degrees)</code>
<div class="block">Moves this robot forward by pixels and turns this robot right by degrees
 at the same time.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-">turnBackLeft</a></span>(int&nbsp;distance,
            int&nbsp;degrees)</code>
<div class="block">Moves this robot backward by pixels and turns this robot left by degrees
 at the same time.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnBackRight-int-int-">turnBackRight</a></span>(int&nbsp;distance,
             int&nbsp;degrees)</code>
<div class="block">Moves this robot backward by pixels and turns this robot right by degrees
 at the same time.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnGunLeft-int-">turnGunLeft</a></span>(int&nbsp;degrees)</code>
<div class="block">Turns the gun left by degrees.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnGunRight-int-">turnGunRight</a></span>(int&nbsp;degrees)</code>
<div class="block">Turns the gun right by degrees.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnGunTo-int-">turnGunTo</a></span>(int&nbsp;angle)</code>
<div class="block">Turns the gun to the specified angle (in degrees).</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnLeft-int-">turnLeft</a></span>(int&nbsp;degrees)</code>
<div class="block">Turns this robot left by degrees.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnRight-int-">turnRight</a></span>(int&nbsp;degrees)</code>
<div class="block">Turns this robot right by degrees.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/JuniorRobot.html#turnTo-int-">turnTo</a></span>(int&nbsp;angle)</code>
<div class="block">Turns this robot to the specified angle (in degrees).</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#finalize--">finalize</a>, <a href="../robocode/_RobotBase.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/_RobotBase.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a>, <a href="../robocode/_RobotBase.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IBasicRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="black">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>black</h4>
<pre>public static final&nbsp;int black</pre>
<div class="block">The color black (0x000000)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.black">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="white">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>white</h4>
<pre>public static final&nbsp;int white</pre>
<div class="block">The color white (0xFFFFFF)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.white">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="red">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>red</h4>
<pre>public static final&nbsp;int red</pre>
<div class="block">The color red  (0xFF0000)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.red">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="orange">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>orange</h4>
<pre>public static final&nbsp;int orange</pre>
<div class="block">The color orange (0xFFA500)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.orange">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="yellow">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>yellow</h4>
<pre>public static final&nbsp;int yellow</pre>
<div class="block">The color yellow (0xFFFF00)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.yellow">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="green">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>green</h4>
<pre>public static final&nbsp;int green</pre>
<div class="block">The color green (0x008000)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.green">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="blue">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>blue</h4>
<pre>public static final&nbsp;int blue</pre>
<div class="block">The color blue (0x0000FF)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.blue">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="purple">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>purple</h4>
<pre>public static final&nbsp;int purple</pre>
<div class="block">The color purple (0x800080)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.purple">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="brown">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>brown</h4>
<pre>public static final&nbsp;int brown</pre>
<div class="block">The color brown (0x8B4513)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.brown">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="gray">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gray</h4>
<pre>public static final&nbsp;int gray</pre>
<div class="block">The color gray (0x808080)</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.JuniorRobot.gray">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="fieldWidth">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fieldWidth</h4>
<pre>public&nbsp;int fieldWidth</pre>
<div class="block">Contains the width of the battlefield.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#fieldWidth"><code>fieldWidth</code></a></dd>
</dl>
</li>
</ul>
<a name="fieldHeight">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fieldHeight</h4>
<pre>public&nbsp;int fieldHeight</pre>
<div class="block">Contains the height of the battlefield.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#fieldWidth"><code>fieldWidth</code></a></dd>
</dl>
</li>
</ul>
<a name="others">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>others</h4>
<pre>public&nbsp;int others</pre>
<div class="block">Current number of other robots on the battle field.</div>
</li>
</ul>
<a name="energy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>energy</h4>
<pre>public&nbsp;int energy</pre>
<div class="block">Current energy of this robot, where 100 means full energy and 0 means no energy (dead).</div>
</li>
</ul>
<a name="robotX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>robotX</h4>
<pre>public&nbsp;int robotX</pre>
<div class="block">Current horizontal location of this robot (in pixels).</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#robotY"><code>robotY</code></a></dd>
</dl>
</li>
</ul>
<a name="robotY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>robotY</h4>
<pre>public&nbsp;int robotY</pre>
<div class="block">Current vertical location of this robot (in pixels).</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#robotX"><code>robotX</code></a></dd>
</dl>
</li>
</ul>
<a name="heading">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>heading</h4>
<pre>public&nbsp;int heading</pre>
<div class="block">Current heading angle of this robot (in degrees).</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#turnLeft-int-"><code>turnLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnRight-int-"><code>turnRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnTo-int-"><code>turnTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-"><code>turnAheadLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-"><code>turnAheadRight(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-"><code>turnBackLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackRight-int-int-"><code>turnBackRight(int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="gunHeading">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gunHeading</h4>
<pre>public&nbsp;int gunHeading</pre>
<div class="block">Current gun heading angle of this robot (in degrees).</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunBearing"><code>gunBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunLeft-int-"><code>turnGunLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunRight-int-"><code>turnGunRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunTo-int-"><code>turnGunTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#bearGunTo-int-"><code>bearGunTo(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="gunBearing">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gunBearing</h4>
<pre>public&nbsp;int gunBearing</pre>
<div class="block">Current gun heading angle of this robot compared to its body (in degrees).</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunHeading"><code>gunHeading</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunLeft-int-"><code>turnGunLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunRight-int-"><code>turnGunRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunTo-int-"><code>turnGunTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#bearGunTo-int-"><code>bearGunTo(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="gunReady">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gunReady</h4>
<pre>public&nbsp;boolean gunReady</pre>
<div class="block">Flag specifying if the gun is ready to fire, i.e. gun heat &lt;= 0.
 <code>true</code> means that the gun is able to fire; <code>false</code>
 means that the gun cannot fire yet as it still needs to cool down.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#fire--"><code>fire()</code></a>, 
<a href="../robocode/JuniorRobot.html#fire-double-"><code>fire(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="scannedDistance">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scannedDistance</h4>
<pre>public&nbsp;int scannedDistance</pre>
<div class="block">Current distance to the scanned nearest other robot (in pixels).
 If there is no robot in the radar's sight, this field will be less than 0, i.e -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedAngle"><code>scannedAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedBearing"><code>scannedBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedEnergy"><code>scannedEnergy</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedVelocity"><code>scannedVelocity</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedHeading"><code>scannedHeading</code></a></dd>
</dl>
</li>
</ul>
<a name="scannedAngle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scannedAngle</h4>
<pre>public&nbsp;int scannedAngle</pre>
<div class="block">Current angle to the scanned nearest other robot (in degrees).
 If there is no robot in the radar's sight, this field will be less than 0, i.e -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedDistance"><code>scannedDistance</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedBearing"><code>scannedBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedEnergy"><code>scannedEnergy</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedVelocity"><code>scannedVelocity</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedHeading"><code>scannedHeading</code></a></dd>
</dl>
</li>
</ul>
<a name="scannedBearing">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scannedBearing</h4>
<pre>public&nbsp;int scannedBearing</pre>
<div class="block">Current angle to the scanned nearest other robot (in degrees) compared to
 the body of this robot.
 If there is no robot in the radar's sight, this field will be less than 0, i.e -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedDistance"><code>scannedDistance</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedAngle"><code>scannedAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedEnergy"><code>scannedEnergy</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedVelocity"><code>scannedVelocity</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedHeading"><code>scannedHeading</code></a></dd>
</dl>
</li>
</ul>
<a name="scannedVelocity">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scannedVelocity</h4>
<pre>public&nbsp;int scannedVelocity</pre>
<div class="block">Current velocity of the scanned nearest other robot.
 If there is no robot in the radar's sight, this field will be -99.
 Note that a positive value means that the robot moves forward, a negative
 value means that the robot moved backward, and 0 means that the robot is
 not moving at all.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedDistance"><code>scannedDistance</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedAngle"><code>scannedAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedBearing"><code>scannedBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedEnergy"><code>scannedEnergy</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedHeading"><code>scannedHeading</code></a></dd>
</dl>
</li>
</ul>
<a name="scannedHeading">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scannedHeading</h4>
<pre>public&nbsp;int scannedHeading</pre>
<div class="block">Current heading of the scanned nearest other robot (in degrees).
 If there is no robot in the radar's sight, this field will be less than 0, i.e -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedDistance"><code>scannedDistance</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedAngle"><code>scannedAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedBearing"><code>scannedBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedEnergy"><code>scannedEnergy</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedVelocity"><code>scannedVelocity</code></a></dd>
</dl>
</li>
</ul>
<a name="scannedEnergy">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scannedEnergy</h4>
<pre>public&nbsp;int scannedEnergy</pre>
<div class="block">Current energy of scanned nearest other robot.
 If there is no robot in the radar's sight, this field will be less than 0, i.e -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onScannedRobot--"><code>onScannedRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedDistance"><code>scannedDistance</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedAngle"><code>scannedAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedBearing"><code>scannedBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedVelocity"><code>scannedVelocity</code></a></dd>
</dl>
</li>
</ul>
<a name="hitByBulletAngle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hitByBulletAngle</h4>
<pre>public&nbsp;int hitByBulletAngle</pre>
<div class="block">Latest angle from where this robot was hit by a bullet (in degrees).
 If the robot has never been hit, this field will be less than 0, i.e. -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onHitByBullet--"><code>onHitByBullet()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onHitByBullet--"><code>onHitByBullet()</code></a>, 
<a href="../robocode/JuniorRobot.html#hitByBulletBearing"><code>hitByBulletBearing</code></a></dd>
</dl>
</li>
</ul>
<a name="hitByBulletBearing">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hitByBulletBearing</h4>
<pre>public&nbsp;int hitByBulletBearing</pre>
<div class="block">Latest angle from where this robot was hit by a bullet (in degrees)
 compared to the body of this robot.
 If the robot has never been hit, this field will be less than 0, i.e. -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onHitByBullet--"><code>onHitByBullet()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onHitByBullet--"><code>onHitByBullet()</code></a>, 
<a href="../robocode/JuniorRobot.html#hitByBulletAngle"><code>hitByBulletAngle</code></a></dd>
</dl>
</li>
</ul>
<a name="hitRobotAngle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hitRobotAngle</h4>
<pre>public&nbsp;int hitRobotAngle</pre>
<div class="block">Latest angle where this robot has hit another robot (in degrees).
 If this robot has never hit another robot, this field will be less than 0, i.e. -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onHitRobot--"><code>onHitRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onHitRobot--"><code>onHitRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#hitRobotBearing"><code>hitRobotBearing</code></a></dd>
</dl>
</li>
</ul>
<a name="hitRobotBearing">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hitRobotBearing</h4>
<pre>public&nbsp;int hitRobotBearing</pre>
<div class="block">Latest angle where this robot has hit another robot (in degrees)
 compared to the body of this robot.
 If this robot has never hit another robot, this field will be less than 0, i.e. -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onHitRobot--"><code>onHitRobot()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onHitRobot--"><code>onHitRobot()</code></a>, 
<a href="../robocode/JuniorRobot.html#hitRobotAngle"><code>hitRobotAngle</code></a></dd>
</dl>
</li>
</ul>
<a name="hitWallAngle">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hitWallAngle</h4>
<pre>public&nbsp;int hitWallAngle</pre>
<div class="block">Latest angle where this robot has hit a wall (in degrees).
 If this robot has never hit a wall, this field will be less than 0, i.e. -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onHitWall--"><code>onHitWall()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onHitWall--"><code>onHitWall()</code></a>, 
<a href="../robocode/JuniorRobot.html#hitWallBearing"><code>hitWallBearing</code></a></dd>
</dl>
</li>
</ul>
<a name="hitWallBearing">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hitWallBearing</h4>
<pre>public&nbsp;int hitWallBearing</pre>
<div class="block">Latest angle where this robot has hit a wall (in degrees)
 compared to the body of this robot.
 If this robot has never hit a wall, this field will be less than 0, i.e. -1.
 This field will not be updated while <a href="../robocode/JuniorRobot.html#onHitWall--"><code>onHitWall()</code></a> event is active.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#onHitWall--"><code>onHitWall()</code></a>, 
<a href="../robocode/JuniorRobot.html#hitWallAngle"><code>hitWallAngle</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="JuniorRobot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>JuniorRobot</h4>
<pre>public&nbsp;JuniorRobot()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="ahead-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ahead</h4>
<pre>public&nbsp;void&nbsp;ahead(int&nbsp;distance)</pre>
<div class="block">Moves this robot forward by pixels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the amount of pixels to move forward</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#back-int-"><code>back(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#robotX"><code>robotX</code></a>, 
<a href="../robocode/JuniorRobot.html#robotY"><code>robotY</code></a></dd>
</dl>
</li>
</ul>
<a name="back-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>back</h4>
<pre>public&nbsp;void&nbsp;back(int&nbsp;distance)</pre>
<div class="block">Moves this robot backward by pixels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the amount of pixels to move backward</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#ahead-int-"><code>ahead(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#robotX"><code>robotX</code></a>, 
<a href="../robocode/JuniorRobot.html#robotY"><code>robotY</code></a></dd>
</dl>
</li>
</ul>
<a name="bearGunTo-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bearGunTo</h4>
<pre>public&nbsp;void&nbsp;bearGunTo(int&nbsp;angle)</pre>
<div class="block">Turns the gun to the specified angle (in degrees) relative to body of this robot.
 The gun will turn to the side with the shortest delta angle to the specified angle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to turn the gun to relative to the body of this robot</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunHeading"><code>gunHeading</code></a>, 
<a href="../robocode/JuniorRobot.html#gunBearing"><code>gunBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunLeft-int-"><code>turnGunLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunRight-int-"><code>turnGunRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunTo-int-"><code>turnGunTo(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="doNothing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>doNothing</h4>
<pre>public&nbsp;void&nbsp;doNothing()</pre>
<div class="block">Skips a turn.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#doNothing-int-"><code>doNothing(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="doNothing-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>doNothing</h4>
<pre>public&nbsp;void&nbsp;doNothing(int&nbsp;turns)</pre>
<div class="block">Skips the specified number of turns.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>turns</code> - the number of turns to skip</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#doNothing--"><code>doNothing()</code></a></dd>
</dl>
</li>
</ul>
<a name="fire--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fire</h4>
<pre>public&nbsp;void&nbsp;fire()</pre>
<div class="block">Fires a bullet with the default power of 1.
 If the gun heat is more than 0 and hence cannot fire, this method will
 suspend until the gun is ready to fire, and then fire a bullet.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunReady"><code>gunReady</code></a></dd>
</dl>
</li>
</ul>
<a name="fire-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fire</h4>
<pre>public&nbsp;void&nbsp;fire(double&nbsp;power)</pre>
<div class="block">Fires a bullet with the specified bullet power, which is between 0.1 and 3
 where 3 is the maximum bullet power.
 If the gun heat is more than 0 and hence cannot fire, this method will
 suspend until the gun is ready to fire, and then fire a bullet.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - between 0.1 and 3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunReady"><code>gunReady</code></a></dd>
</dl>
</li>
</ul>
<a name="getBasicEventListener--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBasicEventListener</h4>
<pre>public final&nbsp;<a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a>&nbsp;getBasicEventListener()</pre>
<div class="block">Do not call this method!
 <p>
 This method is called by the game to notify this robot about basic
 robot event. Hence, this method must be implemented so it returns your
 <a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><code>IBasicEvents</code></a> listener.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicRobot.html#getBasicEventListener--">getBasicEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>listener to basic events or <code>null</code> if this robot should
         not receive the notifications.</dd>
</dl>
</li>
</ul>
<a name="getRobotRunnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobotRunnable</h4>
<pre>public final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>&nbsp;getRobotRunnable()</pre>
<div class="block">Do not call this method!
 <p>
 This method is called by the game to invoke the
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang"><code>run()</code></a> method of your robot, where the program
 of your robot is implemented.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicRobot.html#getRobotRunnable--">getRobotRunnable</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a runnable implementation</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang"><code>Runnable.run()</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitByBullet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitByBullet</h4>
<pre>public&nbsp;void&nbsp;onHitByBullet()</pre>
<div class="block">This event methods is called from the game when this robot has been hit
 by another robot's bullet. When this event occurs the
 <a href="../robocode/JuniorRobot.html#hitByBulletAngle"><code>hitByBulletAngle</code></a> and <a href="../robocode/JuniorRobot.html#hitByBulletBearing"><code>hitByBulletBearing</code></a> fields values
 are automatically updated.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#hitByBulletAngle"><code>hitByBulletAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#hitByBulletBearing"><code>hitByBulletBearing</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitRobot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitRobot</h4>
<pre>public&nbsp;void&nbsp;onHitRobot()</pre>
<div class="block">This event methods is called from the game when a bullet from this robot
 has hit another robot. When this event occurs the <a href="../robocode/JuniorRobot.html#hitRobotAngle"><code>hitRobotAngle</code></a>
 and <a href="../robocode/JuniorRobot.html#hitRobotBearing"><code>hitRobotBearing</code></a> fields values are automatically updated.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#hitRobotAngle"><code>hitRobotAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#hitRobotBearing"><code>hitRobotBearing</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitWall--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitWall</h4>
<pre>public&nbsp;void&nbsp;onHitWall()</pre>
<div class="block">This event methods is called from the game when this robot has hit a wall.
 When this event occurs the <a href="../robocode/JuniorRobot.html#hitWallAngle"><code>hitWallAngle</code></a> and <a href="../robocode/JuniorRobot.html#hitWallBearing"><code>hitWallBearing</code></a>
 fields values are automatically updated.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#hitWallAngle"><code>hitWallAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#hitWallBearing"><code>hitWallBearing</code></a></dd>
</dl>
</li>
</ul>
<a name="onScannedRobot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onScannedRobot</h4>
<pre>public&nbsp;void&nbsp;onScannedRobot()</pre>
<div class="block">This event method is called from the game when the radar detects another
 robot. When this event occurs the <a href="../robocode/JuniorRobot.html#scannedDistance"><code>scannedDistance</code></a>,
 <a href="../robocode/JuniorRobot.html#scannedAngle"><code>scannedAngle</code></a>, <a href="../robocode/JuniorRobot.html#scannedBearing"><code>scannedBearing</code></a>, and <a href="../robocode/JuniorRobot.html#scannedEnergy"><code>scannedEnergy</code></a>
 field values are automatically updated.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#scannedDistance"><code>scannedDistance</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedAngle"><code>scannedAngle</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedBearing"><code>scannedBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#scannedEnergy"><code>scannedEnergy</code></a></dd>
</dl>
</li>
</ul>
<a name="run--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre>public&nbsp;void&nbsp;run()</pre>
<div class="block">The main method in every robot. You must override this to set up your
 robot's basic behavior.
 <p>
 Example:
 <pre>
   // A basic robot that moves around in a square
   public void run() {
       ahead(100);
       turnRight(90);
   }
 </pre>
 This method is automatically re-called when it has returned.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang">run</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a></code></dd>
</dl>
</li>
</ul>
<a name="setColors-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColors</h4>
<pre>public&nbsp;void&nbsp;setColors(int&nbsp;bodyColor,
                      int&nbsp;gunColor,
                      int&nbsp;radarColor)</pre>
<div class="block">Sets the colors of the robot. The color values are RGB values.
 You can use the colors that are already defined for this class.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bodyColor</code> - the RGB color value for the body</dd>
<dd><code>gunColor</code> - the RGB color value for the gun</dd>
<dd><code>radarColor</code> - the RGB color value for the radar</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#setColors-int-int-int-int-int-"><code>setColors(int, int, int, int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="setColors-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColors</h4>
<pre>public&nbsp;void&nbsp;setColors(int&nbsp;bodyColor,
                      int&nbsp;gunColor,
                      int&nbsp;radarColor,
                      int&nbsp;bulletColor,
                      int&nbsp;scanArcColor)</pre>
<div class="block">Sets the colors of the robot. The color values are RGB values.
 You can use the colors that are already defined for this class.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bodyColor</code> - the RGB color value for the body</dd>
<dd><code>gunColor</code> - the RGB color value for the gun</dd>
<dd><code>radarColor</code> - the RGB color value for the radar</dd>
<dd><code>bulletColor</code> - the RGB color value for the bullets</dd>
<dd><code>scanArcColor</code> - the RGB color value for the scan arc</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#setColors-int-int-int-"><code>setColors(int, int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnAheadLeft-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnAheadLeft</h4>
<pre>public&nbsp;void&nbsp;turnAheadLeft(int&nbsp;distance,
                          int&nbsp;degrees)</pre>
<div class="block">Moves this robot forward by pixels and turns this robot left by degrees
 at the same time. The robot will move in a curve that follows a perfect
 circle, and the moving and turning will end at the same time.
 <p>
 Note that the max. velocity and max. turn rate is automatically adjusted,
 which means that the robot will move slower the sharper the turn is
 compared to the distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the amount of pixels to move forward</dd>
<dd><code>degrees</code> - the amount of degrees to turn to the left</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#heading"><code>heading</code></a>, 
<a href="../robocode/JuniorRobot.html#robotX"><code>robotX</code></a>, 
<a href="../robocode/JuniorRobot.html#robotY"><code>robotY</code></a>, 
<a href="../robocode/JuniorRobot.html#turnLeft-int-"><code>turnLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnRight-int-"><code>turnRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnTo-int-"><code>turnTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-"><code>turnAheadRight(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-"><code>turnBackLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackRight-int-int-"><code>turnBackRight(int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnAheadRight-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnAheadRight</h4>
<pre>public&nbsp;void&nbsp;turnAheadRight(int&nbsp;distance,
                           int&nbsp;degrees)</pre>
<div class="block">Moves this robot forward by pixels and turns this robot right by degrees
 at the same time. The robot will move in a curve that follows a perfect
 circle, and the moving and turning will end at the same time.
 <p>
 Note that the max. velocity and max. turn rate is automatically adjusted,
 which means that the robot will move slower the sharper the turn is
 compared to the distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the amount of pixels to move forward</dd>
<dd><code>degrees</code> - the amount of degrees to turn to the right</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#heading"><code>heading</code></a>, 
<a href="../robocode/JuniorRobot.html#robotX"><code>robotX</code></a>, 
<a href="../robocode/JuniorRobot.html#robotY"><code>robotY</code></a>, 
<a href="../robocode/JuniorRobot.html#turnLeft-int-"><code>turnLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnRight-int-"><code>turnRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnTo-int-"><code>turnTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-"><code>turnAheadLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-"><code>turnBackLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackRight-int-int-"><code>turnBackRight(int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnBackLeft-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnBackLeft</h4>
<pre>public&nbsp;void&nbsp;turnBackLeft(int&nbsp;distance,
                         int&nbsp;degrees)</pre>
<div class="block">Moves this robot backward by pixels and turns this robot left by degrees
 at the same time. The robot will move in a curve that follows a perfect
 circle, and the moving and turning will end at the same time.
 <p>
 Note that the max. velocity and max. turn rate is automatically adjusted,
 which means that the robot will move slower the sharper the turn is
 compared to the distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the amount of pixels to move backward</dd>
<dd><code>degrees</code> - the amount of degrees to turn to the left</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#heading"><code>heading</code></a>, 
<a href="../robocode/JuniorRobot.html#robotX"><code>robotX</code></a>, 
<a href="../robocode/JuniorRobot.html#robotY"><code>robotY</code></a>, 
<a href="../robocode/JuniorRobot.html#turnLeft-int-"><code>turnLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnRight-int-"><code>turnRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnTo-int-"><code>turnTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-"><code>turnAheadLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-"><code>turnAheadRight(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackRight-int-int-"><code>turnBackRight(int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnBackRight-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnBackRight</h4>
<pre>public&nbsp;void&nbsp;turnBackRight(int&nbsp;distance,
                          int&nbsp;degrees)</pre>
<div class="block">Moves this robot backward by pixels and turns this robot right by degrees
 at the same time. The robot will move in a curve that follows a perfect
 circle, and the moving and turning will end at the same time.
 <p>
 Note that the max. velocity and max. turn rate is automatically adjusted,
 which means that the robot will move slower the sharper the turn is
 compared to the distance.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the amount of pixels to move backward</dd>
<dd><code>degrees</code> - the amount of degrees to turn to the right</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#heading"><code>heading</code></a>, 
<a href="../robocode/JuniorRobot.html#robotX"><code>robotX</code></a>, 
<a href="../robocode/JuniorRobot.html#robotY"><code>robotY</code></a>, 
<a href="../robocode/JuniorRobot.html#turnLeft-int-"><code>turnLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnRight-int-"><code>turnRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnTo-int-"><code>turnTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-"><code>turnAheadLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-"><code>turnAheadRight(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-"><code>turnBackLeft(int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGunLeft-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunLeft</h4>
<pre>public&nbsp;void&nbsp;turnGunLeft(int&nbsp;degrees)</pre>
<div class="block">Turns the gun left by degrees.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the gun to the left</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunHeading"><code>gunHeading</code></a>, 
<a href="../robocode/JuniorRobot.html#gunBearing"><code>gunBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunRight-int-"><code>turnGunRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunTo-int-"><code>turnGunTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#bearGunTo-int-"><code>bearGunTo(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGunRight-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunRight</h4>
<pre>public&nbsp;void&nbsp;turnGunRight(int&nbsp;degrees)</pre>
<div class="block">Turns the gun right by degrees.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the gun to the right</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunHeading"><code>gunHeading</code></a>, 
<a href="../robocode/JuniorRobot.html#gunBearing"><code>gunBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunLeft-int-"><code>turnGunLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunTo-int-"><code>turnGunTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#bearGunTo-int-"><code>bearGunTo(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGunTo-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunTo</h4>
<pre>public&nbsp;void&nbsp;turnGunTo(int&nbsp;angle)</pre>
<div class="block">Turns the gun to the specified angle (in degrees).
 The gun will turn to the side with the shortest delta angle to the
 specified angle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to turn the gun to</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#gunHeading"><code>gunHeading</code></a>, 
<a href="../robocode/JuniorRobot.html#gunBearing"><code>gunBearing</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunLeft-int-"><code>turnGunLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnGunRight-int-"><code>turnGunRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#bearGunTo-int-"><code>bearGunTo(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnLeft-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnLeft</h4>
<pre>public&nbsp;void&nbsp;turnLeft(int&nbsp;degrees)</pre>
<div class="block">Turns this robot left by degrees.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn to the left</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#heading"><code>heading</code></a>, 
<a href="../robocode/JuniorRobot.html#turnRight-int-"><code>turnRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnTo-int-"><code>turnTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-"><code>turnAheadLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-"><code>turnAheadRight(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-"><code>turnBackLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackRight-int-int-"><code>turnBackRight(int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRight-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRight</h4>
<pre>public&nbsp;void&nbsp;turnRight(int&nbsp;degrees)</pre>
<div class="block">Turns this robot right by degrees.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn to the right</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#heading"><code>heading</code></a>, 
<a href="../robocode/JuniorRobot.html#turnLeft-int-"><code>turnLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnTo-int-"><code>turnTo(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-"><code>turnAheadLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-"><code>turnAheadRight(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-"><code>turnBackLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackRight-int-int-"><code>turnBackRight(int, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnTo-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>turnTo</h4>
<pre>public&nbsp;void&nbsp;turnTo(int&nbsp;angle)</pre>
<div class="block">Turns this robot to the specified angle (in degrees).
 The robot will turn to the side with the shortest delta angle to the
 specified angle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to turn this robot to</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html#heading"><code>heading</code></a>, 
<a href="../robocode/JuniorRobot.html#turnLeft-int-"><code>turnLeft(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnRight-int-"><code>turnRight(int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadLeft-int-int-"><code>turnAheadLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnAheadRight-int-int-"><code>turnAheadRight(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackLeft-int-int-"><code>turnBackLeft(int, int)</code></a>, 
<a href="../robocode/JuniorRobot.html#turnBackRight-int-int-"><code>turnBackRight(int, int)</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/HitWallEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/KeyEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/JuniorRobot.html" target="_top">Frames</a></li>
<li><a href="JuniorRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
