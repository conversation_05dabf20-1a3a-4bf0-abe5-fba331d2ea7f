<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RobotStatus (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RobotStatus (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RobotDeathEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/RoundEndedEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/RobotStatus.html" target="_top">Frames</a></li>
<li><a href="RobotStatus.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class RobotStatus" class="title">Class RobotStatus</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>robocode.RobotStatus</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">RobotStatus</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a></pre>
<div class="block">Contains the status of a robot for a specific time/turn returned by
 <a href="../robocode/StatusEvent.html#getStatus--"><code>StatusEvent.getStatus()</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.5</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Flemming N. Larsen (original)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../serialized-form.html#robocode.RobotStatus">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getDistanceRemaining--">getDistanceRemaining</a></span>()</code>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getEnergy--">getEnergy</a></span>()</code>
<div class="block">Returns the robot's current energy.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getGunHeading--">getGunHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's gun is facing, in degrees.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getGunHeadingRadians--">getGunHeadingRadians</a></span>()</code>
<div class="block">Returns the direction that the robot's gun is facing, in radians.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getGunHeat--">getGunHeat</a></span>()</code>
<div class="block">Returns the current heat of the gun.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getGunTurnRemaining--">getGunTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the gun's turn, in degrees.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians</a></span>()</code>
<div class="block">Returns the angle remaining in the gun's turn, in radians.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getHeading--">getHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's body is facing, in degrees.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getHeadingRadians--">getHeadingRadians</a></span>()</code>
<div class="block">Returns the direction that the robot's body is facing, in radians.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getNumRounds--">getNumRounds</a></span>()</code>
<div class="block">Returns the number of rounds in the current battle.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getNumSentries--">getNumSentries</a></span>()</code>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getOthers--">getOthers</a></span>()</code>
<div class="block">Returns how many opponents that are left in the current round.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getRadarHeading--">getRadarHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's radar is facing, in degrees.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getRadarHeadingRadians--">getRadarHeadingRadians</a></span>()</code>
<div class="block">Returns the direction that the robot's radar is facing, in radians.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getRadarTurnRemaining--">getRadarTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the radar's turn, in degrees.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians</a></span>()</code>
<div class="block">Returns the angle remaining in the radar's turn, in radians.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getRoundNum--">getRoundNum</a></span>()</code>
<div class="block">Returns the current round number (0 to <a href="../robocode/RobotStatus.html#getNumRounds--"><code>getNumRounds()</code></a> - 1) of
 the battle.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getTime--">getTime</a></span>()</code>
<div class="block">Returns the game time of the round, where the time is equal to the current turn in the round.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getTurnRemaining--">getTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the robots's turn, in degrees.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getTurnRemainingRadians--">getTurnRemainingRadians</a></span>()</code>
<div class="block">Returns the angle remaining in the robots's turn, in radians.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getVelocity--">getVelocity</a></span>()</code>
<div class="block">Returns the velocity of the robot measured in pixels/turn.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getX--">getX</a></span>()</code>
<div class="block">Returns the X position of the robot.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RobotStatus.html#getY--">getY</a></span>()</code>
<div class="block">Returns the Y position of the robot.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnergy</h4>
<pre>public&nbsp;double&nbsp;getEnergy()</pre>
<div class="block">Returns the robot's current energy.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot's current energy</dd>
</dl>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;double&nbsp;getX()</pre>
<div class="block">Returns the X position of the robot. (0,0) is at the bottom left of the
 battlefield.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the X position of the robot</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RobotStatus.html#getY--"><code>getY()</code></a></dd>
</dl>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;double&nbsp;getY()</pre>
<div class="block">Returns the Y position of the robot. (0,0) is at the bottom left of the
 battlefield.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Y position of the robot</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RobotStatus.html#getX--"><code>getX()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getHeadingRadians()</pre>
<div class="block">Returns the direction that the robot's body is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's body is facing, in radians.</dd>
</dl>
</li>
</ul>
<a name="getHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeading</h4>
<pre>public&nbsp;double&nbsp;getHeading()</pre>
<div class="block">Returns the direction that the robot's body is facing, in degrees.
 The value returned will be between 0 and 360 (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 90 means East, 180 means South, and 270 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's body is facing, in degrees.</dd>
</dl>
</li>
</ul>
<a name="getGunHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getGunHeadingRadians()</pre>
<div class="block">Returns the direction that the robot's gun is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's gun is facing, in radians.</dd>
</dl>
</li>
</ul>
<a name="getGunHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeading</h4>
<pre>public&nbsp;double&nbsp;getGunHeading()</pre>
<div class="block">Returns the direction that the robot's gun is facing, in degrees.
 The value returned will be between 0 and 360 (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 90 means East, 180 means South, and 270 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's gun is facing, in degrees.</dd>
</dl>
</li>
</ul>
<a name="getRadarHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getRadarHeadingRadians()</pre>
<div class="block">Returns the direction that the robot's radar is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's radar is facing, in radians.</dd>
</dl>
</li>
</ul>
<a name="getRadarHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarHeading</h4>
<pre>public&nbsp;double&nbsp;getRadarHeading()</pre>
<div class="block">Returns the direction that the robot's radar is facing, in degrees.
 The value returned will be between 0 and 360 (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 90 means East, 180 means South, and 270 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's radar is facing, in degrees.</dd>
</dl>
</li>
</ul>
<a name="getVelocity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVelocity</h4>
<pre>public&nbsp;double&nbsp;getVelocity()</pre>
<div class="block">Returns the velocity of the robot measured in pixels/turn.
 <p>
 The maximum velocity of a robot is defined by <a href="../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a>
 (8 pixels / turn).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the velocity of the robot measured in pixels/turn</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a></dd>
</dl>
</li>
</ul>
<a name="getTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getTurnRemainingRadians()</pre>
<div class="block">Returns the angle remaining in the robots's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently turning to the right. Negative values
 means that the robot is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the robots's turn, in radians</dd>
</dl>
</li>
</ul>
<a name="getTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRemaining</h4>
<pre>public&nbsp;double&nbsp;getTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the robots's turn, in degrees.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently turning to the right. Negative values
 means that the robot is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the robots's turn, in degrees</dd>
</dl>
</li>
</ul>
<a name="getRadarTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getRadarTurnRemainingRadians()</pre>
<div class="block">Returns the angle remaining in the radar's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the radar is currently turning to the right. Negative values
 means that the radar is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the radar's turn, in radians</dd>
</dl>
</li>
</ul>
<a name="getRadarTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarTurnRemaining</h4>
<pre>public&nbsp;double&nbsp;getRadarTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the radar's turn, in degrees.
 <p>
 This call returns both positive and negative values. Positive values
 means that the radar is currently turning to the right. Negative values
 means that the radar is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the radar's turn, in degrees</dd>
</dl>
</li>
</ul>
<a name="getGunTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getGunTurnRemainingRadians()</pre>
<div class="block">Returns the angle remaining in the gun's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the gun is currently turning to the right. Negative values
 means that the gun is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the gun's turn, in radians</dd>
</dl>
</li>
</ul>
<a name="getGunTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunTurnRemaining</h4>
<pre>public&nbsp;double&nbsp;getGunTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the gun's turn, in degrees.
 <p>
 This call returns both positive and negative values. Positive values
 means that the gun is currently turning to the right. Negative values
 means that the gun is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the gun's turn, in degrees</dd>
</dl>
</li>
</ul>
<a name="getDistanceRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistanceRemaining</h4>
<pre>public&nbsp;double&nbsp;getDistanceRemaining()</pre>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently moving forwards. Negative values means
 that the robot is currently moving backwards.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the distance remaining in the robot's current move measured in
         pixels.</dd>
</dl>
</li>
</ul>
<a name="getGunHeat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeat</h4>
<pre>public&nbsp;double&nbsp;getGunHeat()</pre>
<div class="block">Returns the current heat of the gun. The gun cannot fire unless this is
 0. (Calls to fire will succeed, but will not actually fire unless
 getGunHeat() == 0).
 <p>
 The amount of gun heat generated when the gun is fired is
 1 + (firePower / 5). Each turn the gun heat drops by the amount returned
 by <a href="../robocode/Robot.html#getGunCoolingRate--"><code>Robot.getGunCoolingRate()</code></a>, which is a battle setup.
 <p>
 Note that all guns are "hot" at the start of each round, where the gun
 heat is 3.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current gun heat</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getGunCoolingRate--"><code>Robot.getGunCoolingRate()</code></a>, 
<a href="../robocode/Robot.html#fire-double-"><code>Robot.fire(double)</code></a>, 
<a href="../robocode/Robot.html#fireBullet-double-"><code>Robot.fireBullet(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getOthers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOthers</h4>
<pre>public&nbsp;int&nbsp;getOthers()</pre>
<div class="block">Returns how many opponents that are left in the current round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>how many opponents that are left in the current round.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RobotStatus.html#getNumSentries--"><code>getNumSentries()</code></a></dd>
</dl>
</li>
</ul>
<a name="getNumSentries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumSentries</h4>
<pre>public&nbsp;int&nbsp;getNumSentries()</pre>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>how many sentry robots that are left in the current round.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.9.1.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RobotStatus.html#getOthers--"><code>getOthers()</code></a></dd>
</dl>
</li>
</ul>
<a name="getNumRounds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumRounds</h4>
<pre>public&nbsp;int&nbsp;getNumRounds()</pre>
<div class="block">Returns the number of rounds in the current battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds in the current battle</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RobotStatus.html#getRoundNum--"><code>getRoundNum()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRoundNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoundNum</h4>
<pre>public&nbsp;int&nbsp;getRoundNum()</pre>
<div class="block">Returns the current round number (0 to <a href="../robocode/RobotStatus.html#getNumRounds--"><code>getNumRounds()</code></a> - 1) of
 the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current round number of the battle (zero indexed).</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RobotStatus.html#getNumRounds--"><code>getNumRounds()</code></a></dd>
</dl>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTime</h4>
<pre>public&nbsp;long&nbsp;getTime()</pre>
<div class="block">Returns the game time of the round, where the time is equal to the current turn in the round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the game time/turn of the current round.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RobotDeathEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/RoundEndedEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/RobotStatus.html" target="_top">Frames</a></li>
<li><a href="RobotStatus.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
