<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>HitRobotEvent (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HitRobotEvent (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":42,"i3":10,"i4":10,"i5":10,"i6":42,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/HitByBulletEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/HitWallEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/HitRobotEvent.html" target="_top">Frames</a></li>
<li><a href="HitRobotEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class HitRobotEvent" class="title">Class HitRobotEvent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/Event.html" title="class in robocode">robocode.Event</a></li>
<li>
<ul class="inheritance">
<li>robocode.HitRobotEvent</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/Event.html" title="class in robocode">Event</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">HitRobotEvent</span>
extends <a href="../robocode/Event.html" title="class in robocode">Event</a></pre>
<div class="block">A HitRobotEvent is sent to <a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-"><code>onHitRobot()</code></a>
 when your robot collides with another robot.
 You can use the information contained in this event to determine what to do.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../serialized-form.html#robocode.HitRobotEvent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#HitRobotEvent-java.lang.String-double-double-boolean-">HitRobotEvent</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
             double&nbsp;bearing,
             double&nbsp;energy,
             boolean&nbsp;atFault)</code>
<div class="block">Called by the game to create a new HitRobotEvent.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#compareTo-robocode.Event-">compareTo</a></span>(<a href="../robocode/Event.html" title="class in robocode">Event</a>&nbsp;event)</code>
<div class="block">Compares this event to another event regarding precedence.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#getBearing--">getBearing</a></span>()</code>
<div class="block">Returns the bearing to the robot you hit, relative to your robot's
 heading, in degrees (-180 &lt;= getBearing() &lt; 180)</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#getBearingDegrees--">getBearingDegrees</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use <a href="../robocode/HitRobotEvent.html#getBearing--"><code>getBearing()</code></a> instead.</span></div>
</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#getBearingRadians--">getBearingRadians</a></span>()</code>
<div class="block">Returns the bearing to the robot you hit, relative to your robot's
 heading, in radians (-PI &lt;= getBearingRadians() &lt; PI)</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#getEnergy--">getEnergy</a></span>()</code>
<div class="block">Returns the amount of energy of the robot you hit.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of the robot you hit.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#getRobotName--">getRobotName</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use <a href="../robocode/HitRobotEvent.html#getName--"><code>getName()</code></a> instead.</span></div>
</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/HitRobotEvent.html#isMyFault--">isMyFault</a></span>()</code>
<div class="block">Checks if your robot was moving towards the robot that was hit.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.Event">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/Event.html" title="class in robocode">Event</a></h3>
<code><a href="../robocode/Event.html#getPriority--">getPriority</a>, <a href="../robocode/Event.html#getTime--">getTime</a>, <a href="../robocode/Event.html#setPriority-int-">setPriority</a>, <a href="../robocode/Event.html#setTime-long-">setTime</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HitRobotEvent-java.lang.String-double-double-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HitRobotEvent</h4>
<pre>public&nbsp;HitRobotEvent(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                     double&nbsp;bearing,
                     double&nbsp;energy,
                     boolean&nbsp;atFault)</pre>
<div class="block">Called by the game to create a new HitRobotEvent.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the robot you hit</dd>
<dd><code>bearing</code> - the bearing to the robot that your robot hit, in radians</dd>
<dd><code>energy</code> - the amount of energy of the robot you hit</dd>
<dd><code>atFault</code> - <code>true</code> if your robot was moving toward the other
                robot; <code>false</code> otherwise</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBearing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBearing</h4>
<pre>public&nbsp;double&nbsp;getBearing()</pre>
<div class="block">Returns the bearing to the robot you hit, relative to your robot's
 heading, in degrees (-180 &lt;= getBearing() &lt; 180)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bearing to the robot you hit, in degrees</dd>
</dl>
</li>
</ul>
<a name="getBearingDegrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBearingDegrees</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;double&nbsp;getBearingDegrees()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use <a href="../robocode/HitRobotEvent.html#getBearing--"><code>getBearing()</code></a> instead.</span></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bearing to the robot you hit, in degrees</dd>
</dl>
</li>
</ul>
<a name="getBearingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBearingRadians</h4>
<pre>public&nbsp;double&nbsp;getBearingRadians()</pre>
<div class="block">Returns the bearing to the robot you hit, relative to your robot's
 heading, in radians (-PI &lt;= getBearingRadians() &lt; PI)</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bearing to the robot you hit, in radians</dd>
</dl>
</li>
</ul>
<a name="getEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnergy</h4>
<pre>public&nbsp;double&nbsp;getEnergy()</pre>
<div class="block">Returns the amount of energy of the robot you hit.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the amount of energy of the robot you hit</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Returns the name of the robot you hit.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the robot you hit</dd>
</dl>
</li>
</ul>
<a name="getRobotName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobotName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRobotName()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use <a href="../robocode/HitRobotEvent.html#getName--"><code>getName()</code></a> instead.</span></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the robot you hit</dd>
</dl>
</li>
</ul>
<a name="isMyFault--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMyFault</h4>
<pre>public&nbsp;boolean&nbsp;isMyFault()</pre>
<div class="block">Checks if your robot was moving towards the robot that was hit.
 <p>
 If <a href="../robocode/HitRobotEvent.html#isMyFault--"><code>isMyFault()</code></a> returns <code>true</code> then your robot's movement
 (including turning) will have stopped and been marked complete.
 <p>
 Note: If two robots are moving toward each other and collide, they will
 each receive two HitRobotEvents. The first will be the one if
 <a href="../robocode/HitRobotEvent.html#isMyFault--"><code>isMyFault()</code></a> returns <code>true</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if your robot was moving towards the robot that was
         hit; <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="compareTo-robocode.Event-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>compareTo</h4>
<pre>public&nbsp;int&nbsp;compareTo(<a href="../robocode/Event.html" title="class in robocode">Event</a>&nbsp;event)</pre>
<div class="block">Compares this event to another event regarding precedence.
 The event precedence is first and foremost determined by the event time,
 secondly the event priority, and lastly specific event information.
 <p>
 This method will first compare the time of each event. If the event time
 is the same for both events, then this method compared the priority of
 each event. If the event priorities are equals, then this method will
 compare the two event based on specific event information.
 <p>
 This method is called by the game in order to sort the event queue of a
 robot to make sure the events are listed in chronological order.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true#compareTo-T-" title="class or interface in java.lang">compareTo</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/Event.html" title="class in robocode">Event</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/Event.html#compareTo-robocode.Event-">compareTo</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/Event.html" title="class in robocode">Event</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event to compare to this event.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a negative value if this event has higher precedence, i.e. must
         be listed before the specified event. A positive value if this event
         has a lower precedence, i.e. must be listed after the specified event.
         0 means that the precedence of the two events are equal.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/HitByBulletEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/HitWallEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/HitRobotEvent.html" target="_top">Frames</a></li>
<li><a href="HitRobotEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
