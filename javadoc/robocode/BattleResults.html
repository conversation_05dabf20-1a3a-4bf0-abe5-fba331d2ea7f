<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BattleResults (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BattleResults (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/BattleEndedEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/BattleRules.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/BattleResults.html" target="_top">Frames</a></li>
<li><a href="BattleResults.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class BattleResults" class="title">Class BattleResults</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>robocode.BattleResults</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&gt;</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BattleResults</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&gt;</pre>
<div class="block">Contains the battle results returned by <a href="../robocode/BattleEndedEvent.html#getResults--"><code>BattleEndedEvent.getResults()</code></a>
 when a battle has ended.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.1</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/BattleEndedEvent.html#getResults--"><code>BattleEndedEvent.getResults()</code></a>, 
<a href="../robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-"><code>Robot.onBattleEnded(BattleEndedEvent)</code></a>, 
<a href="../serialized-form.html#robocode.BattleResults">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#bulletDamage">bulletDamage</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#bulletDamageBonus">bulletDamageBonus</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#firsts">firsts</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#lastSurvivorBonus">lastSurvivorBonus</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#ramDamage">ramDamage</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#ramDamageBonus">ramDamageBonus</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#rank">rank</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#score">score</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#seconds">seconds</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#serialVersionUID">serialVersionUID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#survival">survival</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#teamLeaderName">teamLeaderName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#thirds">thirds</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#BattleResults-java.lang.String-int-double-double-double-double-double-double-double-int-int-int-">BattleResults</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;teamLeaderName,
             int&nbsp;rank,
             double&nbsp;score,
             double&nbsp;survival,
             double&nbsp;lastSurvivorBonus,
             double&nbsp;bulletDamage,
             double&nbsp;bulletDamageBonus,
             double&nbsp;ramDamage,
             double&nbsp;ramDamageBonus,
             int&nbsp;firsts,
             int&nbsp;seconds,
             int&nbsp;thirds)</code>
<div class="block">Constructs this BattleResults object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#compareTo-robocode.BattleResults-">compareTo</a></span>(<a href="../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&nbsp;o)</code></td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#equals-java.lang.Object-">equals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;obj)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getBulletDamage--">getBulletDamage</a></span>()</code>
<div class="block">Returns the bullet damage score of this robot in the battle.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getBulletDamageBonus--">getBulletDamageBonus</a></span>()</code>
<div class="block">Returns the bullet damage bonus of this robot in the battle.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getFirsts--">getFirsts</a></span>()</code>
<div class="block">Returns the number of rounds this robot placed first in the battle.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getLastSurvivorBonus--">getLastSurvivorBonus</a></span>()</code>
<div class="block">Returns the last survivor score of this robot in the battle.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getRamDamage--">getRamDamage</a></span>()</code>
<div class="block">Returns the ram damage score of this robot in the battle.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getRamDamageBonus--">getRamDamageBonus</a></span>()</code>
<div class="block">Returns the ram damage bonus of this robot in the battle.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getRank--">getRank</a></span>()</code>
<div class="block">Returns the rank of this robot in the battle results.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getScore--">getScore</a></span>()</code>
<div class="block">Returns the total score of this robot in the battle.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getSeconds--">getSeconds</a></span>()</code>
<div class="block">Returns the number of rounds this robot placed second in the battle.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getSurvival--">getSurvival</a></span>()</code>
<div class="block">Returns the survival score of this robot in the battle.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getTeamLeaderName--">getTeamLeaderName</a></span>()</code>
<div class="block">Returns the name of the team leader in the team or the name of the
 robot if the robot is not participating in a team.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#getThirds--">getThirds</a></span>()</code>
<div class="block">Returns the number of rounds this robot placed third in the battle.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleResults.html#hashCode--">hashCode</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="serialVersionUID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>serialVersionUID</h4>
<pre>protected static final&nbsp;long serialVersionUID</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.BattleResults.serialVersionUID">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="teamLeaderName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>teamLeaderName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> teamLeaderName</pre>
</li>
</ul>
<a name="rank">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rank</h4>
<pre>protected&nbsp;int rank</pre>
</li>
</ul>
<a name="score">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>score</h4>
<pre>protected&nbsp;double score</pre>
</li>
</ul>
<a name="survival">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>survival</h4>
<pre>protected&nbsp;double survival</pre>
</li>
</ul>
<a name="lastSurvivorBonus">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastSurvivorBonus</h4>
<pre>protected&nbsp;double lastSurvivorBonus</pre>
</li>
</ul>
<a name="bulletDamage">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bulletDamage</h4>
<pre>protected&nbsp;double bulletDamage</pre>
</li>
</ul>
<a name="bulletDamageBonus">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bulletDamageBonus</h4>
<pre>protected&nbsp;double bulletDamageBonus</pre>
</li>
</ul>
<a name="ramDamage">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ramDamage</h4>
<pre>protected&nbsp;double ramDamage</pre>
</li>
</ul>
<a name="ramDamageBonus">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ramDamageBonus</h4>
<pre>protected&nbsp;double ramDamageBonus</pre>
</li>
</ul>
<a name="firsts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>firsts</h4>
<pre>protected&nbsp;int firsts</pre>
</li>
</ul>
<a name="seconds">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>seconds</h4>
<pre>protected&nbsp;int seconds</pre>
</li>
</ul>
<a name="thirds">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>thirds</h4>
<pre>protected&nbsp;int thirds</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BattleResults-java.lang.String-int-double-double-double-double-double-double-double-int-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BattleResults</h4>
<pre>public&nbsp;BattleResults(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;teamLeaderName,
                     int&nbsp;rank,
                     double&nbsp;score,
                     double&nbsp;survival,
                     double&nbsp;lastSurvivorBonus,
                     double&nbsp;bulletDamage,
                     double&nbsp;bulletDamageBonus,
                     double&nbsp;ramDamage,
                     double&nbsp;ramDamageBonus,
                     int&nbsp;firsts,
                     int&nbsp;seconds,
                     int&nbsp;thirds)</pre>
<div class="block">Constructs this BattleResults object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>teamLeaderName</code> - the name of the team leader.</dd>
<dd><code>rank</code> - the rank of the robot in the battle.</dd>
<dd><code>score</code> - the total score for the robot in the battle.</dd>
<dd><code>survival</code> - the survival score for the robot in the battle.</dd>
<dd><code>lastSurvivorBonus</code> - the last survivor bonus for the robot in the battle.</dd>
<dd><code>bulletDamage</code> - the bullet damage score for the robot in the battle.</dd>
<dd><code>bulletDamageBonus</code> - the bullet damage bonus for the robot in the battle.</dd>
<dd><code>ramDamage</code> - the ramming damage for the robot in the battle.</dd>
<dd><code>ramDamageBonus</code> - the ramming damage bonus for the robot in the battle.</dd>
<dd><code>firsts</code> - the number of rounds this robot placed first.</dd>
<dd><code>seconds</code> - the number of rounds this robot placed second.</dd>
<dd><code>thirds</code> - the number of rounds this robot placed third.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTeamLeaderName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeamLeaderName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTeamLeaderName()</pre>
<div class="block">Returns the name of the team leader in the team or the name of the
 robot if the robot is not participating in a team.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the team leader in the team or the name of the robot.</dd>
</dl>
</li>
</ul>
<a name="getRank--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRank</h4>
<pre>public&nbsp;int&nbsp;getRank()</pre>
<div class="block">Returns the rank of this robot in the battle results.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the rank of this robot in the battle results.</dd>
</dl>
</li>
</ul>
<a name="getScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScore</h4>
<pre>public&nbsp;int&nbsp;getScore()</pre>
<div class="block">Returns the total score of this robot in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total score of this robot in the battle.</dd>
</dl>
</li>
</ul>
<a name="getSurvival--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSurvival</h4>
<pre>public&nbsp;int&nbsp;getSurvival()</pre>
<div class="block">Returns the survival score of this robot in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the survival score of this robot in the battle.</dd>
</dl>
</li>
</ul>
<a name="getLastSurvivorBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastSurvivorBonus</h4>
<pre>public&nbsp;int&nbsp;getLastSurvivorBonus()</pre>
<div class="block">Returns the last survivor score of this robot in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the last survivor score of this robot in the battle.</dd>
</dl>
</li>
</ul>
<a name="getBulletDamage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletDamage</h4>
<pre>public&nbsp;int&nbsp;getBulletDamage()</pre>
<div class="block">Returns the bullet damage score of this robot in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bullet damage score of this robot in the battle.</dd>
</dl>
</li>
</ul>
<a name="getBulletDamageBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletDamageBonus</h4>
<pre>public&nbsp;int&nbsp;getBulletDamageBonus()</pre>
<div class="block">Returns the bullet damage bonus of this robot in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bullet damage bonus of this robot in the battle.</dd>
</dl>
</li>
</ul>
<a name="getRamDamage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRamDamage</h4>
<pre>public&nbsp;int&nbsp;getRamDamage()</pre>
<div class="block">Returns the ram damage score of this robot in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the ram damage score of this robot in the battle.</dd>
</dl>
</li>
</ul>
<a name="getRamDamageBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRamDamageBonus</h4>
<pre>public&nbsp;int&nbsp;getRamDamageBonus()</pre>
<div class="block">Returns the ram damage bonus of this robot in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the ram damage bonus of this robot in the battle.</dd>
</dl>
</li>
</ul>
<a name="getFirsts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirsts</h4>
<pre>public&nbsp;int&nbsp;getFirsts()</pre>
<div class="block">Returns the number of rounds this robot placed first in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds this robot placed first in the battle.</dd>
</dl>
</li>
</ul>
<a name="getSeconds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSeconds</h4>
<pre>public&nbsp;int&nbsp;getSeconds()</pre>
<div class="block">Returns the number of rounds this robot placed second in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds this robot placed second in the battle.</dd>
</dl>
</li>
</ul>
<a name="getThirds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThirds</h4>
<pre>public&nbsp;int&nbsp;getThirds()</pre>
<div class="block">Returns the number of rounds this robot placed third in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds this robot placed third in the battle.</dd>
</dl>
</li>
</ul>
<a name="compareTo-robocode.BattleResults-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compareTo</h4>
<pre>public&nbsp;int&nbsp;compareTo(<a href="../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true#compareTo-T-" title="class or interface in java.lang">compareTo</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;obj)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/BattleEndedEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/BattleRules.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/BattleResults.html" target="_top">Frames</a></li>
<li><a href="BattleResults.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
