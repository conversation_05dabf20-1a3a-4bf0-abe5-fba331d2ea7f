<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RateControlRobot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RateControlRobot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RadarTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/Robocode.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/RateControlRobot.html" target="_top">Frames</a></li>
<li><a href="RateControlRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class RateControlRobot" class="title">Class RateControlRobot</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_RobotBase.html" title="class in robocode">robocode._RobotBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_Robot.html" title="class in robocode">robocode._Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/Robot.html" title="class in robocode">robocode.Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_AdvancedRobot.html" title="class in robocode">robocode._AdvancedRobot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">robocode._AdvancedRadiansRobot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/AdvancedRobot.html" title="class in robocode">robocode.AdvancedRobot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/TeamRobot.html" title="class in robocode">robocode.TeamRobot</a></li>
<li>
<ul class="inheritance">
<li>robocode.RateControlRobot</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>, <a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a>, <a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a>, <a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a>, <a href="../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a>, <a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, <a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, <a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, <a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>, <a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a>, <a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a>, <a href="../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RateControlRobot</span>
extends <a href="../robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></pre>
<div class="block">This advanced robot type allows you to set a rate for each of the robot's movements.
 <p>
 You can set the rate for:<ul>
 <li>velocity - pixels per turn</li>
 <li>robot turn - radians per turn</li>
 <li>gun rotation - radians per turn</li>
 <li>radar rotation - radians per turn</li>
 </ul>
 When you set a rate for one of the above movements, the movement will continue the move by
 specified rate for ever, until the rate is changed. In order to move ahead or right, the
 rate must be set to a positive value. If a negative value is used instead, the movement
 will go back or to the left. In order to stop the movement, the rate must be
 set to 0.
 <p>
 Note: When calling <code>setVelocityRate()</code>, <code>setTurnRate()</code>, <code>setGunRotationRate()</code>,
 <code>setRadarRotationRate()</code> and variants, Any previous calls to "movement" functions outside of
 <code>RateControlRobot</code>, such as <code>setAhead()</code>, <code>setTurnLeft()</code>,
 <code>setTurnRadarRightRadians()</code> and similar will be overridden when calling the
 <a href="../robocode/RateControlRobot.html#execute--"><code>execute()</code></a> on this robot class.
 <p>
 Look into the source code for the <code>sample.VelociRobot</code> in order to see how to use this
 robot type.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7.1.3</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Joshua Galecki (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>, 
<a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>, 
<a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, 
<a href="../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>, 
<a href="../robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>, 
<a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#out">out</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#RateControlRobot--">RateControlRobot</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#execute--">execute</a></span>()</code>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#getGunRotationRate--">getGunRotationRate</a></span>()</code>
<div class="block">Gets the gun's clockwise rotation per turn, in degrees.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#getGunRotationRateRadians--">getGunRotationRateRadians</a></span>()</code>
<div class="block">Gets the gun's clockwise rotation per turn, in radians.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#getRadarRotationRate--">getRadarRotationRate</a></span>()</code>
<div class="block">Gets the radar's clockwise rotation per turn, in degrees.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#getRadarRotationRateRadians--">getRadarRotationRateRadians</a></span>()</code>
<div class="block">Gets the radar's clockwise rotation per turn, in radians.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#getTurnRate--">getTurnRate</a></span>()</code>
<div class="block">Gets the robot's clockwise rotation per turn, in degrees.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#getTurnRateRadians--">getTurnRateRadians</a></span>()</code>
<div class="block">Gets the robot's clockwise rotation per turn, in radians.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#getVelocityRate--">getVelocityRate</a></span>()</code>
<div class="block">Returns the speed the robot will move, in pixels per turn.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#setGunRotationRate-double-">setGunRotationRate</a></span>(double&nbsp;gunRotationRate)</code>
<div class="block">Sets the gun's clockwise (right) rotation per turn, in degrees.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#setGunRotationRateRadians-double-">setGunRotationRateRadians</a></span>(double&nbsp;gunRotationRate)</code>
<div class="block">Sets the gun's clockwise (right) rotation per turn, in radians.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#setRadarRotationRate-double-">setRadarRotationRate</a></span>(double&nbsp;radarRotationRate)</code>
<div class="block">Sets the radar's clockwise (right) rotation per turn, in degrees.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#setRadarRotationRateRadians-double-">setRadarRotationRateRadians</a></span>(double&nbsp;radarRotationRate)</code>
<div class="block">Sets the radar's clockwise (right) rotation per turn, in radians.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#setTurnRate-double-">setTurnRate</a></span>(double&nbsp;turnRate)</code>
<div class="block">Sets the robot's clockwise (right) rotation per turn, in degrees.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#setTurnRateRadians-double-">setTurnRateRadians</a></span>(double&nbsp;turnRate)</code>
<div class="block">Sets the robot's clockwise (right) rotation per turn, in radians.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/RateControlRobot.html#setVelocityRate-double-">setVelocityRate</a></span>(double&nbsp;velocityRate)</code>
<div class="block">Sets the speed the robot will move (forward), in pixels per turn.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.TeamRobot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></h3>
<code><a href="../robocode/TeamRobot.html#broadcastMessage-java.io.Serializable-">broadcastMessage</a>, <a href="../robocode/TeamRobot.html#getMessageEvents--">getMessageEvents</a>, <a href="../robocode/TeamRobot.html#getTeamEventListener--">getTeamEventListener</a>, <a href="../robocode/TeamRobot.html#getTeammates--">getTeammates</a>, <a href="../robocode/TeamRobot.html#isTeammate-java.lang.String-">isTeammate</a>, <a href="../robocode/TeamRobot.html#onMessageReceived-robocode.MessageEvent-">onMessageReceived</a>, <a href="../robocode/TeamRobot.html#sendMessage-java.lang.String-java.io.Serializable-">sendMessage</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.AdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></h3>
<code><a href="../robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-">addCustomEvent</a>, <a href="../robocode/AdvancedRobot.html#clearAllEvents--">clearAllEvents</a>, <a href="../robocode/AdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener</a>, <a href="../robocode/AdvancedRobot.html#getAllEvents--">getAllEvents</a>, <a href="../robocode/AdvancedRobot.html#getBulletHitBulletEvents--">getBulletHitBulletEvents</a>, <a href="../robocode/AdvancedRobot.html#getBulletHitEvents--">getBulletHitEvents</a>, <a href="../robocode/AdvancedRobot.html#getBulletMissedEvents--">getBulletMissedEvents</a>, <a href="../robocode/AdvancedRobot.html#getDataDirectory--">getDataDirectory</a>, <a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-">getDataFile</a>, <a href="../robocode/AdvancedRobot.html#getDataQuotaAvailable--">getDataQuotaAvailable</a>, <a href="../robocode/AdvancedRobot.html#getDistanceRemaining--">getDistanceRemaining</a>, <a href="../robocode/AdvancedRobot.html#getEventPriority-java.lang.String-">getEventPriority</a>, <a href="../robocode/AdvancedRobot.html#getGunHeadingRadians--">getGunHeadingRadians</a>, <a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--">getGunTurnRemaining</a>, <a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians</a>, <a href="../robocode/AdvancedRobot.html#getHeadingRadians--">getHeadingRadians</a>, <a href="../robocode/AdvancedRobot.html#getHitByBulletEvents--">getHitByBulletEvents</a>, <a href="../robocode/AdvancedRobot.html#getHitRobotEvents--">getHitRobotEvents</a>, <a href="../robocode/AdvancedRobot.html#getHitWallEvents--">getHitWallEvents</a>, <a href="../robocode/AdvancedRobot.html#getRadarHeadingRadians--">getRadarHeadingRadians</a>, <a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--">getRadarTurnRemaining</a>, <a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians</a>, <a href="../robocode/AdvancedRobot.html#getRobotDeathEvents--">getRobotDeathEvents</a>, <a href="../robocode/AdvancedRobot.html#getScannedRobotEvents--">getScannedRobotEvents</a>, <a href="../robocode/AdvancedRobot.html#getStatusEvents--">getStatusEvents</a>, <a href="../robocode/AdvancedRobot.html#getTurnRemaining--">getTurnRemaining</a>, <a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--">getTurnRemainingRadians</a>, <a href="../robocode/AdvancedRobot.html#isAdjustGunForRobotTurn--">isAdjustGunForRobotTurn</a>, <a href="../robocode/AdvancedRobot.html#isAdjustRadarForGunTurn--">isAdjustRadarForGunTurn</a>, <a href="../robocode/AdvancedRobot.html#isAdjustRadarForRobotTurn--">isAdjustRadarForRobotTurn</a>, <a href="../robocode/AdvancedRobot.html#onCustomEvent-robocode.CustomEvent-">onCustomEvent</a>, <a href="../robocode/AdvancedRobot.html#onDeath-robocode.DeathEvent-">onDeath</a>, <a href="../robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-">onSkippedTurn</a>, <a href="../robocode/AdvancedRobot.html#removeCustomEvent-robocode.Condition-">removeCustomEvent</a>, <a href="../robocode/AdvancedRobot.html#setAhead-double-">setAhead</a>, <a href="../robocode/AdvancedRobot.html#setBack-double-">setBack</a>, <a href="../robocode/AdvancedRobot.html#setEventPriority-java.lang.String-int-">setEventPriority</a>, <a href="../robocode/AdvancedRobot.html#setFire-double-">setFire</a>, <a href="../robocode/AdvancedRobot.html#setFireBullet-double-">setFireBullet</a>, <a href="../robocode/AdvancedRobot.html#setInterruptible-boolean-">setInterruptible</a>, <a href="../robocode/AdvancedRobot.html#setMaxTurnRate-double-">setMaxTurnRate</a>, <a href="../robocode/AdvancedRobot.html#setMaxVelocity-double-">setMaxVelocity</a>, <a href="../robocode/AdvancedRobot.html#setResume--">setResume</a>, <a href="../robocode/AdvancedRobot.html#setStop--">setStop</a>, <a href="../robocode/AdvancedRobot.html#setStop-boolean-">setStop</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunLeft-double-">setTurnGunLeft</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-">setTurnGunLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunRight-double-">setTurnGunRight</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunRightRadians-double-">setTurnGunRightRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnLeft-double-">setTurnLeft</a>, <a href="../robocode/AdvancedRobot.html#setTurnLeftRadians-double-">setTurnLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarLeft-double-">setTurnRadarLeft</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-">setTurnRadarLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarRight-double-">setTurnRadarRight</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-">setTurnRadarRightRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnRight-double-">setTurnRight</a>, <a href="../robocode/AdvancedRobot.html#setTurnRightRadians-double-">setTurnRightRadians</a>, <a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-">turnGunLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-">turnGunRightRadians</a>, <a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-">turnLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-">turnRadarLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-">turnRadarRightRadians</a>, <a href="../robocode/AdvancedRobot.html#turnRightRadians-double-">turnRightRadians</a>, <a href="../robocode/AdvancedRobot.html#waitFor-robocode.Condition-">waitFor</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._AdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></h3>
<code><a href="../robocode/_AdvancedRobot.html#endTurn--">endTurn</a>, <a href="../robocode/_AdvancedRobot.html#getGunHeadingDegrees--">getGunHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getHeadingDegrees--">getHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getMaxWaitCount--">getMaxWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#getRadarHeadingDegrees--">getRadarHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getWaitCount--">getWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunLeftDegrees-double-">setTurnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunRightDegrees-double-">setTurnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnLeftDegrees-double-">setTurnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarLeftDegrees-double-">setTurnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarRightDegrees-double-">setTurnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRightDegrees-double-">setTurnRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunLeftDegrees-double-">turnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunRightDegrees-double-">turnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnLeftDegrees-double-">turnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarLeftDegrees-double-">turnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarRightDegrees-double-">turnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRightDegrees-double-">turnRightDegrees</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/Robot.html" title="class in robocode">Robot</a></h3>
<code><a href="../robocode/Robot.html#ahead-double-">ahead</a>, <a href="../robocode/Robot.html#back-double-">back</a>, <a href="../robocode/Robot.html#doNothing--">doNothing</a>, <a href="../robocode/Robot.html#fire-double-">fire</a>, <a href="../robocode/Robot.html#fireBullet-double-">fireBullet</a>, <a href="../robocode/Robot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../robocode/Robot.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../robocode/Robot.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../robocode/Robot.html#getEnergy--">getEnergy</a>, <a href="../robocode/Robot.html#getGraphics--">getGraphics</a>, <a href="../robocode/Robot.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../robocode/Robot.html#getGunHeading--">getGunHeading</a>, <a href="../robocode/Robot.html#getGunHeat--">getGunHeat</a>, <a href="../robocode/Robot.html#getHeading--">getHeading</a>, <a href="../robocode/Robot.html#getHeight--">getHeight</a>, <a href="../robocode/Robot.html#getInteractiveEventListener--">getInteractiveEventListener</a>, <a href="../robocode/Robot.html#getName--">getName</a>, <a href="../robocode/Robot.html#getNumRounds--">getNumRounds</a>, <a href="../robocode/Robot.html#getNumSentries--">getNumSentries</a>, <a href="../robocode/Robot.html#getOthers--">getOthers</a>, <a href="../robocode/Robot.html#getPaintEventListener--">getPaintEventListener</a>, <a href="../robocode/Robot.html#getRadarHeading--">getRadarHeading</a>, <a href="../robocode/Robot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../robocode/Robot.html#getRoundNum--">getRoundNum</a>, <a href="../robocode/Robot.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../robocode/Robot.html#getTime--">getTime</a>, <a href="../robocode/Robot.html#getVelocity--">getVelocity</a>, <a href="../robocode/Robot.html#getWidth--">getWidth</a>, <a href="../robocode/Robot.html#getX--">getX</a>, <a href="../robocode/Robot.html#getY--">getY</a>, <a href="../robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded</a>, <a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit</a>, <a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet</a>, <a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed</a>, <a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet</a>, <a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot</a>, <a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-">onHitWall</a>, <a href="../robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed</a>, <a href="../robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased</a>, <a href="../robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped</a>, <a href="../robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked</a>, <a href="../robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged</a>, <a href="../robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered</a>, <a href="../robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited</a>, <a href="../robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved</a>, <a href="../robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed</a>, <a href="../robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased</a>, <a href="../robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved</a>, <a href="../robocode/Robot.html#onPaint-java.awt.Graphics2D-">onPaint</a>, <a href="../robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath</a>, <a href="../robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded</a>, <a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot</a>, <a href="../robocode/Robot.html#onStatus-robocode.StatusEvent-">onStatus</a>, <a href="../robocode/Robot.html#onWin-robocode.WinEvent-">onWin</a>, <a href="../robocode/Robot.html#resume--">resume</a>, <a href="../robocode/Robot.html#run--">run</a>, <a href="../robocode/Robot.html#scan--">scan</a>, <a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-">setAdjustGunForRobotTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-">setAdjustRadarForRobotTurn</a>, <a href="../robocode/Robot.html#setAllColors-java.awt.Color-">setAllColors</a>, <a href="../robocode/Robot.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../robocode/Robot.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../robocode/Robot.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../robocode/Robot.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../robocode/Robot.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../robocode/Robot.html#stop--">stop</a>, <a href="../robocode/Robot.html#stop-boolean-">stop</a>, <a href="../robocode/Robot.html#turnGunLeft-double-">turnGunLeft</a>, <a href="../robocode/Robot.html#turnGunRight-double-">turnGunRight</a>, <a href="../robocode/Robot.html#turnLeft-double-">turnLeft</a>, <a href="../robocode/Robot.html#turnRadarLeft-double-">turnRadarLeft</a>, <a href="../robocode/Robot.html#turnRadarRight-double-">turnRadarRight</a>, <a href="../robocode/Robot.html#turnRight-double-">turnRight</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_Robot.html" title="class in robocode">_Robot</a></h3>
<code><a href="../robocode/_Robot.html#getBattleNum--">getBattleNum</a>, <a href="../robocode/_Robot.html#getGunCharge--">getGunCharge</a>, <a href="../robocode/_Robot.html#getGunImageName--">getGunImageName</a>, <a href="../robocode/_Robot.html#getLife--">getLife</a>, <a href="../robocode/_Robot.html#getNumBattles--">getNumBattles</a>, <a href="../robocode/_Robot.html#getRadarImageName--">getRadarImageName</a>, <a href="../robocode/_Robot.html#getRobotImageName--">getRobotImageName</a>, <a href="../robocode/_Robot.html#setGunImageName-java.lang.String-">setGunImageName</a>, <a href="../robocode/_Robot.html#setRadarImageName-java.lang.String-">setRadarImageName</a>, <a href="../robocode/_Robot.html#setRobotImageName-java.lang.String-">setRobotImageName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#finalize--">finalize</a>, <a href="../robocode/_RobotBase.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/_RobotBase.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a>, <a href="../robocode/_RobotBase.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IAdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IAdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IBasicRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IBasicRobot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RateControlRobot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RateControlRobot</h4>
<pre>public&nbsp;RateControlRobot()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setVelocityRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVelocityRate</h4>
<pre>public&nbsp;void&nbsp;setVelocityRate(double&nbsp;velocityRate)</pre>
<div class="block">Sets the speed the robot will move (forward), in pixels per turn.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot will move backwards
 <p>
 Example:
 <pre>
   // Set the robot to move forward 2 pixels per turn
   setVelocityRate(2);

   // Set the robot to move backwards 8 pixels per turn
   // (overrides the previous order)
   setVelocityRate(-8);

   ...
   // Executes the last setVelocityRate()
   execute();
 </pre>

 Note: This method overrules <a href="../robocode/AdvancedRobot.html#setAhead-double-"><code>AdvancedRobot.setAhead(double)</code></a> and
 <a href="../robocode/AdvancedRobot.html#setBack-double-"><code>AdvancedRobot.setBack(double)</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>velocityRate</code> - pixels per turn the robot will move.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getVelocityRate--"><code>getVelocityRate()</code></a>, 
<a href="../robocode/RateControlRobot.html#setTurnRate-double-"><code>setTurnRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setGunRotationRate-double-"><code>setGunRotationRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setRadarRotationRate-double-"><code>setRadarRotationRate(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setAhead-double-"><code>AdvancedRobot.setAhead(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setBack-double-"><code>AdvancedRobot.setBack(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getVelocityRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVelocityRate</h4>
<pre>public&nbsp;double&nbsp;getVelocityRate()</pre>
<div class="block">Returns the speed the robot will move, in pixels per turn.
 Positive values means that the robot will move forward.
 Negative values means that the robot will move backwards.
 If the value is 0, the robot will stand still.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The speed of the robot in pixels per turn</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#setVelocityRate-double-"><code>setVelocityRate(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRate</h4>
<pre>public&nbsp;void&nbsp;setTurnRate(double&nbsp;turnRate)</pre>
<div class="block">Sets the robot's clockwise (right) rotation per turn, in degrees.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot turns counterclockwise (left)
 <p>
 Example:
 <pre>
   // Set the robot to turn right 10 degrees per turn
   setTurnRate(10);

   // Set the robot to turn left 4 degrees per turn
   // (overrides the previous order)
   setTurnRate(-5);

   ...
   // Executes the last setTurnRate()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>turnRate</code> - angle of the clockwise rotation, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getTurnRate--"><code>getTurnRate()</code></a>, 
<a href="../robocode/RateControlRobot.html#setVelocityRate-double-"><code>setVelocityRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setGunRotationRate-double-"><code>setGunRotationRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setRadarRotationRate-double-"><code>setRadarRotationRate(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRight-double-"><code>AdvancedRobot.setTurnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnLeft-double-"><code>AdvancedRobot.setTurnLeft(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getTurnRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRate</h4>
<pre>public&nbsp;double&nbsp;getTurnRate()</pre>
<div class="block">Gets the robot's clockwise rotation per turn, in degrees.
 Positive values means that the robot will turn to the right.
 Negative values means that the robot will turn to the left.
 If the value is 0, the robot will not turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Angle of the clockwise rotation, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#setTurnRate-double-"><code>setTurnRate(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRateRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRateRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnRateRadians(double&nbsp;turnRate)</pre>
<div class="block">Sets the robot's clockwise (right) rotation per turn, in radians.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot turns counterclockwise (left)
 <p>
 Example:
 <pre>
   // Set the robot to turn right pi / 32 radians per turn
   setTurnRateRadians(Math.PI / 32);

   // Set the robot to turn left pi / 20 radians per turn
   // (overrides the previous order)
   setTurnRateRadians(-Math.PI / 20);

   ...
   // Executes the last setTurnRateRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>turnRate</code> - angle of the clockwise rotation, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getTurnRateRadians--"><code>getTurnRateRadians()</code></a>, 
<a href="../robocode/RateControlRobot.html#setVelocityRate-double-"><code>setVelocityRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setGunRotationRateRadians-double-"><code>setGunRotationRateRadians(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setRadarRotationRateRadians-double-"><code>setRadarRotationRateRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRightRadians-double-"><code>AdvancedRobot.setTurnRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnLeftRadians-double-"><code>AdvancedRobot.setTurnLeftRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getTurnRateRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRateRadians</h4>
<pre>public&nbsp;double&nbsp;getTurnRateRadians()</pre>
<div class="block">Gets the robot's clockwise rotation per turn, in radians.
 Positive values means that the robot will turn to the right.
 Negative values means that the robot will turn to the left.
 If the value is 0, the robot will not turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Angle of the clockwise rotation, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getTurnRateRadians--"><code>getTurnRateRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="setGunRotationRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGunRotationRate</h4>
<pre>public&nbsp;void&nbsp;setGunRotationRate(double&nbsp;gunRotationRate)</pre>
<div class="block">Sets the gun's clockwise (right) rotation per turn, in degrees.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the gun turns counterclockwise (left)
 <p>
 Example:
 <pre>
   // Set the gun to turn right 15 degrees per turn
   setGunRotationRate(15);

   // Set the gun to turn left 9 degrees per turn
   // (overrides the previous order)
   setGunRotationRate(-9);

   ...
   // Executes the last setGunRotationRate()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gunRotationRate</code> - angle of the clockwise rotation, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getGunRotationRate--"><code>getGunRotationRate()</code></a>, 
<a href="../robocode/RateControlRobot.html#setVelocityRate-double-"><code>setVelocityRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setTurnRate-double-"><code>setTurnRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setRadarRotationRate-double-"><code>setRadarRotationRate(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunRight-double-"><code>AdvancedRobot.setTurnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunLeft-double-"><code>AdvancedRobot.setTurnGunLeft(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunRotationRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunRotationRate</h4>
<pre>public&nbsp;double&nbsp;getGunRotationRate()</pre>
<div class="block">Gets the gun's clockwise rotation per turn, in degrees.
 Positive values means that the gun will turn to the right.
 Negative values means that the gun will turn to the left.
 If the value is 0, the gun will not turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Angle of the clockwise rotation, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#setGunRotationRate-double-"><code>setGunRotationRate(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setGunRotationRateRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGunRotationRateRadians</h4>
<pre>public&nbsp;void&nbsp;setGunRotationRateRadians(double&nbsp;gunRotationRate)</pre>
<div class="block">Sets the gun's clockwise (right) rotation per turn, in radians.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the gun turns counterclockwise (left)
 <p>
 Example:
 <pre>
   // Set the gun to turn right pi / 16 radians per turn
   setGunRotationRateRadians(Math.PI / 16);

   // Set the gun to turn left pi / 12 radians per turn
   // (overrides the previous order)
   setGunRotationRateRadians(-Math.PI / 12);

   ...
   // Executes the last setGunRotationRateRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gunRotationRate</code> - angle of the clockwise rotation, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getGunRotationRateRadians--"><code>getGunRotationRateRadians()</code></a>, 
<a href="../robocode/RateControlRobot.html#setVelocityRate-double-"><code>setVelocityRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setTurnRateRadians-double-"><code>setTurnRateRadians(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setRadarRotationRateRadians-double-"><code>setRadarRotationRateRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunRightRadians-double-"><code>AdvancedRobot.setTurnGunRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-"><code>AdvancedRobot.setTurnGunLeftRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunRotationRateRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunRotationRateRadians</h4>
<pre>public&nbsp;double&nbsp;getGunRotationRateRadians()</pre>
<div class="block">Gets the gun's clockwise rotation per turn, in radians.
 Positive values means that the gun will turn to the right.
 Negative values means that the gun will turn to the left.
 If the value is 0, the gun will not turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Angle of the clockwise rotation, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#setGunRotationRateRadians-double-"><code>setGunRotationRateRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setRadarRotationRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRadarRotationRate</h4>
<pre>public&nbsp;void&nbsp;setRadarRotationRate(double&nbsp;radarRotationRate)</pre>
<div class="block">Sets the radar's clockwise (right) rotation per turn, in degrees.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the radar turns counterclockwise (left)
 <p>
 Example:
 <pre>
   // Set the radar to turn right 45 degrees per turn
   setRadarRotationRate(45);

   // Set the radar to turn left 15 degrees per turn
   // (overrides the previous order)
   setRadarRotationRate(-15);

   ...
   // Executes the last setRadarRotationRate()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radarRotationRate</code> - angle of the clockwise rotation, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getRadarRotationRate--"><code>getRadarRotationRate()</code></a>, 
<a href="../robocode/RateControlRobot.html#setVelocityRate-double-"><code>setVelocityRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setTurnRate-double-"><code>setTurnRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setGunRotationRate-double-"><code>setGunRotationRate(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarRight-double-"><code>AdvancedRobot.setTurnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarLeft-double-"><code>AdvancedRobot.setTurnRadarLeft(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarRotationRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarRotationRate</h4>
<pre>public&nbsp;double&nbsp;getRadarRotationRate()</pre>
<div class="block">Gets the radar's clockwise rotation per turn, in degrees.
 Positive values means that the radar will turn to the right.
 Negative values means that the radar will turn to the left.
 If the value is 0, the radar will not turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Angle of the clockwise rotation, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#setRadarRotationRate-double-"><code>setRadarRotationRate(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setRadarRotationRateRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRadarRotationRateRadians</h4>
<pre>public&nbsp;void&nbsp;setRadarRotationRateRadians(double&nbsp;radarRotationRate)</pre>
<div class="block">Sets the radar's clockwise (right) rotation per turn, in radians.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the radar turns counterclockwise (left)
 <p>
 Example:
 <pre>
   // Set the radar to turn right pi / 4 radians per turn
   setRadarRotationRateRadians(Math.PI / 4);

   // Set the radar to turn left pi / 8 radians per turn
   // (overrides the previous order)
   setRadarRotationRateRadians(-Math.PI / 8);

   ...
   // Executes the last setRadarRotationRateRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radarRotationRate</code> - angle of the clockwise rotation, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#getRadarRotationRateRadians--"><code>getRadarRotationRateRadians()</code></a>, 
<a href="../robocode/RateControlRobot.html#setVelocityRate-double-"><code>setVelocityRate(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setTurnRateRadians-double-"><code>setTurnRateRadians(double)</code></a>, 
<a href="../robocode/RateControlRobot.html#setGunRotationRateRadians-double-"><code>setGunRotationRateRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-"><code>AdvancedRobot.setTurnRadarRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-"><code>AdvancedRobot.setTurnRadarLeftRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarRotationRateRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarRotationRateRadians</h4>
<pre>public&nbsp;double&nbsp;getRadarRotationRateRadians()</pre>
<div class="block">Gets the radar's clockwise rotation per turn, in radians.
 Positive values means that the radar will turn to the right.
 Negative values means that the radar will turn to the left.
 If the value is 0, the radar will not turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Angle of the clockwise rotation, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RateControlRobot.html#setRadarRotationRateRadians-double-"><code>setRadarRotationRateRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="execute--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>execute</h4>
<pre>public&nbsp;void&nbsp;execute()</pre>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process. This call returns after the actions have been started.
 <p>
 Note that advanced robots <em>must</em> call this function in order to
 execute pending set* calls like e.g. <code>setVelocityRate()</code>, <code>setFire()</code>,
 <code>setTurnRate()</code> etc. Otherwise, these calls will never get executed.
 <p>
 Any previous calls to "movement" functions outside of <code>RateControlRobot</code>,
 such as <code>setAhead()</code>, <code>setTurnLeft()</code>, <code>setTurnRadarLeftRadians()</code>
 etc. will be overridden when this method is called on this robot class.
 <p>
 In this example the robot will move while turning:
 <pre>
   setVelocityRate(6);
   setTurnRate(7);

   while (true) {
       execute();
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/AdvancedRobot.html#execute--">execute</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RadarTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/Robocode.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/RateControlRobot.html" target="_top">Frames</a></li>
<li><a href="RateControlRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
