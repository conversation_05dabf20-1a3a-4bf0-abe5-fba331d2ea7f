<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../robocode/package-summary.html" target="classFrame">robocode</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="BorderSentry.html" title="interface in robocode" target="classFrame"><span class="interfaceName">BorderSentry</span></a></li>
<li><a href="Droid.html" title="interface in robocode" target="classFrame"><span class="interfaceName">Droid</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="_AdvancedRadiansRobot.html" title="class in robocode" target="classFrame">_AdvancedRadiansRobot</a></li>
<li><a href="_AdvancedRobot.html" title="class in robocode" target="classFrame">_AdvancedRobot</a></li>
<li><a href="_Robot.html" title="class in robocode" target="classFrame">_Robot</a></li>
<li><a href="_RobotBase.html" title="class in robocode" target="classFrame">_RobotBase</a></li>
<li><a href="AdvancedRobot.html" title="class in robocode" target="classFrame">AdvancedRobot</a></li>
<li><a href="BattleEndedEvent.html" title="class in robocode" target="classFrame">BattleEndedEvent</a></li>
<li><a href="BattleResults.html" title="class in robocode" target="classFrame">BattleResults</a></li>
<li><a href="BattleRules.html" title="class in robocode" target="classFrame">BattleRules</a></li>
<li><a href="Bullet.html" title="class in robocode" target="classFrame">Bullet</a></li>
<li><a href="BulletHitBulletEvent.html" title="class in robocode" target="classFrame">BulletHitBulletEvent</a></li>
<li><a href="BulletHitEvent.html" title="class in robocode" target="classFrame">BulletHitEvent</a></li>
<li><a href="BulletMissedEvent.html" title="class in robocode" target="classFrame">BulletMissedEvent</a></li>
<li><a href="Condition.html" title="class in robocode" target="classFrame">Condition</a></li>
<li><a href="CustomEvent.html" title="class in robocode" target="classFrame">CustomEvent</a></li>
<li><a href="DeathEvent.html" title="class in robocode" target="classFrame">DeathEvent</a></li>
<li><a href="Event.html" title="class in robocode" target="classFrame">Event</a></li>
<li><a href="GunTurnCompleteCondition.html" title="class in robocode" target="classFrame">GunTurnCompleteCondition</a></li>
<li><a href="HitByBulletEvent.html" title="class in robocode" target="classFrame">HitByBulletEvent</a></li>
<li><a href="HitRobotEvent.html" title="class in robocode" target="classFrame">HitRobotEvent</a></li>
<li><a href="HitWallEvent.html" title="class in robocode" target="classFrame">HitWallEvent</a></li>
<li><a href="JuniorRobot.html" title="class in robocode" target="classFrame">JuniorRobot</a></li>
<li><a href="KeyEvent.html" title="class in robocode" target="classFrame">KeyEvent</a></li>
<li><a href="KeyPressedEvent.html" title="class in robocode" target="classFrame">KeyPressedEvent</a></li>
<li><a href="KeyReleasedEvent.html" title="class in robocode" target="classFrame">KeyReleasedEvent</a></li>
<li><a href="KeyTypedEvent.html" title="class in robocode" target="classFrame">KeyTypedEvent</a></li>
<li><a href="MessageEvent.html" title="class in robocode" target="classFrame">MessageEvent</a></li>
<li><a href="MouseClickedEvent.html" title="class in robocode" target="classFrame">MouseClickedEvent</a></li>
<li><a href="MouseDraggedEvent.html" title="class in robocode" target="classFrame">MouseDraggedEvent</a></li>
<li><a href="MouseEnteredEvent.html" title="class in robocode" target="classFrame">MouseEnteredEvent</a></li>
<li><a href="MouseEvent.html" title="class in robocode" target="classFrame">MouseEvent</a></li>
<li><a href="MouseExitedEvent.html" title="class in robocode" target="classFrame">MouseExitedEvent</a></li>
<li><a href="MouseMovedEvent.html" title="class in robocode" target="classFrame">MouseMovedEvent</a></li>
<li><a href="MousePressedEvent.html" title="class in robocode" target="classFrame">MousePressedEvent</a></li>
<li><a href="MouseReleasedEvent.html" title="class in robocode" target="classFrame">MouseReleasedEvent</a></li>
<li><a href="MouseWheelMovedEvent.html" title="class in robocode" target="classFrame">MouseWheelMovedEvent</a></li>
<li><a href="MoveCompleteCondition.html" title="class in robocode" target="classFrame">MoveCompleteCondition</a></li>
<li><a href="PaintEvent.html" title="class in robocode" target="classFrame">PaintEvent</a></li>
<li><a href="RadarTurnCompleteCondition.html" title="class in robocode" target="classFrame">RadarTurnCompleteCondition</a></li>
<li><a href="RateControlRobot.html" title="class in robocode" target="classFrame">RateControlRobot</a></li>
<li><a href="Robocode.html" title="class in robocode" target="classFrame">Robocode</a></li>
<li><a href="RobocodeFileOutputStream.html" title="class in robocode" target="classFrame">RobocodeFileOutputStream</a></li>
<li><a href="RobocodeFileWriter.html" title="class in robocode" target="classFrame">RobocodeFileWriter</a></li>
<li><a href="Robot.html" title="class in robocode" target="classFrame">Robot</a></li>
<li><a href="RobotDeathEvent.html" title="class in robocode" target="classFrame">RobotDeathEvent</a></li>
<li><a href="RobotStatus.html" title="class in robocode" target="classFrame">RobotStatus</a></li>
<li><a href="RoundEndedEvent.html" title="class in robocode" target="classFrame">RoundEndedEvent</a></li>
<li><a href="Rules.html" title="class in robocode" target="classFrame">Rules</a></li>
<li><a href="ScannedRobotEvent.html" title="class in robocode" target="classFrame">ScannedRobotEvent</a></li>
<li><a href="SkippedTurnEvent.html" title="class in robocode" target="classFrame">SkippedTurnEvent</a></li>
<li><a href="StatusEvent.html" title="class in robocode" target="classFrame">StatusEvent</a></li>
<li><a href="TeamRobot.html" title="class in robocode" target="classFrame">TeamRobot</a></li>
<li><a href="TurnCompleteCondition.html" title="class in robocode" target="classFrame">TurnCompleteCondition</a></li>
<li><a href="WinEvent.html" title="class in robocode" target="classFrame">WinEvent</a></li>
</ul>
</div>
</body>
</html>
