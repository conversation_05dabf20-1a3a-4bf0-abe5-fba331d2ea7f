<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Robot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Robot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/RobotDeathEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/Robot.html" target="_top">Frames</a></li>
<li><a href="Robot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class Robot" class="title">Class Robot</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_RobotBase.html" title="class in robocode">robocode._RobotBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_Robot.html" title="class in robocode">robocode._Robot</a></li>
<li>
<ul class="inheritance">
<li>robocode.Robot</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>, <a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a>, <a href="../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a>, <a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, <a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, <a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, <a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>, <a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Robot</span>
extends <a href="../robocode/_Robot.html" title="class in robocode">_Robot</a>
implements <a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, <a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a>, <a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, <a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, <a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a></pre>
<div class="block">The basic robot class that you will extend to create your own robots.
 <p>Please note the following standards will be used:
 <br> heading - absolute angle in degrees with 0 facing up the screen,
 positive clockwise. 0 &lt;= heading &lt; 360.
 <br> bearing - relative angle to some object from your robot's heading,
 positive clockwise. -180 &lt; bearing &lt;= 180
 <br> All coordinates are expressed as (x,y).
 <br> All coordinates are positive.
 <br> The origin (0,0) is at the bottom left of the screen.
 <br> Positive x is right.
 <br> Positive y is up.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor), Matthew Reeder (contributor), Stefan Westen (contributor), Pavel Savara (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a target="_top" href="https://robocode.sourceforge.io">
      robocode.sourceforge.net</a>, 
<a href="https://robocode.sourceforge.io/myfirstrobot/MyFirstRobot.html">
      Building your first robot</a>, 
<a href="../robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>, 
<a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, 
<a href="../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>, 
<a href="../robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>, 
<a href="../robocode/RateControlRobot.html" title="class in robocode"><code>RateControlRobot</code></a>, 
<a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#out">out</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/Robot.html#Robot--">Robot</a></span>()</code>
<div class="block">Constructs a new robot.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#ahead-double-">ahead</a></span>(double&nbsp;distance)</code>
<div class="block">Immediately moves your robot ahead (forward) by distance measured in
 pixels.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#back-double-">back</a></span>(double&nbsp;distance)</code>
<div class="block">Immediately moves your robot backward by distance measured in pixels.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#doNothing--">doNothing</a></span>()</code>
<div class="block">Do nothing this turn, meaning that the robot will skip it's turn.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#fire-double-">fire</a></span>(double&nbsp;power)</code>
<div class="block">Immediately fires a bullet.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../robocode/Bullet.html" title="class in robocode">Bullet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#fireBullet-double-">fireBullet</a></span>(double&nbsp;power)</code>
<div class="block">Immediately fires a bullet.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getBasicEventListener--">getBasicEventListener</a></span>()</code>
<div class="block">This method is called by the game to notify this robot about basic
 robot event.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getBattleFieldHeight--">getBattleFieldHeight</a></span>()</code>
<div class="block">Returns the height of the current battlefield measured in pixels.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getBattleFieldWidth--">getBattleFieldWidth</a></span>()</code>
<div class="block">Returns the width of the current battlefield measured in pixels.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getEnergy--">getEnergy</a></span>()</code>
<div class="block">Returns the robot's current energy.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Graphics2D.html?is-external=true" title="class or interface in java.awt">Graphics2D</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getGraphics--">getGraphics</a></span>()</code>
<div class="block">Returns a graphics context used for painting graphical items for the robot.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getGunCoolingRate--">getGunCoolingRate</a></span>()</code>
<div class="block">Returns the rate at which the gun will cool down, i.e.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getGunHeading--">getGunHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's gun is facing, in degrees.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getGunHeat--">getGunHeat</a></span>()</code>
<div class="block">Returns the current heat of the gun.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getHeading--">getHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's body is facing, in degrees.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of the robot measured in pixels.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getInteractiveEventListener--">getInteractiveEventListener</a></span>()</code>
<div class="block">This method is called by the game to notify this robot about interactive
 events, i.e.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getName--">getName</a></span>()</code>
<div class="block">Returns the robot's name.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getNumRounds--">getNumRounds</a></span>()</code>
<div class="block">Returns the number of rounds in the current battle.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getNumSentries--">getNumSentries</a></span>()</code>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getOthers--">getOthers</a></span>()</code>
<div class="block">Returns how many opponents that are left in the current round.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getPaintEventListener--">getPaintEventListener</a></span>()</code>
<div class="block">This method is called by the game to notify this robot about painting
 events.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getRadarHeading--">getRadarHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's radar is facing, in degrees.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getRobotRunnable--">getRobotRunnable</a></span>()</code>
<div class="block">This method is called by the game to invoke the
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang"><code>run()</code></a> method of your robot, where the program
 of your robot is implemented.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getRoundNum--">getRoundNum</a></span>()</code>
<div class="block">Returns the current round number (0 to <a href="../robocode/Robot.html#getNumRounds--"><code>getNumRounds()</code></a> - 1) of
 the battle.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getSentryBorderSize--">getSentryBorderSize</a></span>()</code>
<div class="block">Returns the sentry border size for a <a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getTime--">getTime</a></span>()</code>
<div class="block">Returns the game time of the current round, where the time is equal to
 the current turn in the round.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getVelocity--">getVelocity</a></span>()</code>
<div class="block">Returns the velocity of the robot measured in pixels/turn.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width of the robot measured in pixels.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getX--">getX</a></span>()</code>
<div class="block">Returns the X position of the robot.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#getY--">getY</a></span>()</code>
<div class="block">Returns the Y position of the robot.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded</a></span>(<a href="../robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a>&nbsp;event)</code>
<div class="block">This method is called after the end of the battle, even when the battle is aborted.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit</a></span>(<a href="../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&nbsp;event)</code>
<div class="block">This method is called when one of your bullets hits another robot.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet</a></span>(<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&nbsp;event)</code>
<div class="block">This method is called when one of your bullets hits another bullet.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed</a></span>(<a href="../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when one of your bullets misses, i.e.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onDeath-robocode.DeathEvent-">onDeath</a></span>(<a href="../robocode/DeathEvent.html" title="class in robocode">DeathEvent</a>&nbsp;event)</code>
<div class="block">This method is called if your robot dies.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet</a></span>(<a href="../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot is hit by a bullet.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot</a></span>(<a href="../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot collides with another robot.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-">onHitWall</a></span>(<a href="../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot collides with a wall.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;e)</code>
<div class="block">This method is called when a key has been pressed.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;e)</code>
<div class="block">This method is called when a key has been released.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;e)</code>
<div class="block">This method is called when a key has been typed (pressed and released).</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</code>
<div class="block">This method is called when a mouse button has been clicked (pressed and
 released).</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</code>
<div class="block">This method is called when a mouse button has been pressed and then
 dragged.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</code>
<div class="block">This method is called when the mouse has entered the battle view.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</code>
<div class="block">This method is called when the mouse has exited the battle view.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</code>
<div class="block">This method is called when the mouse has been moved.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</code>
<div class="block">This method is called when a mouse button has been pressed.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</code>
<div class="block">This method is called when a mouse button has been released.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseWheelEvent.html?is-external=true" title="class or interface in java.awt.event">MouseWheelEvent</a>&nbsp;e)</code>
<div class="block">This method is called when the mouse wheel has been rotated.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onPaint-java.awt.Graphics2D-">onPaint</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Graphics2D.html?is-external=true" title="class or interface in java.awt">Graphics2D</a>&nbsp;g)</code>
<div class="block">This method is called every time the robot is painted.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath</a></span>(<a href="../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&nbsp;event)</code>
<div class="block">This method is called when another robot dies.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded</a></span>(<a href="../robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a>&nbsp;event)</code>
<div class="block">This method is called after the end of a round.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot</a></span>(<a href="../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot sees another robot, i.e.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onStatus-robocode.StatusEvent-">onStatus</a></span>(<a href="../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&nbsp;e)</code>
<div class="block">This method is called every turn in a battle round in order to provide
 the robot status as a complete snapshot of the robot's current state at
 that specific time.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#onWin-robocode.WinEvent-">onWin</a></span>(<a href="../robocode/WinEvent.html" title="class in robocode">WinEvent</a>&nbsp;event)</code>
<div class="block">This method is called if your robot wins a battle.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#resume--">resume</a></span>()</code>
<div class="block">Immediately resumes the movement you stopped by <a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, if any.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#run--">run</a></span>()</code>
<div class="block">The main method in every robot.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#scan--">scan</a></span>()</code>
<div class="block">Scans for other robots.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-">setAdjustGunForRobotTurn</a></span>(boolean&nbsp;independent)</code>
<div class="block">Sets the gun to turn independent from the robot's turn.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a></span>(boolean&nbsp;independent)</code>
<div class="block">Sets the radar to turn independent from the gun's turn.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-">setAdjustRadarForRobotTurn</a></span>(boolean&nbsp;independent)</code>
<div class="block">Sets the radar to turn independent from the robot's turn.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setAllColors-java.awt.Color-">setAllColors</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets all the robot's color to the same color in the same time, i.e.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setBodyColor-java.awt.Color-">setBodyColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's body.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setBulletColor-java.awt.Color-">setBulletColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's bullets.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;bodyColor,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;gunColor,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;radarColor)</code>
<div class="block">Sets the color of the robot's body, gun, and radar in the same time.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;bodyColor,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;gunColor,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;radarColor,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;bulletColor,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;scanArcColor)</code>
<div class="block">Sets the color of the robot's body, gun, radar, bullet, and scan arc in
 the same time.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the debug property with the specified key to the specified value.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setGunColor-java.awt.Color-">setGunColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's gun.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setRadarColor-java.awt.Color-">setRadarColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's radar.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#setScanColor-java.awt.Color-">setScanColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's scan arc.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#stop--">stop</a></span>()</code>
<div class="block">Immediately stops all movement, and saves it for a call to
 <a href="../robocode/Robot.html#resume--"><code>resume()</code></a>.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#stop-boolean-">stop</a></span>(boolean&nbsp;overwrite)</code>
<div class="block">Immediately stops all movement, and saves it for a call to
 <a href="../robocode/Robot.html#resume--"><code>resume()</code></a>.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#turnGunLeft-double-">turnGunLeft</a></span>(double&nbsp;degrees)</code>
<div class="block">Immediately turns the robot's gun to the left by degrees.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#turnGunRight-double-">turnGunRight</a></span>(double&nbsp;degrees)</code>
<div class="block">Immediately turns the robot's gun to the right by degrees.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#turnLeft-double-">turnLeft</a></span>(double&nbsp;degrees)</code>
<div class="block">Immediately turns the robot's body to the left by degrees.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#turnRadarLeft-double-">turnRadarLeft</a></span>(double&nbsp;degrees)</code>
<div class="block">Immediately turns the robot's radar to the left by degrees.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#turnRadarRight-double-">turnRadarRight</a></span>(double&nbsp;degrees)</code>
<div class="block">Immediately turns the robot's radar to the right by degrees.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Robot.html#turnRight-double-">turnRight</a></span>(double&nbsp;degrees)</code>
<div class="block">Immediately turns the robot's body to the right by degrees.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_Robot.html" title="class in robocode">_Robot</a></h3>
<code><a href="../robocode/_Robot.html#getBattleNum--">getBattleNum</a>, <a href="../robocode/_Robot.html#getGunCharge--">getGunCharge</a>, <a href="../robocode/_Robot.html#getGunImageName--">getGunImageName</a>, <a href="../robocode/_Robot.html#getLife--">getLife</a>, <a href="../robocode/_Robot.html#getNumBattles--">getNumBattles</a>, <a href="../robocode/_Robot.html#getRadarImageName--">getRadarImageName</a>, <a href="../robocode/_Robot.html#getRobotImageName--">getRobotImageName</a>, <a href="../robocode/_Robot.html#setGunImageName-java.lang.String-">setGunImageName</a>, <a href="../robocode/_Robot.html#setInterruptible-boolean-">setInterruptible</a>, <a href="../robocode/_Robot.html#setRadarImageName-java.lang.String-">setRadarImageName</a>, <a href="../robocode/_Robot.html#setRobotImageName-java.lang.String-">setRobotImageName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#finalize--">finalize</a>, <a href="../robocode/_RobotBase.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/_RobotBase.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a>, <a href="../robocode/_RobotBase.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IBasicRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Robot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Robot</h4>
<pre>public&nbsp;Robot()</pre>
<div class="block">Constructs a new robot.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRobotRunnable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobotRunnable</h4>
<pre>public final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>&nbsp;getRobotRunnable()</pre>
<div class="block">This method is called by the game to invoke the
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang"><code>run()</code></a> method of your robot, where the program
 of your robot is implemented.}</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicRobot.html#getRobotRunnable--">getRobotRunnable</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a runnable implementation</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang"><code>Runnable.run()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBasicEventListener--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBasicEventListener</h4>
<pre>public final&nbsp;<a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a>&nbsp;getBasicEventListener()</pre>
<div class="block">This method is called by the game to notify this robot about basic
 robot event. Hence, this method must be implemented so it returns your
 <a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><code>IBasicEvents</code></a> listener.}</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicRobot.html#getBasicEventListener--">getBasicEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>listener to basic events or <code>null</code> if this robot should
         not receive the notifications.</dd>
</dl>
</li>
</ul>
<a name="getInteractiveEventListener--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInteractiveEventListener</h4>
<pre>public final&nbsp;<a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>&nbsp;getInteractiveEventListener()</pre>
<div class="block">This method is called by the game to notify this robot about interactive
 events, i.e. keyboard and mouse events. Hence, this method must be
 implemented so it returns your <a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces"><code>IInteractiveEvents</code></a> listener.}</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveRobot.html#getInteractiveEventListener--">getInteractiveEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>listener to interactive events or <code>null</code> if this robot
         should not receive the notifications.</dd>
</dl>
</li>
</ul>
<a name="getPaintEventListener--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaintEventListener</h4>
<pre>public final&nbsp;<a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>&nbsp;getPaintEventListener()</pre>
<div class="block">This method is called by the game to notify this robot about painting
 events. Hence, this method must be implemented so it returns your
 <a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces"><code>IPaintEvents</code></a> listener.}</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IPaintRobot.html#getPaintEventListener--">getPaintEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>listener to paint events or <code>null</code> if this robot should
         not receive the notifications.</dd>
</dl>
</li>
</ul>
<a name="ahead-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ahead</h4>
<pre>public&nbsp;void&nbsp;ahead(double&nbsp;distance)</pre>
<div class="block">Immediately moves your robot ahead (forward) by distance measured in
 pixels.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the remaining distance to move is 0.
 <p>
 If the robot collides with a wall, the move is complete, meaning that the
 robot will not move any further. If the robot collides with another
 robot, the move is complete if you are heading toward the other robot.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot is set to move backward
 instead of forward.
 <p>
 Example:
 <pre>
   // Move the robot 100 pixels forward
   ahead(100);

   // Afterwards, move the robot 50 pixels backward
   ahead(-50);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the distance to move ahead measured in pixels.
                 If this value is negative, the robot will move back instead of ahead.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#back-double-"><code>back(double)</code></a>, 
<a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-"><code>onHitWall(HitWallEvent)</code></a>, 
<a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-"><code>onHitRobot(HitRobotEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="back-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>back</h4>
<pre>public&nbsp;void&nbsp;back(double&nbsp;distance)</pre>
<div class="block">Immediately moves your robot backward by distance measured in pixels.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the remaining distance to move is 0.
 <p>
 If the robot collides with a wall, the move is complete, meaning that the
 robot will not move any further. If the robot collides with another
 robot, the move is complete if you are heading toward the other robot.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot is set to move forward instead
 of backward.
 <p>
 Example:
 <pre>
   // Move the robot 100 pixels backward
   back(100);

   // Afterwards, move the robot 50 pixels forward
   back(-50);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the distance to move back measured in pixels.
                 If this value is negative, the robot will move ahead instead of back.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#ahead-double-"><code>ahead(double)</code></a>, 
<a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-"><code>onHitWall(HitWallEvent)</code></a>, 
<a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-"><code>onHitRobot(HitRobotEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="getBattleFieldWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattleFieldWidth</h4>
<pre>public&nbsp;double&nbsp;getBattleFieldWidth()</pre>
<div class="block">Returns the width of the current battlefield measured in pixels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the width of the current battlefield measured in pixels.</dd>
</dl>
</li>
</ul>
<a name="getBattleFieldHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattleFieldHeight</h4>
<pre>public&nbsp;double&nbsp;getBattleFieldHeight()</pre>
<div class="block">Returns the height of the current battlefield measured in pixels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the height of the current battlefield measured in pixels.</dd>
</dl>
</li>
</ul>
<a name="getHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeading</h4>
<pre>public&nbsp;double&nbsp;getHeading()</pre>
<div class="block">Returns the direction that the robot's body is facing, in degrees.
 The value returned will be between 0 and 360 (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 90 means East, 180 means South, and 270 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's body is facing, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getGunHeading--"><code>getGunHeading()</code></a>, 
<a href="../robocode/Robot.html#getRadarHeading--"><code>getRadarHeading()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;double&nbsp;getHeight()</pre>
<div class="block">Returns the height of the robot measured in pixels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the height of the robot measured in pixels.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getWidth--"><code>getWidth()</code></a></dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;double&nbsp;getWidth()</pre>
<div class="block">Returns the width of the robot measured in pixels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the width of the robot measured in pixels.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getHeight--"><code>getHeight()</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Returns the robot's name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot's name.</dd>
</dl>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;double&nbsp;getX()</pre>
<div class="block">Returns the X position of the robot. (0,0) is at the bottom left of the
 battlefield.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the X position of the robot.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getY--"><code>getY()</code></a></dd>
</dl>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;double&nbsp;getY()</pre>
<div class="block">Returns the Y position of the robot. (0,0) is at the bottom left of the
 battlefield.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Y position of the robot.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getX--"><code>getX()</code></a></dd>
</dl>
</li>
</ul>
<a name="run--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre>public&nbsp;void&nbsp;run()</pre>
<div class="block">The main method in every robot. You must override this to set up your
 robot's basic behavior.
 <p>
 Example:
 <pre>
   // A basic robot that moves around in a square
   public void run() {
       while (true) {
           ahead(100);
           turnRight(90);
       }
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang">run</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a></code></dd>
</dl>
</li>
</ul>
<a name="turnLeft-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnLeft</h4>
<pre>public&nbsp;void&nbsp;turnLeft(double&nbsp;degrees)</pre>
<div class="block">Immediately turns the robot's body to the left by degrees.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the robot's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Turn the robot 180 degrees to the left
   turnLeft(180);

   // Afterwards, turn the robot 90 degrees to the right
   turnLeft(-90);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's body to the left.
                If <code>degrees</code> &gt; 0 the robot will turn left.
                If <code>degrees</code> &lt; 0 the robot will turn right.
                If <code>degrees</code> = 0 the robot will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>turnGunRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRight</h4>
<pre>public&nbsp;void&nbsp;turnRight(double&nbsp;degrees)</pre>
<div class="block">Immediately turns the robot's body to the right by degrees.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the robot's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Turn the robot 180 degrees to the right
   turnRight(180);

   // Afterwards, turn the robot 90 degrees to the left
   turnRight(-90);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's body to the right.
                If <code>degrees</code> &gt; 0 the robot will turn right.
                If <code>degrees</code> &lt; 0 the robot will turn left.
                If <code>degrees</code> = 0 the robot will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>turnGunRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="doNothing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>doNothing</h4>
<pre>public&nbsp;void&nbsp;doNothing()</pre>
<div class="block">Do nothing this turn, meaning that the robot will skip it's turn.
 <p>
 This call executes immediately, and does not return until the turn is
 over.</div>
</li>
</ul>
<a name="fire-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fire</h4>
<pre>public&nbsp;void&nbsp;fire(double&nbsp;power)</pre>
<div class="block">Immediately fires a bullet. The bullet will travel in the direction the
 gun is pointing.
 <p>
 The specified bullet power is an amount of energy that will be taken from
 the robot's energy. Hence, the more power you want to spend on the
 bullet, the more energy is taken from your robot.
 <p>
 The bullet will do (4 * power) damage if it hits another robot. If power
 is greater than 1, it will do an additional 2 * (power - 1) damage.
 You will get (3 * power) back if you hit the other robot. You can call
 <a href="../robocode/Rules.html#getBulletDamage-double-"><code>Rules.getBulletDamage(double)</code></a> for getting the damage that a
 bullet with a specific bullet power will do.
 <p>
 The specified bullet power should be between
 <a href="../robocode/Rules.html#MIN_BULLET_POWER"><code>Rules.MIN_BULLET_POWER</code></a> and <a href="../robocode/Rules.html#MAX_BULLET_POWER"><code>Rules.MAX_BULLET_POWER</code></a>.
 <p>
 Note that the gun cannot fire if the gun is overheated, meaning that
 <a href="../robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a> returns a value &gt; 0.
 <p>
 A event is generated when the bullet hits a robot
 (<a href="../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>), wall (<a href="../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>), or another
 bullet (<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>).
 <p>
 Example:
 <pre>
   // Fire a bullet with maximum power if the gun is ready
   if (getGunHeat() == 0) {
       fire(Rules.MAX_BULLET_POWER);
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - the amount of energy given to the bullet, and subtracted
              from the robot's energy.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#fireBullet-double-"><code>fireBullet(double)</code></a>, 
<a href="../robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../robocode/Robot.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="fireBullet-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireBullet</h4>
<pre>public&nbsp;<a href="../robocode/Bullet.html" title="class in robocode">Bullet</a>&nbsp;fireBullet(double&nbsp;power)</pre>
<div class="block">Immediately fires a bullet. The bullet will travel in the direction the
 gun is pointing.
 <p>
 The specified bullet power is an amount of energy that will be taken from
 the robot's energy. Hence, the more power you want to spend on the
 bullet, the more energy is taken from your robot.
 <p>
 The bullet will do (4 * power) damage if it hits another robot. If power
 is greater than 1, it will do an additional 2 * (power - 1) damage.
 You will get (3 * power) back if you hit the other robot. You can call
 <a href="../robocode/Rules.html#getBulletDamage-double-"><code>Rules.getBulletDamage(double)</code></a> for getting the damage that a
 bullet with a specific bullet power will do.
 <p>
 The specified bullet power should be between
 <a href="../robocode/Rules.html#MIN_BULLET_POWER"><code>Rules.MIN_BULLET_POWER</code></a> and <a href="../robocode/Rules.html#MAX_BULLET_POWER"><code>Rules.MAX_BULLET_POWER</code></a>.
 <p>
 Note that the gun cannot fire if the gun is overheated, meaning that
 <a href="../robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a> returns a value &gt; 0.
 <p>
 A event is generated when the bullet hits a robot
 (<a href="../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>), wall (<a href="../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>), or another
 bullet (<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>).
 <p>
 Example:
 <pre>
   // Fire a bullet with maximum power if the gun is ready
   if (getGunHeat() == 0) {
       Bullet bullet = fireBullet(Rules.MAX_BULLET_POWER);

       // Get the velocity of the bullet
       if (bullet != null) {
           double bulletVelocity = bullet.getVelocity();
       }
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - the amount of energy given to the bullet, and subtracted
              from the robot's energy.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a> that contains information about the bullet if it
         was actually fired, which can be used for tracking the bullet after it
         has been fired. If the bullet was not fired, <code>null</code> is returned.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#fire-double-"><code>fire(double)</code></a>, 
<a href="../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a>, 
<a href="../robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../robocode/Robot.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunCoolingRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunCoolingRate</h4>
<pre>public&nbsp;double&nbsp;getGunCoolingRate()</pre>
<div class="block">Returns the rate at which the gun will cool down, i.e. the amount of heat
 the gun heat will drop per turn.
 <p>
 The gun cooling rate is default 0.1 / turn, but can be changed by the
 battle setup. So don't count on the cooling rate being 0.1!</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the gun cooling rate</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../robocode/Robot.html#fire-double-"><code>fire(double)</code></a>, 
<a href="../robocode/Robot.html#fireBullet-double-"><code>fireBullet(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeading</h4>
<pre>public&nbsp;double&nbsp;getGunHeading()</pre>
<div class="block">Returns the direction that the robot's gun is facing, in degrees.
 The value returned will be between 0 and 360 (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 90 means East, 180 means South, and 270 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's gun is facing, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getHeading--"><code>getHeading()</code></a>, 
<a href="../robocode/Robot.html#getRadarHeading--"><code>getRadarHeading()</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunHeat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeat</h4>
<pre>public&nbsp;double&nbsp;getGunHeat()</pre>
<div class="block">Returns the current heat of the gun. The gun cannot fire unless this is
 0. (Calls to fire will succeed, but will not actually fire unless
 getGunHeat() == 0).
 <p>
 The amount of gun heat generated when the gun is fired is
 1 + (firePower / 5). Each turn the gun heat drops by the amount returned
 by <a href="../robocode/Robot.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, which is a battle setup.
 <p>
 Note that all guns are "hot" at the start of each round, where the gun
 heat is 3.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current gun heat</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../robocode/Robot.html#fire-double-"><code>fire(double)</code></a>, 
<a href="../robocode/Robot.html#fireBullet-double-"><code>fireBullet(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getNumRounds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumRounds</h4>
<pre>public&nbsp;int&nbsp;getNumRounds()</pre>
<div class="block">Returns the number of rounds in the current battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds in the current battle</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getRoundNum--"><code>getRoundNum()</code></a></dd>
</dl>
</li>
</ul>
<a name="getSentryBorderSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSentryBorderSize</h4>
<pre>public&nbsp;int&nbsp;getSentryBorderSize()</pre>
<div class="block">Returns the sentry border size for a <a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the border size in units/pixels.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.9.0.0</dd>
</dl>
</li>
</ul>
<a name="getOthers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOthers</h4>
<pre>public&nbsp;int&nbsp;getOthers()</pre>
<div class="block">Returns how many opponents that are left in the current round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>how many opponents that are left in the current round.</dd>
</dl>
</li>
</ul>
<a name="getNumSentries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumSentries</h4>
<pre>public&nbsp;int&nbsp;getNumSentries()</pre>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>how many sentry robots that are left in the current round.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.9.1.0</dd>
</dl>
</li>
</ul>
<a name="getRadarHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarHeading</h4>
<pre>public&nbsp;double&nbsp;getRadarHeading()</pre>
<div class="block">Returns the direction that the robot's radar is facing, in degrees.
 The value returned will be between 0 and 360 (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 90 means East, 180 means South, and 270 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's radar is facing, in degrees.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getHeading--"><code>getHeading()</code></a>, 
<a href="../robocode/Robot.html#getGunHeading--"><code>getGunHeading()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRoundNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoundNum</h4>
<pre>public&nbsp;int&nbsp;getRoundNum()</pre>
<div class="block">Returns the current round number (0 to <a href="../robocode/Robot.html#getNumRounds--"><code>getNumRounds()</code></a> - 1) of
 the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current round number of the battle (zero indexed).</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getNumRounds--"><code>getNumRounds()</code></a></dd>
</dl>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTime</h4>
<pre>public&nbsp;long&nbsp;getTime()</pre>
<div class="block">Returns the game time of the current round, where the time is equal to
 the current turn in the round.
 <p>
 A battle consists of multiple rounds.
 <p>
 Time is reset to 0 at the beginning of every round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the game time/turn of the current round.</dd>
</dl>
</li>
</ul>
<a name="getVelocity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVelocity</h4>
<pre>public&nbsp;double&nbsp;getVelocity()</pre>
<div class="block">Returns the velocity of the robot measured in pixels/turn.
 <p>
 The maximum velocity of a robot is defined by <a href="../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a>
 (8 pixels / turn).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the velocity of the robot measured in pixels/turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a></dd>
</dl>
</li>
</ul>
<a name="onBulletHit-robocode.BulletHitEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBulletHit</h4>
<pre>public&nbsp;void&nbsp;onBulletHit(<a href="../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when one of your bullets hits another robot.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   public void onBulletHit(BulletHitEvent event) {
       out.println("I hit " + event.getName() + "!");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the bullet-hit event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onBulletHitBullet-robocode.BulletHitBulletEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBulletHitBullet</h4>
<pre>public&nbsp;void&nbsp;onBulletHitBullet(<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when one of your bullets hits another bullet.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   public void onBulletHitBullet(BulletHitBulletEvent event) {
       out.println("I hit a bullet fired by " + event.getBullet().getName() + "!");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the bullet-hit-bullet event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onBulletMissed-robocode.BulletMissedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBulletMissed</h4>
<pre>public&nbsp;void&nbsp;onBulletMissed(<a href="../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when one of your bullets misses, i.e. hits a wall.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   public void onBulletMissed(BulletMissedEvent event) {
       out.println("Drat, I missed.");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the bullet-missed event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onDeath-robocode.DeathEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDeath</h4>
<pre>public&nbsp;void&nbsp;onDeath(<a href="../robocode/DeathEvent.html" title="class in robocode">DeathEvent</a>&nbsp;event)</pre>
<div class="block">This method is called if your robot dies.
 <p>
 You should override it in your robot if you want to be informed of this
 event. Actions will have no effect if called from this section. The
 intent is to allow you to perform calculations or print something out
 when the robot is killed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onDeath-robocode.DeathEvent-">onDeath</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the death event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>, 
<a href="../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>, 
<a href="../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>, 
<a href="../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitByBullet-robocode.HitByBulletEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitByBullet</h4>
<pre>public&nbsp;void&nbsp;onHitByBullet(<a href="../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot is hit by a bullet.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   void onHitByBullet(HitByBulletEvent event) {
       out.println(event.getRobotName() + " hit me!");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the hit-by-bullet event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitRobot-robocode.HitRobotEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitRobot</h4>
<pre>public&nbsp;void&nbsp;onHitRobot(<a href="../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot collides with another robot.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   void onHitRobot(HitRobotEvent event) {
       if (event.getBearing() &gt; -90 &amp;&amp; event.getBearing() &lt;= 90) {
           back(100);
       } else {
           ahead(100);
       }
   }
 </pre><p>
   -- or perhaps, for a more advanced robot --
 </p><pre>
   public void onHitRobot(HitRobotEvent event) {
       if (event.getBearing() &gt; -90 &amp;&amp; event.getBearing() &lt;= 90) {
           setBack(100);
       } else {
           setAhead(100);
       }
   }
 </pre>
 <p>
 The angle is relative to your robot's facing. So 0 is straight ahead of
 you.
 <p>
 This event can be generated if another robot hits you, in which case
 <a href="../robocode/HitRobotEvent.html#isMyFault--"><code>event.isMyFault()</code></a> will return
 <code>false</code>. In this case, you will not be automatically stopped by the
 game -- but if you continue moving toward the robot you will hit it (and
 generate another event). If you are moving away, then you won't hit it.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the hit-robot event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitWall-robocode.HitWallEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitWall</h4>
<pre>public&nbsp;void&nbsp;onHitWall(<a href="../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot collides with a wall.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 The wall at the top of the screen is 0 degrees, right is 90 degrees,
 bottom is 180 degrees, left is 270 degrees. But this event is relative to
 your heading, so: The bearing is such that <a href="../robocode/Robot.html#turnRight-double-"><code>turnRight</code></a> <a href="../robocode/HitWallEvent.html#getBearing--"><code>(event.getBearing())</code></a> will
 point you perpendicular to the wall.
 <p>
 Example:
 <pre>
   void onHitWall(HitWallEvent event) {
       out.println("Ouch, I hit a wall bearing " + event.getBearing() + " degrees.");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onHitWall-robocode.HitWallEvent-">onHitWall</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the hit-wall event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onRobotDeath-robocode.RobotDeathEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onRobotDeath</h4>
<pre>public&nbsp;void&nbsp;onRobotDeath(<a href="../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when another robot dies.
 You should override it in your robot if you want to be informed of this
 event.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - The robot-death event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onScannedRobot-robocode.ScannedRobotEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onScannedRobot</h4>
<pre>public&nbsp;void&nbsp;onScannedRobot(<a href="../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot sees another robot, i.e. when the
 robot's radar scan "hits" another robot.
 You should override it in your robot if you want to be informed of this
 event. (Almost all robots should override this!)
 <p>
 This event is automatically called if there is a robot in range of your
 radar.
 <p>
 Note that the robot's radar can only see robot within the range defined
 by <a href="../robocode/Rules.html#RADAR_SCAN_RADIUS"><code>Rules.RADAR_SCAN_RADIUS</code></a> (1200 pixels).
 <p>
 Also not that the bearing of the scanned robot is relative to your
 robot's heading.
 <p>
 Example:
 <pre>
   void onScannedRobot(ScannedRobotEvent event) {
       // Assuming radar and gun are aligned...
       if (event.getDistance() &lt; 100) {
           fire(3);
       } else {
           fire(1);
       }
   }
 </pre>
 <p>
 <b>Note:</b><br>
 The game assists Robots in firing, as follows:
 <ul>
 <li>If the gun and radar are aligned (and were aligned last turn),
 <li>and the event is current,
 <li>and you call fire() before taking any other actions, <a href="../robocode/Robot.html#fire-double-"><code>fire()</code></a> will fire directly at the robot.
 </ul>
 <p>
 In essence, this means that if you can see a robot, and it doesn't move,
 then fire will hit it.
 <p>
 AdvancedRobots will NOT be assisted in this manner, and are expected to
 examine the event to determine if <a href="../robocode/Robot.html#fire-double-"><code>fire()</code></a> would
 hit. (i.e. you are spinning your gun around, but by the time you get the
 event, your gun is 5 degrees past the robot).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the scanned-robot event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a>, 
<a href="../robocode/Rules.html#RADAR_SCAN_RADIUS"><code>Rules.RADAR_SCAN_RADIUS</code></a></dd>
</dl>
</li>
</ul>
<a name="onWin-robocode.WinEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onWin</h4>
<pre>public&nbsp;void&nbsp;onWin(<a href="../robocode/WinEvent.html" title="class in robocode">WinEvent</a>&nbsp;event)</pre>
<div class="block">This method is called if your robot wins a battle.
 <p>
 Your robot could perform a victory dance here! :-)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onWin-robocode.WinEvent-">onWin</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the win event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>, 
<a href="../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>, 
<a href="../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onRoundEnded-robocode.RoundEndedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onRoundEnded</h4>
<pre>public&nbsp;void&nbsp;onRoundEnded(<a href="../robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called after the end of a round.
 You should override it in your robot if you want to be informed of this event.
 <p>
 Example:
 <pre>
   public void onRoundEnded(RoundEndedEvent event) {
       out.println("The round has ended");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents3.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the RoundEndedEvent event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>, 
<a href="../robocode/robotinterfaces/IBasicEvents2.html#onBattleEnded-robocode.BattleEndedEvent-"><code>IBasicEvents2.onBattleEnded(BattleEndedEvent)</code></a>, 
<a href="../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>, 
<a href="../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onBattleEnded-robocode.BattleEndedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBattleEnded</h4>
<pre>public&nbsp;void&nbsp;onBattleEnded(<a href="../robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called after the end of the battle, even when the battle is aborted.
 You should override it in your robot if you want to be informed of this event.
 <p>
 Example:
 <pre>
   public void onBattleEnded(BattleEndedEvent event) {
       out.println("The battle has ended");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents2.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the BattleEndedEvent set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>, 
<a href="../robocode/robotinterfaces/IBasicEvents3.html#onRoundEnded-robocode.RoundEndedEvent-"><code>IBasicEvents3.onRoundEnded(RoundEndedEvent)</code></a>, 
<a href="../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>, 
<a href="../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="scan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scan</h4>
<pre>public&nbsp;void&nbsp;scan()</pre>
<div class="block">Scans for other robots. This method is called automatically by the game,
 as long as the robot is moving, turning its body, turning its gun, or
 turning its radar.
 <p>
 Scan will cause <a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a> to be called if you see a robot.
 <p>
 There are 2 reasons to call <code>scan()</code> manually:
 <ol>
 <li>You want to scan after you stop moving.
 <li>You want to interrupt the <code>onScannedRobot</code> event. This is more
 likely. If you are in <code>onScannedRobot</code> and call <code>scan()</code>,
 and you still see a robot, then the system will interrupt your
 <code>onScannedRobot</code> event immediately and start it from the top.
 </ol>
 <p>
 This call executes immediately.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a>, 
<a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdjustGunForRobotTurn-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdjustGunForRobotTurn</h4>
<pre>public&nbsp;void&nbsp;setAdjustGunForRobotTurn(boolean&nbsp;independent)</pre>
<div class="block">Sets the gun to turn independent from the robot's turn.
 <p>
 Ok, so this needs some explanation: The gun is mounted on the robot's
 body. So, normally, if the robot turns 90 degrees to the right, then the
 gun will turn with it as it is mounted on top of the robot's body. To
 compensate for this, you can call <code>setAdjustGunForRobotTurn(true)</code>.
 When this is set, the gun will turn independent from the robot's turn,
 i.e. the gun will compensate for the robot's body turn.
 <p>
 Note: This method is additive until you reach the maximum the gun can
 turn. The "adjust" is added to the amount you set for turning the robot,
 then capped by the physics of the game. If you turn infinite, then the
 adjust is ignored (and hence overridden).
 <p>
 Example, assuming both the robot and gun start out facing up (0 degrees):
 <pre>
   // Set gun to turn with the robot's turn
   setAdjustGunForRobotTurn(false); // This is the default
   turnRight(90);
   // At this point, both the robot and gun are facing right (90 degrees)
   turnLeft(90);
   // Both are back to 0 degrees

   -- or --

   // Set gun to turn independent from the robot's turn
   setAdjustGunForRobotTurn(true);
   turnRight(90);
   // At this point, the robot is facing right (90 degrees), but the gun is still facing up.
   turnLeft(90);
   // Both are back to 0 degrees.
 </pre>
 <p>
 Note: The gun compensating this way does count as "turning the gun".
 See <a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a> for details.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>independent</code> - <code>true</code> if the gun must turn independent from the
                    robot's turn; <code>false</code> if the gun must turn with the robot's turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdjustRadarForRobotTurn-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdjustRadarForRobotTurn</h4>
<pre>public&nbsp;void&nbsp;setAdjustRadarForRobotTurn(boolean&nbsp;independent)</pre>
<div class="block">Sets the radar to turn independent from the robot's turn.
 <p>
 Ok, so this needs some explanation: The radar is mounted on the gun, and
 the gun is mounted on the robot's body. So, normally, if the robot turns
 90 degrees to the right, the gun turns, as does the radar. Hence, if the
 robot turns 90 degrees to the right, then the gun and radar will turn
 with it as the radar is mounted on top of the gun. To compensate for
 this, you can call <code>setAdjustRadarForRobotTurn(true)</code>. When this is
 set, the radar will turn independent from the robot's turn, i.e. the
 radar will compensate for the robot's turn.
 <p>
 Note: This method is additive until you reach the maximum the radar can
 turn. The "adjust" is added to the amount you set for turning the robot,
 then capped by the physics of the game. If you turn infinite, then the
 adjust is ignored (and hence overridden).
 <p>
 Example, assuming the robot, gun, and radar all start out facing up (0
 degrees):
 <pre>
   // Set radar to turn with the robots's turn
   setAdjustRadarForRobotTurn(false); // This is the default
   turnRight(90);
   // At this point, the body, gun, and radar are all facing right (90 degrees);

   -- or --

   // Set radar to turn independent from the robot's turn
   setAdjustRadarForRobotTurn(true);
   turnRight(90);
   // At this point, the robot and gun are facing right (90 degrees), but the radar is still facing up.
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>independent</code> - <code>true</code> if the radar must turn independent from
                    the robots's turn; <code>false</code> if the radar must turn with the robot's
                    turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>setAdjustGunForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdjustRadarForGunTurn-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdjustRadarForGunTurn</h4>
<pre>public&nbsp;void&nbsp;setAdjustRadarForGunTurn(boolean&nbsp;independent)</pre>
<div class="block">Sets the radar to turn independent from the gun's turn.
 <p>
 Ok, so this needs some explanation: The radar is mounted on the robot's
 gun. So, normally, if the gun turns 90 degrees to the right, then the
 radar will turn with it as it is mounted on top of the gun. To compensate
 for this, you can call <code>setAdjustRadarForGunTurn(true)</code>. When this
 is set, the radar will turn independent from the robot's turn, i.e. the
 radar will compensate for the gun's turn.
 <p>
 Note: This method is additive until you reach the maximum the radar can
 turn. The "adjust" is added to the amount you set for turning the gun,
 then capped by the physics of the game. If you turn infinite, then the
 adjust is ignored (and hence overridden).
 <p>
 Example, assuming both the gun and radar start out facing up (0 degrees):
 <pre>
   // Set radar to turn with the gun's turn
   setAdjustRadarForGunTurn(false); // This is the default
   turnGunRight(90);
   // At this point, both the radar and gun are facing right (90 degrees);

   -- or --

   // Set radar to turn independent from the gun's turn
   setAdjustRadarForGunTurn(true);
   turnGunRight(90);
   // At this point, the gun is facing right (90 degrees), but the radar is still facing up.
 </pre>
 Note: Calling <code>setAdjustRadarForGunTurn(boolean)</code> will
 automatically call <a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>setAdjustRadarForRobotTurn(boolean)</code></a> with the
 same value, unless you have already called it earlier. This behavior is
 primarily for backward compatibility with older Robocode robots.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>independent</code> - <code>true</code> if the radar must turn independent from
                    the gun's turn; <code>false</code> if the radar must turn with the gun's
                    turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setColors-java.awt.Color-java.awt.Color-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColors</h4>
<pre>public&nbsp;void&nbsp;setColors(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;bodyColor,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;gunColor,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;radarColor)</pre>
<div class="block">Sets the color of the robot's body, gun, and radar in the same time.
 <p>
 A <code>null</code> indicates the default (blue) color.
 <p>
 Example:
 <pre>
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setColors(null, Color.RED, new Color(150, 0, 150));
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bodyColor</code> - the new body color</dd>
<dd><code>gunColor</code> - the new gun color</dd>
<dd><code>radarColor</code> - the new radar color</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setAllColors-java.awt.Color-"><code>setAllColors(Color)</code></a>, 
<a href="../robocode/Robot.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColors</h4>
<pre>public&nbsp;void&nbsp;setColors(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;bodyColor,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;gunColor,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;radarColor,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;bulletColor,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;scanArcColor)</pre>
<div class="block">Sets the color of the robot's body, gun, radar, bullet, and scan arc in
 the same time.
 <p>
 A <code>null</code> indicates the default (blue) color for the body, gun,
 radar, and scan arc, but white for the bullet color.
 <p>
 Example:
 <pre>
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setColors(null, Color.RED, Color.GREEN, null, new Color(150, 0, 150));
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bodyColor</code> - the new body color</dd>
<dd><code>gunColor</code> - the new gun color</dd>
<dd><code>radarColor</code> - the new radar color</dd>
<dd><code>bulletColor</code> - the new bullet color</dd>
<dd><code>scanArcColor</code> - the new scan arc color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setAllColors-java.awt.Color-"><code>setAllColors(Color)</code></a>, 
<a href="../robocode/Robot.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setAllColors-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllColors</h4>
<pre>public&nbsp;void&nbsp;setAllColors(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets all the robot's color to the same color in the same time, i.e. the
 color of the body, gun, radar, bullet, and scan arc.
 <p>
 A <code>null</code> indicates the default (blue) color for the body, gun,
 radar, and scan arc, but white for the bullet color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setAllColors(Color.RED);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new color for all the colors of the robot</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.3</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setBodyColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBodyColor</h4>
<pre>public&nbsp;void&nbsp;setBodyColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's body.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setBodyColor(Color.BLACK);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new body color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setAllColors-java.awt.Color-"><code>setAllColors(Color)</code></a>, 
<a href="../robocode/Robot.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setGunColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGunColor</h4>
<pre>public&nbsp;void&nbsp;setGunColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's gun.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setGunColor(Color.RED);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new gun color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setAllColors-java.awt.Color-"><code>setAllColors(Color)</code></a>, 
<a href="../robocode/Robot.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setRadarColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRadarColor</h4>
<pre>public&nbsp;void&nbsp;setRadarColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's radar.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setRadarColor(Color.YELLOW);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new radar color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setAllColors-java.awt.Color-"><code>setAllColors(Color)</code></a>, 
<a href="../robocode/Robot.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setBulletColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBulletColor</h4>
<pre>public&nbsp;void&nbsp;setBulletColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's bullets.
 <p>
 A <code>null</code> indicates the default white color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setBulletColor(Color.GREEN);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new bullet color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setAllColors-java.awt.Color-"><code>setAllColors(Color)</code></a>, 
<a href="../robocode/Robot.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setScanColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanColor</h4>
<pre>public&nbsp;void&nbsp;setScanColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's scan arc.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setScanColor(Color.WHITE);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new scan arc color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-"><code>setColors(Color, Color, Color, Color, Color)</code></a>, 
<a href="../robocode/Robot.html#setAllColors-java.awt.Color-"><code>setAllColors(Color)</code></a>, 
<a href="../robocode/Robot.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../robocode/Robot.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="stop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stop</h4>
<pre>public&nbsp;void&nbsp;stop()</pre>
<div class="block">Immediately stops all movement, and saves it for a call to
 <a href="../robocode/Robot.html#resume--"><code>resume()</code></a>. If there is already movement saved from a previous
 stop, this will have no effect.
 <p>
 This method is equivalent to <code>#stop(false)</code>.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#resume--"><code>resume()</code></a>, 
<a href="../robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="stop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stop</h4>
<pre>public&nbsp;void&nbsp;stop(boolean&nbsp;overwrite)</pre>
<div class="block">Immediately stops all movement, and saves it for a call to
 <a href="../robocode/Robot.html#resume--"><code>resume()</code></a>. If there is already movement saved from a previous
 stop, you can overwrite it by calling <code>stop(true)</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overwrite</code> - If there is already movement saved from a previous stop,
                  you can overwrite it by calling <code>stop(true)</code>.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#resume--"><code>resume()</code></a>, 
<a href="../robocode/Robot.html#stop--"><code>stop()</code></a></dd>
</dl>
</li>
</ul>
<a name="resume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resume</h4>
<pre>public&nbsp;void&nbsp;resume()</pre>
<div class="block">Immediately resumes the movement you stopped by <a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, if any.
 <p>
 This call executes immediately, and does not return until it is complete.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, 
<a href="../robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGunLeft-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunLeft</h4>
<pre>public&nbsp;void&nbsp;turnGunLeft(double&nbsp;degrees)</pre>
<div class="block">Immediately turns the robot's gun to the left by degrees.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the gun's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Turn the robot's gun 180 degrees to the left
   turnGunLeft(180);

   // Afterwards, turn the robot's gun 90 degrees to the right
   turnGunLeft(-90);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's gun to the left.
                If <code>degrees</code> &gt; 0 the robot's gun will turn left.
                If <code>degrees</code> &lt; 0 the robot's gun will turn right.
                If <code>degrees</code> = 0 the robot's gun will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnGunRight-double-"><code>turnGunRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGunRight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunRight</h4>
<pre>public&nbsp;void&nbsp;turnGunRight(double&nbsp;degrees)</pre>
<div class="block">Immediately turns the robot's gun to the right by degrees.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the gun's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Turn the robot's gun 180 degrees to the right
   turnGunRight(180);

   // Afterwards, turn the robot's gun 90 degrees to the left
   turnGunRight(-90);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's gun to the right.
                If <code>degrees</code> &gt; 0 the robot's gun will turn right.
                If <code>degrees</code> &lt; 0 the robot's gun will turn left.
                If <code>degrees</code> = 0 the robot's gun will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRadarLeft-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRadarLeft</h4>
<pre>public&nbsp;void&nbsp;turnRadarLeft(double&nbsp;degrees)</pre>
<div class="block">Immediately turns the robot's radar to the left by degrees.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the radar's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Turn the robot's radar 180 degrees to the left
   turnRadarLeft(180);

   // Afterwards, turn the robot's radar 90 degrees to the right
   turnRadarLeft(-90);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's radar to the left.
                If <code>degrees</code> &gt; 0 the robot's radar will turn left.
                If <code>degrees</code> &lt; 0 the robot's radar will turn right.
                If <code>degrees</code> = 0 the robot's radar will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>turnGunRight(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRadarRight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRadarRight</h4>
<pre>public&nbsp;void&nbsp;turnRadarRight(double&nbsp;degrees)</pre>
<div class="block">Immediately turns the robot's radar to the right by degrees.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the radar's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Turn the robot's radar 180 degrees to the right
   turnRadarRight(180);

   // Afterwards, turn the robot's radar 90 degrees to the left
   turnRadarRight(-90);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's radar to the right.
                If <code>degrees</code> &gt; 0 the robot's radar will turn right.
                If <code>degrees</code> &lt; 0 the robot's radar will turn left.
                If <code>degrees</code> = 0 the robot's radar will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>turnGunRight(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="getEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnergy</h4>
<pre>public&nbsp;double&nbsp;getEnergy()</pre>
<div class="block">Returns the robot's current energy.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot's current energy.</dd>
</dl>
</li>
</ul>
<a name="getGraphics--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGraphics</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Graphics2D.html?is-external=true" title="class or interface in java.awt">Graphics2D</a>&nbsp;getGraphics()</pre>
<div class="block">Returns a graphics context used for painting graphical items for the robot.
 <p>
 This method is very useful for debugging your robot.
 <p>
 Note that the robot will only be painted if the "Paint" is enabled on the
 robot's console window; otherwise the robot will never get painted (the
 reason being that all robots might have graphical items that must be
 painted, and then you might not be able to tell what graphical items that
 have been painted for your robot).
 <p>
 Also note that the coordinate system for the graphical context where you
 paint items fits for the Robocode coordinate system where (0, 0) is at
 the bottom left corner of the battlefield, where X is towards right and Y
 is upwards.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a graphics context used for painting graphical items for the robot.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.1</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onPaint-java.awt.Graphics2D-"><code>onPaint(Graphics2D)</code></a></dd>
</dl>
</li>
</ul>
<a name="setDebugProperty-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDebugProperty</h4>
<pre>public&nbsp;void&nbsp;setDebugProperty(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
                             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the debug property with the specified key to the specified value.
 <p>
 This method is very useful when debugging or reviewing your robot as you
 will be able to see this property displayed in the robot console for your
 robots under the Debug Properties tab page.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - the name/key of the debug property.</dd>
<dd><code>value</code> - the new value of the debug property, where <code>null</code> or
              the empty string is used for removing this debug property.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
</dl>
</li>
</ul>
<a name="onPaint-java.awt.Graphics2D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onPaint</h4>
<pre>public&nbsp;void&nbsp;onPaint(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Graphics2D.html?is-external=true" title="class or interface in java.awt">Graphics2D</a>&nbsp;g)</pre>
<div class="block">This method is called every time the robot is painted. You should
 override this method if you want to draw items for your robot on the
 battle field, e.g. targets, virtual bullets etc.
 <p>
 This method is very useful for debugging your robot.
 <p>
 Note that the robot will only be painted if the "Paint" is enabled on the
 robot's console window; otherwise the robot will never get painted (the
 reason being that all robots might have graphical items that must be
 painted, and then you might not be able to tell what graphical items that
 have been painted for your robot).
 <p>
 Also note that the coordinate system for the graphical context where you
 paint items fits for the Robocode coordinate system where (0, 0) is at
 the bottom left corner of the battlefield, where X is towards right and Y
 is upwards.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IPaintEvents.html#onPaint-java.awt.Graphics2D-">onPaint</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>g</code> - the graphics context to use for painting graphical items for the
          robot.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Graphics2D.html?is-external=true" title="class or interface in java.awt"><code>Graphics2D</code></a></dd>
</dl>
</li>
</ul>
<a name="onKeyPressed-java.awt.event.KeyEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onKeyPressed</h4>
<pre>public&nbsp;void&nbsp;onKeyPressed(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when a key has been pressed.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 key events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyListener.html?is-external=true#keyPressed-java.awt.event.KeyEvent-" title="class or interface in java.awt.event"><code>KeyListener.keyPressed(KeyEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyReleased-java.awt.event.KeyEvent-"><code>IInteractiveEvents.onKeyReleased(KeyEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyTyped-java.awt.event.KeyEvent-"><code>IInteractiveEvents.onKeyTyped(KeyEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onKeyReleased-java.awt.event.KeyEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onKeyReleased</h4>
<pre>public&nbsp;void&nbsp;onKeyReleased(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when a key has been released.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 key events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyListener.html?is-external=true#keyReleased-java.awt.event.KeyEvent-" title="class or interface in java.awt.event"><code>KeyListener.keyReleased(KeyEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyPressed-java.awt.event.KeyEvent-"><code>IInteractiveEvents.onKeyPressed(KeyEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyTyped-java.awt.event.KeyEvent-"><code>IInteractiveEvents.onKeyTyped(KeyEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onKeyTyped-java.awt.event.KeyEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onKeyTyped</h4>
<pre>public&nbsp;void&nbsp;onKeyTyped(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when a key has been typed (pressed and released).
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 key events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyListener.html?is-external=true#keyTyped-java.awt.event.KeyEvent-" title="class or interface in java.awt.event"><code>KeyListener.keyTyped(KeyEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyPressed-java.awt.event.KeyEvent-"><code>IInteractiveEvents.onKeyPressed(KeyEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onKeyReleased-java.awt.event.KeyEvent-"><code>IInteractiveEvents.onKeyReleased(KeyEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseClicked-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseClicked</h4>
<pre>public&nbsp;void&nbsp;onMouseClicked(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when a mouse button has been clicked (pressed and
 released).
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseClicked-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseDragged(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>IInteractiveEvents.onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseEntered-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseEntered</h4>
<pre>public&nbsp;void&nbsp;onMouseEntered(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when the mouse has entered the battle view.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseEntered-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseDragged(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>IInteractiveEvents.onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseExited-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseExited</h4>
<pre>public&nbsp;void&nbsp;onMouseExited(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when the mouse has exited the battle view.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseExited-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseDragged(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>IInteractiveEvents.onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMousePressed-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMousePressed</h4>
<pre>public&nbsp;void&nbsp;onMousePressed(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when a mouse button has been pressed.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mousePressed-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseDragged(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>IInteractiveEvents.onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseReleased-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseReleased</h4>
<pre>public&nbsp;void&nbsp;onMouseReleased(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when a mouse button has been released.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseReleased-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseDragged(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>IInteractiveEvents.onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseMoved-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseMoved</h4>
<pre>public&nbsp;void&nbsp;onMouseMoved(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when the mouse has been moved.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseMotionListener.html?is-external=true#mouseMoved-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseMotionListener.mouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseDragged(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>IInteractiveEvents.onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseDragged-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseDragged</h4>
<pre>public&nbsp;void&nbsp;onMouseDragged(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when a mouse button has been pressed and then
 dragged.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseMotionListener.html?is-external=true#mouseDragged-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseMotionListener.mouseDragged(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>IInteractiveEvents.onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseWheelMoved-java.awt.event.MouseWheelEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseWheelMoved</h4>
<pre>public&nbsp;void&nbsp;onMouseWheelMoved(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseWheelEvent.html?is-external=true" title="class or interface in java.awt.event">MouseWheelEvent</a>&nbsp;e)</pre>
<div class="block">This method is called when the mouse wheel has been rotated.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseWheelListener.html?is-external=true#mouseWheelMoved-java.awt.event.MouseWheelEvent-" title="class or interface in java.awt.event"><code>MouseWheelListener.mouseWheelMoved(MouseWheelEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseMoved(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMousePressed(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseReleased(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseClicked(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseEntered(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseExited(MouseEvent)</code></a>, 
<a href="../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>IInteractiveEvents.onMouseDragged(MouseEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onStatus-robocode.StatusEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onStatus</h4>
<pre>public&nbsp;void&nbsp;onStatus(<a href="../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&nbsp;e)</pre>
<div class="block">This method is called every turn in a battle round in order to provide
 the robot status as a complete snapshot of the robot's current state at
 that specific time.
 <p>
 The main benefit of this method is that you'll automatically receive all
 current data values of the robot like e.g. the x and y coordinate,
 heading, gun heat etc., which are grouped into the exact same time/turn.
 <p>
 This is the only way to map the robots data values to a specific time.
 For example, it is not possible to determine the exact time of the
 robot's heading by calling first calling <a href="../robocode/Robot.html#getTime--"><code>getTime()</code></a> and then
 <a href="../robocode/Robot.html#getHeading--"><code>getHeading()</code></a> afterwards, as the time <em>might</em> change
 after between the <a href="../robocode/Robot.html#getTime--"><code>getTime()</code></a> and <a href="../robocode/Robot.html#getHeading--"><code>getHeading()</code></a>
 call.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onStatus-robocode.StatusEvent-">onStatus</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - the event containing the robot status at the time it occurred.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/RobotDeathEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/Robot.html" target="_top">Frames</a></li>
<li><a href="Robot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
