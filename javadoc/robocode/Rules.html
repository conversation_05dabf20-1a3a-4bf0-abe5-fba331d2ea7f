<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Rules (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Rules (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RoundEndedEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/Rules.html" target="_top">Frames</a></li>
<li><a href="Rules.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class Rules" class="title">Class Rules</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>robocode.Rules</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Rules</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Constants and methods that defines the rules of Robocode.
 Constants are used for rules that will not change.
 Methods are provided for rules that can be changed between battles or which depends
 on some other factor.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.4</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Luis Crespo (original), Flemming N. Larsen (original)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#ACCELERATION">ACCELERATION</a></span></code>
<div class="block">The acceleration of a robot, i.e.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#DECELERATION">DECELERATION</a></span></code>
<div class="block">The deceleration of a robot, i.e.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#GUN_TURN_RATE">GUN_TURN_RATE</a></span></code>
<div class="block">The turning rate of the gun measured in degrees, which is
 20 degrees/turn.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#GUN_TURN_RATE_RADIANS">GUN_TURN_RATE_RADIANS</a></span></code>
<div class="block">The turning rate of the gun measured in radians instead of degrees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#MAX_BULLET_POWER">MAX_BULLET_POWER</a></span></code>
<div class="block">The maximum bullet power, i.e.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#MAX_TURN_RATE">MAX_TURN_RATE</a></span></code>
<div class="block">The maximum turning rate of the robot, in degrees, which is
 10 degress/turn.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#MAX_TURN_RATE_RADIANS">MAX_TURN_RATE_RADIANS</a></span></code>
<div class="block">The maximum turning rate of the robot measured in radians instead of
 degrees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#MAX_VELOCITY">MAX_VELOCITY</a></span></code>
<div class="block">The maximum velocity of a robot, which is 8 pixels/turn.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#MIN_BULLET_POWER">MIN_BULLET_POWER</a></span></code>
<div class="block">The minimum bullet power, i.e the amount of energy required for firing a
 bullet, which is 0.1 energy points.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#RADAR_SCAN_RADIUS">RADAR_SCAN_RADIUS</a></span></code>
<div class="block">The radar scan radius, which is 1200 pixels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#RADAR_TURN_RATE">RADAR_TURN_RATE</a></span></code>
<div class="block">The turning rate of the radar measured in degrees, which is
 45 degrees/turn.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#RADAR_TURN_RATE_RADIANS">RADAR_TURN_RATE_RADIANS</a></span></code>
<div class="block">The turning rate of the radar measured in radians instead of degrees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#ROBOT_HIT_BONUS">ROBOT_HIT_BONUS</a></span></code>
<div class="block">The amount of bonus damage dealt by a robot ramming an opponent by moving forward into it,
 which is 2 x <a href="../robocode/Rules.html#ROBOT_HIT_DAMAGE"><code>ROBOT_HIT_DAMAGE</code></a> = 1.2 energy points.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#ROBOT_HIT_DAMAGE">ROBOT_HIT_DAMAGE</a></span></code>
<div class="block">The amount of damage taken when a robot hits or is hit by another robot,
 which is 0.6 energy points.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#getBulletDamage-double-">getBulletDamage</a></span>(double&nbsp;bulletPower)</code>
<div class="block">Returns the amount of damage of a bullet given a specific bullet power.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#getBulletHitBonus-double-">getBulletHitBonus</a></span>(double&nbsp;bulletPower)</code>
<div class="block">Returns the amount of bonus given when a robot's bullet hits an opponent
 robot given a specific bullet power.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#getBulletSpeed-double-">getBulletSpeed</a></span>(double&nbsp;bulletPower)</code>
<div class="block">Returns the speed of a bullet given a specific bullet power measured in pixels/turn.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#getGunHeat-double-">getGunHeat</a></span>(double&nbsp;bulletPower)</code>
<div class="block">Returns the heat produced by firing the gun given a specific bullet
 power.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#getTurnRate-double-">getTurnRate</a></span>(double&nbsp;velocity)</code>
<div class="block">Returns the turn rate of a robot given a specific velocity measured in
 degrees/turn.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#getTurnRateRadians-double-">getTurnRateRadians</a></span>(double&nbsp;velocity)</code>
<div class="block">Returns the turn rate of a robot given a specific velocity measured in
 radians/turn.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/Rules.html#getWallHitDamage-double-">getWallHitDamage</a></span>(double&nbsp;velocity)</code>
<div class="block">Returns the amount of damage taken when robot hits a wall with a
 specific velocity.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ACCELERATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACCELERATION</h4>
<pre>public static final&nbsp;double ACCELERATION</pre>
<div class="block">The acceleration of a robot, i.e. the increase of velocity when the
 robot moves forward, which is 1 pixel/turn.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.ACCELERATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECELERATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECELERATION</h4>
<pre>public static final&nbsp;double DECELERATION</pre>
<div class="block">The deceleration of a robot, i.e. the decrease of velocity when the
 robot moves backwards (or brakes), which is 2 pixels/turn.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.DECELERATION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MAX_VELOCITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAX_VELOCITY</h4>
<pre>public static final&nbsp;double MAX_VELOCITY</pre>
<div class="block">The maximum velocity of a robot, which is 8 pixels/turn.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.MAX_VELOCITY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RADAR_SCAN_RADIUS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RADAR_SCAN_RADIUS</h4>
<pre>public static final&nbsp;double RADAR_SCAN_RADIUS</pre>
<div class="block">The radar scan radius, which is 1200 pixels.
 Robots which is more than 1200 pixels away cannot be seen on the radar.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.RADAR_SCAN_RADIUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MIN_BULLET_POWER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIN_BULLET_POWER</h4>
<pre>public static final&nbsp;double MIN_BULLET_POWER</pre>
<div class="block">The minimum bullet power, i.e the amount of energy required for firing a
 bullet, which is 0.1 energy points.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.MIN_BULLET_POWER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MAX_BULLET_POWER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAX_BULLET_POWER</h4>
<pre>public static final&nbsp;double MAX_BULLET_POWER</pre>
<div class="block">The maximum bullet power, i.e. the maximum amount of energy that can be
 transferred to a bullet when it is fired, which is 3 energy points.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.MAX_BULLET_POWER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MAX_TURN_RATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAX_TURN_RATE</h4>
<pre>public static final&nbsp;double MAX_TURN_RATE</pre>
<div class="block">The maximum turning rate of the robot, in degrees, which is
 10 degress/turn.
 Note, that the turn rate of the robot depends on it's velocity.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#MAX_TURN_RATE_RADIANS"><code>MAX_TURN_RATE_RADIANS</code></a>, 
<a href="../robocode/Rules.html#getTurnRate-double-"><code>getTurnRate(double)</code></a>, 
<a href="../robocode/Rules.html#getTurnRateRadians-double-"><code>getTurnRateRadians(double)</code></a>, 
<a href="../constant-values.html#robocode.Rules.MAX_TURN_RATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MAX_TURN_RATE_RADIANS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAX_TURN_RATE_RADIANS</h4>
<pre>public static final&nbsp;double MAX_TURN_RATE_RADIANS</pre>
<div class="block">The maximum turning rate of the robot measured in radians instead of
 degrees.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#MAX_TURN_RATE"><code>MAX_TURN_RATE</code></a>, 
<a href="../robocode/Rules.html#getTurnRate-double-"><code>getTurnRate(double)</code></a>, 
<a href="../robocode/Rules.html#getTurnRateRadians-double-"><code>getTurnRateRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="GUN_TURN_RATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GUN_TURN_RATE</h4>
<pre>public static final&nbsp;double GUN_TURN_RATE</pre>
<div class="block">The turning rate of the gun measured in degrees, which is
 20 degrees/turn.
 Note, that if setAdjustGunForRobotTurn(true) has been called, the gun
 turn is independent of the robot turn.
 In this case the gun moves relatively to the screen. If
 setAdjustGunForRobotTurn(false) has been called or
 setAdjustGunForRobotTurn() has not been called at all (this is the
 default), then the gun turn is dependent on the robot turn, and in this
 case the gun moves relatively to the robot body.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#GUN_TURN_RATE_RADIANS"><code>GUN_TURN_RATE_RADIANS</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a>, 
<a href="../constant-values.html#robocode.Rules.GUN_TURN_RATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GUN_TURN_RATE_RADIANS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GUN_TURN_RATE_RADIANS</h4>
<pre>public static final&nbsp;double GUN_TURN_RATE_RADIANS</pre>
<div class="block">The turning rate of the gun measured in radians instead of degrees.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#GUN_TURN_RATE"><code>GUN_TURN_RATE</code></a></dd>
</dl>
</li>
</ul>
<a name="RADAR_TURN_RATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RADAR_TURN_RATE</h4>
<pre>public static final&nbsp;double RADAR_TURN_RATE</pre>
<div class="block">The turning rate of the radar measured in degrees, which is
 45 degrees/turn.
 Note, that if setAdjustRadarForRobotTurn(true) and/or
 setAdjustRadarForGunTurn(true) has been called, the radar turn is
 independent of the robot and/or gun turn. If both methods has been set to
 true, the radar moves relatively to the screen.
 If setAdjustRadarForRobotTurn(false) and/or setAdjustRadarForGunTurn(false)
 has been called or not called at all (this is the default), then the
 radar turn is dependent on the robot and/or gun turn, and in this case
 the radar moves relatively to the gun and/or robot body.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#RADAR_TURN_RATE_RADIANS"><code>RADAR_TURN_RATE_RADIANS</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>Robot.setAdjustRadarForGunTurn(boolean)</code></a>, 
<a href="../constant-values.html#robocode.Rules.RADAR_TURN_RATE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RADAR_TURN_RATE_RADIANS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RADAR_TURN_RATE_RADIANS</h4>
<pre>public static final&nbsp;double RADAR_TURN_RATE_RADIANS</pre>
<div class="block">The turning rate of the radar measured in radians instead of degrees.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#RADAR_TURN_RATE"><code>RADAR_TURN_RATE</code></a></dd>
</dl>
</li>
</ul>
<a name="ROBOT_HIT_DAMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROBOT_HIT_DAMAGE</h4>
<pre>public static final&nbsp;double ROBOT_HIT_DAMAGE</pre>
<div class="block">The amount of damage taken when a robot hits or is hit by another robot,
 which is 0.6 energy points.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.ROBOT_HIT_DAMAGE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ROBOT_HIT_BONUS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ROBOT_HIT_BONUS</h4>
<pre>public static final&nbsp;double ROBOT_HIT_BONUS</pre>
<div class="block">The amount of bonus damage dealt by a robot ramming an opponent by moving forward into it,
 which is 2 x <a href="../robocode/Rules.html#ROBOT_HIT_DAMAGE"><code>ROBOT_HIT_DAMAGE</code></a> = 1.2 energy points.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../constant-values.html#robocode.Rules.ROBOT_HIT_BONUS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTurnRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRate</h4>
<pre>public static&nbsp;double&nbsp;getTurnRate(double&nbsp;velocity)</pre>
<div class="block">Returns the turn rate of a robot given a specific velocity measured in
 degrees/turn.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>velocity</code> - the velocity of the robot.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>turn rate in degrees/turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#getTurnRateRadians-double-"><code>getTurnRateRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getTurnRateRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRateRadians</h4>
<pre>public static&nbsp;double&nbsp;getTurnRateRadians(double&nbsp;velocity)</pre>
<div class="block">Returns the turn rate of a robot given a specific velocity measured in
 radians/turn.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>velocity</code> - the velocity of the robot.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>turn rate in radians/turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Rules.html#getTurnRate-double-"><code>getTurnRate(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getWallHitDamage-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallHitDamage</h4>
<pre>public static&nbsp;double&nbsp;getWallHitDamage(double&nbsp;velocity)</pre>
<div class="block">Returns the amount of damage taken when robot hits a wall with a
 specific velocity.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>velocity</code> - the velocity of the robot.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>wall hit damage in energy points.</dd>
</dl>
</li>
</ul>
<a name="getBulletDamage-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletDamage</h4>
<pre>public static&nbsp;double&nbsp;getBulletDamage(double&nbsp;bulletPower)</pre>
<div class="block">Returns the amount of damage of a bullet given a specific bullet power.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bulletPower</code> - the energy power of the bullet.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bullet damage in energy points.</dd>
</dl>
</li>
</ul>
<a name="getBulletHitBonus-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletHitBonus</h4>
<pre>public static&nbsp;double&nbsp;getBulletHitBonus(double&nbsp;bulletPower)</pre>
<div class="block">Returns the amount of bonus given when a robot's bullet hits an opponent
 robot given a specific bullet power.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bulletPower</code> - the energy power of the bullet.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bullet hit bonus in energy points.</dd>
</dl>
</li>
</ul>
<a name="getBulletSpeed-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletSpeed</h4>
<pre>public static&nbsp;double&nbsp;getBulletSpeed(double&nbsp;bulletPower)</pre>
<div class="block">Returns the speed of a bullet given a specific bullet power measured in pixels/turn.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bulletPower</code> - the energy power of the bullet.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>bullet speed in pixels/turn</dd>
</dl>
</li>
</ul>
<a name="getGunHeat-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getGunHeat</h4>
<pre>public static&nbsp;double&nbsp;getGunHeat(double&nbsp;bulletPower)</pre>
<div class="block">Returns the heat produced by firing the gun given a specific bullet
 power.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bulletPower</code> - the energy power of the bullet.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>gun heat</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RoundEndedEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/Rules.html" target="_top">Frames</a></li>
<li><a href="Rules.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
