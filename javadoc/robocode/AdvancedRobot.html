<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AdvancedRobot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AdvancedRobot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/_RobotBase.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/BattleEndedEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/AdvancedRobot.html" target="_top">Frames</a></li>
<li><a href="AdvancedRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class AdvancedRobot" class="title">Class AdvancedRobot</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_RobotBase.html" title="class in robocode">robocode._RobotBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_Robot.html" title="class in robocode">robocode._Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/Robot.html" title="class in robocode">robocode.Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_AdvancedRobot.html" title="class in robocode">robocode._AdvancedRobot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">robocode._AdvancedRadiansRobot</a></li>
<li>
<ul class="inheritance">
<li>robocode.AdvancedRobot</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>, <a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a>, <a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a>, <a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a>, <a href="../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a>, <a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, <a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, <a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, <a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>, <a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AdvancedRobot</span>
extends <a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a>
implements <a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a>, <a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a></pre>
<div class="block">A more advanced type of robot than Robot that allows non-blocking calls,
 custom events, and writes to the filesystem.
 <p>
 If you have not already, you should create a <a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a> first.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor), Robert D. Maupin (contributor), Pavel Savara (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a target="_top" href="https://robocode.sourceforge.io">
      robocode.sourceforge.net</a>, 
<a href="https://robocode.sourceforge.io/myfirstrobot/MyFirstRobot.html">
      Building your first robot</a>, 
<a href="../robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>, 
<a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>, 
<a href="../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>, 
<a href="../robocode/RateControlRobot.html" title="class in robocode"><code>RateControlRobot</code></a>, 
<a href="../robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>, 
<a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#out">out</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#AdvancedRobot--">AdvancedRobot</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-">addCustomEvent</a></span>(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</code>
<div class="block">Registers a custom event to be called when a condition is met.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#clearAllEvents--">clearAllEvents</a></span>()</code>
<div class="block">Clears out any pending events in the robot's event queue immediately.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#execute--">execute</a></span>()</code>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener</a></span>()</code>
<div class="block">Do not call this method!</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/Event.html" title="class in robocode">Event</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getAllEvents--">getAllEvents</a></span>()</code>
<div class="block">Returns a vector containing all events currently in the robot's queue.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getBulletHitBulletEvents--">getBulletHitBulletEvents</a></span>()</code>
<div class="block">Returns a vector containing all BulletHitBulletEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getBulletHitEvents--">getBulletHitEvents</a></span>()</code>
<div class="block">Returns a vector containing all BulletHitEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getBulletMissedEvents--">getBulletMissedEvents</a></span>()</code>
<div class="block">Returns a vector containing all BulletMissedEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getDataDirectory--">getDataDirectory</a></span>()</code>
<div class="block">Returns a file representing a data directory for the robot, which can be
 written to using <a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or
 <a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-">getDataFile</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filename)</code>
<div class="block">Returns a file in your data directory that you can write to using
 <a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or <a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getDataQuotaAvailable--">getDataQuotaAvailable</a></span>()</code>
<div class="block">Returns the data quota available in your data directory, i.e.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getDistanceRemaining--">getDistanceRemaining</a></span>()</code>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getEventPriority-java.lang.String-">getEventPriority</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass)</code>
<div class="block">Returns the current priority of a class of events.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getGunHeadingRadians--">getGunHeadingRadians</a></span>()</code>
<div class="block">Returns the direction that the robot's gun is facing, in radians.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--">getGunTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the gun's turn, in degrees.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians</a></span>()</code>
<div class="block">Returns the angle remaining in the gun's turn, in radians.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getHeadingRadians--">getHeadingRadians</a></span>()</code>
<div class="block">Returns the direction that the robot's body is facing, in radians.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getHitByBulletEvents--">getHitByBulletEvents</a></span>()</code>
<div class="block">Returns a vector containing all HitByBulletEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getHitRobotEvents--">getHitRobotEvents</a></span>()</code>
<div class="block">Returns a vector containing all HitRobotEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getHitWallEvents--">getHitWallEvents</a></span>()</code>
<div class="block">Returns a vector containing all HitWallEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getRadarHeadingRadians--">getRadarHeadingRadians</a></span>()</code>
<div class="block">Returns the direction that the robot's radar is facing, in radians.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--">getRadarTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the radar's turn, in degrees.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians</a></span>()</code>
<div class="block">Returns the angle remaining in the radar's turn, in radians.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getRobotDeathEvents--">getRobotDeathEvents</a></span>()</code>
<div class="block">Returns a vector containing all RobotDeathEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getScannedRobotEvents--">getScannedRobotEvents</a></span>()</code>
<div class="block">Returns a vector containing all ScannedRobotEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getStatusEvents--">getStatusEvents</a></span>()</code>
<div class="block">Returns a vector containing all StatusEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getTurnRemaining--">getTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the robots's turn, in degrees.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--">getTurnRemainingRadians</a></span>()</code>
<div class="block">Returns the angle remaining in the robot's turn, in radians.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#isAdjustGunForRobotTurn--">isAdjustGunForRobotTurn</a></span>()</code>
<div class="block">Checks if the gun is set to adjust for the robot turning, i.e.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#isAdjustRadarForGunTurn--">isAdjustRadarForGunTurn</a></span>()</code>
<div class="block">Checks if the radar is set to adjust for the gun turning, i.e.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#isAdjustRadarForRobotTurn--">isAdjustRadarForRobotTurn</a></span>()</code>
<div class="block">Checks if the radar is set to adjust for the robot turning, i.e.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#onCustomEvent-robocode.CustomEvent-">onCustomEvent</a></span>(<a href="../robocode/CustomEvent.html" title="class in robocode">CustomEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a custom condition is met.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#onDeath-robocode.DeathEvent-">onDeath</a></span>(<a href="../robocode/DeathEvent.html" title="class in robocode">DeathEvent</a>&nbsp;event)</code>
<div class="block">This method is called if your robot dies.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-">onSkippedTurn</a></span>(<a href="../robocode/SkippedTurnEvent.html" title="class in robocode">SkippedTurnEvent</a>&nbsp;event)</code>
<div class="block">This method is called if the robot is using too much time between
 actions.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#removeCustomEvent-robocode.Condition-">removeCustomEvent</a></span>(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</code>
<div class="block">Removes a custom event that was previously added by calling
 <a href="../robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-"><code>addCustomEvent(Condition)</code></a>.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setAhead-double-">setAhead</a></span>(double&nbsp;distance)</code>
<div class="block">Sets the robot to move ahead (forward) by distance measured in pixels
 when the next execution takes place.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setBack-double-">setBack</a></span>(double&nbsp;distance)</code>
<div class="block">Sets the robot to move back by distance measured in pixels when the next
 execution takes place.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setEventPriority-java.lang.String-int-">setEventPriority</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass,
                int&nbsp;priority)</code>
<div class="block">Sets the priority of a class of events.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setFire-double-">setFire</a></span>(double&nbsp;power)</code>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../robocode/Bullet.html" title="class in robocode">Bullet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setFireBullet-double-">setFireBullet</a></span>(double&nbsp;power)</code>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setInterruptible-boolean-">setInterruptible</a></span>(boolean&nbsp;interruptible)</code>
<div class="block">Call this during an event handler to allow new events of the same
 priority to restart the event handler.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setMaxTurnRate-double-">setMaxTurnRate</a></span>(double&nbsp;newMaxTurnRate)</code>
<div class="block">Sets the maximum turn rate of the robot measured in degrees if the robot
 should turn slower than <a href="../robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a> (10 degress/turn).</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setMaxVelocity-double-">setMaxVelocity</a></span>(double&nbsp;newMaxVelocity)</code>
<div class="block">Sets the maximum velocity of the robot measured in pixels/turn if the
 robot should move slower than <a href="../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a> (8 pixels/turn).</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setResume--">setResume</a></span>()</code>
<div class="block">Sets the robot to resume the movement stopped by <a href="../robocode/Robot.html#stop--"><code>stop()</code></a>
 or <a href="../robocode/AdvancedRobot.html#setStop--"><code>setStop()</code></a>, if any.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setStop--">setStop</a></span>()</code>
<div class="block">This call is identical to <a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, but returns immediately, and
 will not execute until you call <a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a> or take an action that
 executes.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setStop-boolean-">setStop</a></span>(boolean&nbsp;overwrite)</code>
<div class="block">This call is identical to <a href="../robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a>, but
 returns immediately, and will not execute until you call
 <a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a> or take an action that executes.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnGunLeft-double-">setTurnGunLeft</a></span>(double&nbsp;degrees)</code>
<div class="block">Sets the robot's gun to turn left by degrees when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-">setTurnGunLeftRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's gun to turn left by radians when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnGunRight-double-">setTurnGunRight</a></span>(double&nbsp;degrees)</code>
<div class="block">Sets the robot's gun to turn right by degrees when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnGunRightRadians-double-">setTurnGunRightRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's gun to turn right by radians when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnLeft-double-">setTurnLeft</a></span>(double&nbsp;degrees)</code>
<div class="block">Sets the robot's body to turn left by degrees when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnLeftRadians-double-">setTurnLeftRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's body to turn left by radians when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnRadarLeft-double-">setTurnRadarLeft</a></span>(double&nbsp;degrees)</code>
<div class="block">Sets the robot's radar to turn left by degrees when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-">setTurnRadarLeftRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's radar to turn left by radians when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnRadarRight-double-">setTurnRadarRight</a></span>(double&nbsp;degrees)</code>
<div class="block">Sets the robot's radar to turn right by degrees when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-">setTurnRadarRightRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's radar to turn right by radians when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnRight-double-">setTurnRight</a></span>(double&nbsp;degrees)</code>
<div class="block">Sets the robot's body to turn right by degrees when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#setTurnRightRadians-double-">setTurnRightRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's body to turn right by radians when the next execution
 takes place.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-">turnGunLeftRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's gun to the left by radians.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-">turnGunRightRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's gun to the right by radians.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-">turnLeftRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's body to the left by radians.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-">turnRadarLeftRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's radar to the left by radians.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-">turnRadarRightRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's radar to the right by radians.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#turnRightRadians-double-">turnRightRadians</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's body to the right by radians.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/AdvancedRobot.html#waitFor-robocode.Condition-">waitFor</a></span>(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</code>
<div class="block">Does not return until a condition is met, i.e.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._AdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></h3>
<code><a href="../robocode/_AdvancedRobot.html#endTurn--">endTurn</a>, <a href="../robocode/_AdvancedRobot.html#getGunHeadingDegrees--">getGunHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getHeadingDegrees--">getHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getMaxWaitCount--">getMaxWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#getRadarHeadingDegrees--">getRadarHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getWaitCount--">getWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunLeftDegrees-double-">setTurnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunRightDegrees-double-">setTurnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnLeftDegrees-double-">setTurnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarLeftDegrees-double-">setTurnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarRightDegrees-double-">setTurnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRightDegrees-double-">setTurnRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunLeftDegrees-double-">turnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunRightDegrees-double-">turnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnLeftDegrees-double-">turnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarLeftDegrees-double-">turnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarRightDegrees-double-">turnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRightDegrees-double-">turnRightDegrees</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/Robot.html" title="class in robocode">Robot</a></h3>
<code><a href="../robocode/Robot.html#ahead-double-">ahead</a>, <a href="../robocode/Robot.html#back-double-">back</a>, <a href="../robocode/Robot.html#doNothing--">doNothing</a>, <a href="../robocode/Robot.html#fire-double-">fire</a>, <a href="../robocode/Robot.html#fireBullet-double-">fireBullet</a>, <a href="../robocode/Robot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../robocode/Robot.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../robocode/Robot.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../robocode/Robot.html#getEnergy--">getEnergy</a>, <a href="../robocode/Robot.html#getGraphics--">getGraphics</a>, <a href="../robocode/Robot.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../robocode/Robot.html#getGunHeading--">getGunHeading</a>, <a href="../robocode/Robot.html#getGunHeat--">getGunHeat</a>, <a href="../robocode/Robot.html#getHeading--">getHeading</a>, <a href="../robocode/Robot.html#getHeight--">getHeight</a>, <a href="../robocode/Robot.html#getInteractiveEventListener--">getInteractiveEventListener</a>, <a href="../robocode/Robot.html#getName--">getName</a>, <a href="../robocode/Robot.html#getNumRounds--">getNumRounds</a>, <a href="../robocode/Robot.html#getNumSentries--">getNumSentries</a>, <a href="../robocode/Robot.html#getOthers--">getOthers</a>, <a href="../robocode/Robot.html#getPaintEventListener--">getPaintEventListener</a>, <a href="../robocode/Robot.html#getRadarHeading--">getRadarHeading</a>, <a href="../robocode/Robot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../robocode/Robot.html#getRoundNum--">getRoundNum</a>, <a href="../robocode/Robot.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../robocode/Robot.html#getTime--">getTime</a>, <a href="../robocode/Robot.html#getVelocity--">getVelocity</a>, <a href="../robocode/Robot.html#getWidth--">getWidth</a>, <a href="../robocode/Robot.html#getX--">getX</a>, <a href="../robocode/Robot.html#getY--">getY</a>, <a href="../robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded</a>, <a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit</a>, <a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet</a>, <a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed</a>, <a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet</a>, <a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot</a>, <a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-">onHitWall</a>, <a href="../robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed</a>, <a href="../robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased</a>, <a href="../robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped</a>, <a href="../robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked</a>, <a href="../robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged</a>, <a href="../robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered</a>, <a href="../robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited</a>, <a href="../robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved</a>, <a href="../robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed</a>, <a href="../robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased</a>, <a href="../robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved</a>, <a href="../robocode/Robot.html#onPaint-java.awt.Graphics2D-">onPaint</a>, <a href="../robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath</a>, <a href="../robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded</a>, <a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot</a>, <a href="../robocode/Robot.html#onStatus-robocode.StatusEvent-">onStatus</a>, <a href="../robocode/Robot.html#onWin-robocode.WinEvent-">onWin</a>, <a href="../robocode/Robot.html#resume--">resume</a>, <a href="../robocode/Robot.html#run--">run</a>, <a href="../robocode/Robot.html#scan--">scan</a>, <a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-">setAdjustGunForRobotTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-">setAdjustRadarForRobotTurn</a>, <a href="../robocode/Robot.html#setAllColors-java.awt.Color-">setAllColors</a>, <a href="../robocode/Robot.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../robocode/Robot.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../robocode/Robot.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../robocode/Robot.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../robocode/Robot.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../robocode/Robot.html#stop--">stop</a>, <a href="../robocode/Robot.html#stop-boolean-">stop</a>, <a href="../robocode/Robot.html#turnGunLeft-double-">turnGunLeft</a>, <a href="../robocode/Robot.html#turnGunRight-double-">turnGunRight</a>, <a href="../robocode/Robot.html#turnLeft-double-">turnLeft</a>, <a href="../robocode/Robot.html#turnRadarLeft-double-">turnRadarLeft</a>, <a href="../robocode/Robot.html#turnRadarRight-double-">turnRadarRight</a>, <a href="../robocode/Robot.html#turnRight-double-">turnRight</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_Robot.html" title="class in robocode">_Robot</a></h3>
<code><a href="../robocode/_Robot.html#getBattleNum--">getBattleNum</a>, <a href="../robocode/_Robot.html#getGunCharge--">getGunCharge</a>, <a href="../robocode/_Robot.html#getGunImageName--">getGunImageName</a>, <a href="../robocode/_Robot.html#getLife--">getLife</a>, <a href="../robocode/_Robot.html#getNumBattles--">getNumBattles</a>, <a href="../robocode/_Robot.html#getRadarImageName--">getRadarImageName</a>, <a href="../robocode/_Robot.html#getRobotImageName--">getRobotImageName</a>, <a href="../robocode/_Robot.html#setGunImageName-java.lang.String-">setGunImageName</a>, <a href="../robocode/_Robot.html#setRadarImageName-java.lang.String-">setRadarImageName</a>, <a href="../robocode/_Robot.html#setRobotImageName-java.lang.String-">setRobotImageName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#finalize--">finalize</a>, <a href="../robocode/_RobotBase.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/_RobotBase.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a>, <a href="../robocode/_RobotBase.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IBasicRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IBasicRobot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AdvancedRobot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AdvancedRobot</h4>
<pre>public&nbsp;AdvancedRobot()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDistanceRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistanceRemaining</h4>
<pre>public&nbsp;double&nbsp;getDistanceRemaining()</pre>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently moving forwards. Negative values means
 that the robot is currently moving backwards. If the returned value is 0,
 the robot currently stands still.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the distance remaining in the robot's current move measured in
         pixels.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getTurnRemaining--"><code>getTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--"><code>getTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--"><code>getGunTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--"><code>getRadarTurnRemainingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="getTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRemaining</h4>
<pre>public&nbsp;double&nbsp;getTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the robots's turn, in degrees.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently turning to the right. Negative values
 means that the robot is currently turning to the left. If the returned
 value is 0, the robot is currently not turning.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the robots's turn, in degrees</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--"><code>getTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getDistanceRemaining--"><code>getDistanceRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--"><code>getGunTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--"><code>getRadarTurnRemainingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunTurnRemaining</h4>
<pre>public&nbsp;double&nbsp;getGunTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the gun's turn, in degrees.
 <p>
 This call returns both positive and negative values. Positive values
 means that the gun is currently turning to the right. Negative values
 means that the gun is currently turning to the left. If the returned
 value is 0, the gun is currently not turning.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the gun's turn, in degrees</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--"><code>getGunTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getDistanceRemaining--"><code>getDistanceRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getTurnRemaining--"><code>getTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--"><code>getTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--"><code>getRadarTurnRemainingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarTurnRemaining</h4>
<pre>public&nbsp;double&nbsp;getRadarTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the radar's turn, in degrees.
 <p>
 This call returns both positive and negative values. Positive values
 means that the radar is currently turning to the right. Negative values
 means that the radar is currently turning to the left. If the returned
 value is 0, the radar is currently not turning.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the radar's turn, in degrees</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--"><code>getRadarTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getDistanceRemaining--"><code>getDistanceRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--"><code>getGunTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--"><code>getRadarTurnRemainingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="setAhead-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAhead</h4>
<pre>public&nbsp;void&nbsp;setAhead(double&nbsp;distance)</pre>
<div class="block">Sets the robot to move ahead (forward) by distance measured in pixels
 when the next execution takes place.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a> or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot is set to move ahead, and negative
 values means that the robot is set to move back. If 0 is given as input,
 the robot will stop its movement, but will have to decelerate
 till it stands still, and will thus not be able to stop its movement
 immediately, but eventually.
 <p>
 Example:
 <pre>
   // Set the robot to move 50 pixels ahead
   setAhead(50);

   // Set the robot to move 100 pixels back
   // (overrides the previous order)
   setAhead(-100);

   ...
   // Executes the last setAhead()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the distance to move measured in pixels.
                 If <code>distance</code> &gt; 0 the robot is set to move ahead.
                 If <code>distance</code> &lt; 0 the robot is set to move back.
                 If <code>distance</code> = 0 the robot is set to stop its movement.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#ahead-double-"><code>ahead(double)</code></a>, 
<a href="../robocode/Robot.html#back-double-"><code>back(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setBack-double-"><code>setBack(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setBack-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBack</h4>
<pre>public&nbsp;void&nbsp;setBack(double&nbsp;distance)</pre>
<div class="block">Sets the robot to move back by distance measured in pixels when the next
 execution takes place.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a> or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot is set to move back, and negative
 values means that the robot is set to move ahead. If 0 is given as input,
 the robot will stop its movement, but will have to decelerate
 till it stands still, and will thus not be able to stop its movement
 immediately, but eventually.
 <p>
 Example:
 <pre>
   // Set the robot to move 50 pixels back
   setBack(50);

   // Set the robot to move 100 pixels ahead
   // (overrides the previous order)
   setBack(-100);

   ...
   // Executes the last setBack()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the distance to move measured in pixels.
                 If <code>distance</code> &gt; 0 the robot is set to move back.
                 If <code>distance</code> &lt; 0 the robot is set to move ahead.
                 If <code>distance</code> = 0 the robot is set to stop its movement.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#back-double-"><code>back(double)</code></a>, 
<a href="../robocode/Robot.html#ahead-double-"><code>ahead(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setAhead-double-"><code>setAhead(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnLeft-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnLeft</h4>
<pre>public&nbsp;void&nbsp;setTurnLeft(double&nbsp;degrees)</pre>
<div class="block">Sets the robot's body to turn left by degrees when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Set the robot to turn 180 degrees to the left
   setTurnLeft(180);

   // Set the robot to turn 90 degrees to the right instead of left
   // (overrides the previous order)
   setTurnLeft(-90);

   ...
   // Executes the last setTurnLeft()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's body to the left.
                If <code>degrees</code> &gt; 0 the robot is set to turn left.
                If <code>degrees</code> &lt; 0 the robot is set to turn right.
                If <code>degrees</code> = 0 the robot is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnLeftRadians-double-"><code>setTurnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRight-double-"><code>setTurnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRightRadians-double-"><code>setTurnRightRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRight</h4>
<pre>public&nbsp;void&nbsp;setTurnRight(double&nbsp;degrees)</pre>
<div class="block">Sets the robot's body to turn right by degrees when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Set the robot to turn 180 degrees to the right
   setTurnRight(180);

   // Set the robot to turn 90 degrees to the left instead of right
   // (overrides the previous order)
   setTurnRight(-90);

   ...
   // Executes the last setTurnRight()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's body to the right.
                If <code>degrees</code> &gt; 0 the robot is set to turn right.
                If <code>degrees</code> &lt; 0 the robot is set to turn left.
                If <code>degrees</code> = 0 the robot is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnRightRadians-double-"><code>setTurnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnLeft-double-"><code>setTurnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnLeftRadians-double-"><code>setTurnLeftRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setFire-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFire</h4>
<pre>public&nbsp;void&nbsp;setFire(double&nbsp;power)</pre>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.
 The bullet will travel in the direction the gun is pointing.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 The specified bullet power is an amount of energy that will be taken from
 the robot's energy. Hence, the more power you want to spend on the
 bullet, the more energy is taken from your robot.
 <p>
 The bullet will do (4 * power) damage if it hits another robot. If power
 is greater than 1, it will do an additional 2 * (power - 1) damage.
 You will get (3 * power) back if you hit the other robot. You can call
 Rules#getBulletDamage(double)} for getting the damage that a
 bullet with a specific bullet power will do.
 <p>
 The specified bullet power should be between
 <a href="../robocode/Rules.html#MIN_BULLET_POWER"><code>Rules.MIN_BULLET_POWER</code></a> and <a href="../robocode/Rules.html#MAX_BULLET_POWER"><code>Rules.MAX_BULLET_POWER</code></a>.
 <p>
 Note that the gun cannot fire if the gun is overheated, meaning that
 <a href="../robocode/Robot.html#getGunHeat--"><code>Robot.getGunHeat()</code></a> returns a value &gt; 0.
 <p>
 An event is generated when the bullet hits a robot, wall, or another
 bullet.
 <p>
 Example:
 <pre>
   // Fire a bullet with maximum power if the gun is ready
   if (getGunHeat() == 0) {
       setFire(Rules.MAX_BULLET_POWER);
   }
   ...
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - the amount of energy given to the bullet, and subtracted
              from the robot's energy.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setFireBullet-double-"><code>setFireBullet(double)</code></a>, 
<a href="../robocode/Robot.html#fire-double-"><code>fire(double)</code></a>, 
<a href="../robocode/Robot.html#fireBullet-double-"><code>fireBullet(double)</code></a>, 
<a href="../robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../robocode/Robot.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="setFireBullet-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFireBullet</h4>
<pre>public&nbsp;<a href="../robocode/Bullet.html" title="class in robocode">Bullet</a>&nbsp;setFireBullet(double&nbsp;power)</pre>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.
 The bullet will travel in the direction the gun is pointing.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 The specified bullet power is an amount of energy that will be taken from
 the robot's energy. Hence, the more power you want to spend on the
 bullet, the more energy is taken from your robot.
 <p>
 The bullet will do (4 * power) damage if it hits another robot. If power
 is greater than 1, it will do an additional 2 * (power - 1) damage.
 You will get (3 * power) back if you hit the other robot. You can call
 <a href="../robocode/Rules.html#getBulletDamage-double-"><code>Rules.getBulletDamage(double)</code></a> for getting the damage that a
 bullet with a specific bullet power will do.
 <p>
 The specified bullet power should be between
 <a href="../robocode/Rules.html#MIN_BULLET_POWER"><code>Rules.MIN_BULLET_POWER</code></a> and <a href="../robocode/Rules.html#MAX_BULLET_POWER"><code>Rules.MAX_BULLET_POWER</code></a>.
 <p>
 Note that the gun cannot fire if the gun is overheated, meaning that
 <a href="../robocode/Robot.html#getGunHeat--"><code>Robot.getGunHeat()</code></a> returns a value &gt; 0.
 <p>
 A event is generated when the bullet hits a robot
 (<a href="../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>), wall (<a href="../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>), or another
 bullet (<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>).
 <p>
 Example:
 <pre>
   Bullet bullet = null;

   // Fire a bullet with maximum power if the gun is ready
   if (getGunHeat() == 0) {
       bullet = setFireBullet(Rules.MAX_BULLET_POWER);
   }
   ...
   execute();
   ...
   // Get the velocity of the bullet
   if (bullet != null) {
       double bulletVelocity = bullet.getVelocity();
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - the amount of energy given to the bullet, and subtracted
              from the robot's energy.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a> that contains information about the bullet if it
         was actually fired, which can be used for tracking the bullet after it
         has been fired. If the bullet was not fired, <code>null</code> is returned.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setFire-double-"><code>setFire(double)</code></a>, 
<a href="../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a>, 
<a href="../robocode/Robot.html#fire-double-"><code>fire(double)</code></a>, 
<a href="../robocode/Robot.html#fireBullet-double-"><code>fireBullet(double)</code></a>, 
<a href="../robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../robocode/Robot.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="addCustomEvent-robocode.Condition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCustomEvent</h4>
<pre>public&nbsp;void&nbsp;addCustomEvent(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</pre>
<div class="block">Registers a custom event to be called when a condition is met.
 When you are finished with your condition or just want to remove it you
 must call <a href="../robocode/AdvancedRobot.html#removeCustomEvent-robocode.Condition-"><code>removeCustomEvent(Condition)</code></a>.
 <p>
 Example:
 <pre>
   // Create the condition for our custom event
   Condition triggerHitCondition = new Condition("triggerhit") {
       public boolean test() {
           return (getEnergy() &lt;= trigger);
       }
   }

   // Add our custom event based on our condition
   <b>addCustomEvent(triggerHitCondition);</b>
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that must be met.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the condition parameter has been set to
                              <code>null</code>.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Condition.html" title="class in robocode"><code>Condition</code></a>, 
<a href="../robocode/AdvancedRobot.html#removeCustomEvent-robocode.Condition-"><code>removeCustomEvent(Condition)</code></a></dd>
</dl>
</li>
</ul>
<a name="removeCustomEvent-robocode.Condition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeCustomEvent</h4>
<pre>public&nbsp;void&nbsp;removeCustomEvent(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</pre>
<div class="block">Removes a custom event that was previously added by calling
 <a href="../robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-"><code>addCustomEvent(Condition)</code></a>.
 <p>
 Example:
 <pre>
   // Create the condition for our custom event
   Condition triggerHitCondition = new Condition("triggerhit") {
       public boolean test() {
           return (getEnergy() &lt;= trigger);
       }
   }

   // Add our custom event based on our condition
   addCustomEvent(triggerHitCondition);
   ...
   <i>do something with your robot</i>
   ...
   // Remove the custom event based on our condition
   <b>removeCustomEvent(triggerHitCondition);</b>
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that was previous added and that must be
                  removed now.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the condition parameter has been set to
                              <code>null</code>.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Condition.html" title="class in robocode"><code>Condition</code></a>, 
<a href="../robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-"><code>addCustomEvent(Condition)</code></a></dd>
</dl>
</li>
</ul>
<a name="clearAllEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clearAllEvents</h4>
<pre>public&nbsp;void&nbsp;clearAllEvents()</pre>
<div class="block">Clears out any pending events in the robot's event queue immediately.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="execute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>execute</h4>
<pre>public&nbsp;void&nbsp;execute()</pre>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process. This call returns after the actions have been started.
 <p>
 Note that advanced robots <em>must</em> call this function in order to
 execute pending set* calls like e.g. <a href="../robocode/AdvancedRobot.html#setAhead-double-"><code>setAhead(double)</code></a>,
 <a href="../robocode/AdvancedRobot.html#setFire-double-"><code>setFire(double)</code></a>, <a href="../robocode/AdvancedRobot.html#setTurnLeft-double-"><code>setTurnLeft(double)</code></a> etc. Otherwise,
 these calls will never get executed.
 <p>
 In this example the robot will move while turning:
 <pre>
   setTurnRight(90);
   setAhead(100);
   execute();

   while (getDistanceRemaining() &gt; 0 &amp;&amp; getTurnRemaining() &gt; 0) {
       execute();
   }
 </pre></div>
</li>
</ul>
<a name="getAllEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/Event.html" title="class in robocode">Event</a>&gt;&nbsp;getAllEvents()</pre>
<div class="block">Returns a vector containing all events currently in the robot's queue.
 You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (Event event : getAllEvents()) {
       if (event instanceof HitRobotEvent) {
           <i>// do something with the event</i>
       } else if (event instanceof HitByBulletEvent) {
           <i>// do something with the event</i>
       }
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all events currently in the robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a>, 
<a href="../robocode/AdvancedRobot.html#clearAllEvents--"><code>clearAllEvents()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getStatusEvents--"><code>getStatusEvents()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getScannedRobotEvents--"><code>getScannedRobotEvents()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getBulletHitEvents--"><code>getBulletHitEvents()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getBulletMissedEvents--"><code>getBulletMissedEvents()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getBulletHitBulletEvents--"><code>getBulletHitBulletEvents()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRobotDeathEvents--"><code>getRobotDeathEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBulletHitBulletEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletHitBulletEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&gt;&nbsp;getBulletHitBulletEvents()</pre>
<div class="block">Returns a vector containing all BulletHitBulletEvents currently in the
 robot's queue. You might, for example, call this while processing another
 event.
 <p>
 Example:
 <pre>
   for (BulletHitBulletEvent event : getBulletHitBulletEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all BulletHitBulletEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBulletHitEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletHitEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&gt;&nbsp;getBulletHitEvents()</pre>
<div class="block">Returns a vector containing all BulletHitEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (BulletHitEvent event: getBulletHitEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all BulletHitEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBulletMissedEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletMissedEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&gt;&nbsp;getBulletMissedEvents()</pre>
<div class="block">Returns a vector containing all BulletMissedEvents currently in the
 robot's queue. You might, for example, call this while processing another
 event.
 <p>
 Example:
 <pre>
   for (BulletMissedEvent event : getBulletMissedEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all BulletMissedEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a>, 
<a href="../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getDataDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataDirectory</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;getDataDirectory()</pre>
<div class="block">Returns a file representing a data directory for the robot, which can be
 written to using <a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or
 <a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.
 <p>
 The system will automatically create the directory for you, so you do not
 need to create it by yourself.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a file representing the data directory for your robot</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>getDataFile(String)</code></a>, 
<a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a>, 
<a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a></dd>
</dl>
</li>
</ul>
<a name="getDataFile-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataFile</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;getDataFile(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filename)</pre>
<div class="block">Returns a file in your data directory that you can write to using
 <a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or <a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.
 <p>
 The system will automatically create the directory for you, so you do not
 need to create it by yourself.
 <p>
 Please notice that the max. size of your data file is set to 200000
 (~195 KB).
 <p>
 See the <code>sample.SittingDuck</code> to see an example of how to use this
 method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - the file name of the data file for your robot</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a file representing the data file for your robot or null if the data
                   file could not be created due to an error.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getDataDirectory--"><code>getDataDirectory()</code></a>, 
<a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a>, 
<a href="../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a></dd>
</dl>
</li>
</ul>
<a name="getDataQuotaAvailable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataQuotaAvailable</h4>
<pre>public&nbsp;long&nbsp;getDataQuotaAvailable()</pre>
<div class="block">Returns the data quota available in your data directory, i.e. the amount
 of bytes left in the data directory for the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the amount of bytes left in the robot's data directory</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getDataDirectory--"><code>getDataDirectory()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>getDataFile(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="getEventPriority-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEventPriority</h4>
<pre>public&nbsp;int&nbsp;getEventPriority(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass)</pre>
<div class="block">Returns the current priority of a class of events.
 An event priority is a value from 0 - 99. The higher value, the higher
 priority.
 <p>
 Example:
 <pre>
   int myHitRobotPriority = getEventPriority("HitRobotEvent");
 </pre>
 <p>
 The default priorities are, from highest to lowest:
 <pre>
   <a href="../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>:      100 (reserved)
   <a href="../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>:     100 (reserved)
   <a href="../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>:             100 (reserved)
   <a href="../robocode/SkippedTurnEvent.html" title="class in robocode"><code>SkippedTurnEvent</code></a>:     100 (reserved)
   <a href="../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>:           99
   Key and mouse events:  98
   <a href="../robocode/CustomEvent.html" title="class in robocode"><code>CustomEvent</code></a>:           80 (default value)
   <a href="../robocode/MessageEvent.html" title="class in robocode"><code>MessageEvent</code></a>:          75
   <a href="../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>:       70
   <a href="../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>:     60
   <a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>:  55
   <a href="../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>:        50
   <a href="../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>:      40
   <a href="../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>:          30
   <a href="../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>:         20
   <a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>:     10
   <a href="../robocode/PaintEvent.html" title="class in robocode"><code>PaintEvent</code></a>:             5
   <a href="../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>:            -1 (reserved)
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>eventClass</code> - the name of the event class (string)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current priority of a class of events</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setEventPriority-java.lang.String-int-"><code>setEventPriority(String, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="getHitByBulletEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHitByBulletEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&gt;&nbsp;getHitByBulletEvents()</pre>
<div class="block">Returns a vector containing all HitByBulletEvents currently in the
 robot's queue. You might, for example, call this while processing
 another event.
 <p>
 Example:
 <pre>
   for (HitByBulletEvent event : getHitByBulletEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all HitByBulletEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-"><code>onHitByBullet(HitByBulletEvent)</code></a>, 
<a href="../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHitRobotEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHitRobotEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&gt;&nbsp;getHitRobotEvents()</pre>
<div class="block">Returns a vector containing all HitRobotEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (HitRobotEvent event : getHitRobotEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all HitRobotEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-"><code>onHitRobot(HitRobotEvent)</code></a>, 
<a href="../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHitWallEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHitWallEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&gt;&nbsp;getHitWallEvents()</pre>
<div class="block">Returns a vector containing all HitWallEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (HitWallEvent event : getHitWallEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all HitWallEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-"><code>onHitWall(HitWallEvent)</code></a>, 
<a href="../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRobotDeathEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobotDeathEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&gt;&nbsp;getRobotDeathEvents()</pre>
<div class="block">Returns a vector containing all RobotDeathEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (RobotDeathEvent event : getRobotDeathEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all RobotDeathEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-"><code>onRobotDeath(RobotDeathEvent)</code></a>, 
<a href="../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getScannedRobotEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScannedRobotEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&gt;&nbsp;getScannedRobotEvents()</pre>
<div class="block">Returns a vector containing all ScannedRobotEvents currently in the
 robot's queue. You might, for example, call this while processing another
 event.
 <p>
 Example:
 <pre>
   for (ScannedRobotEvent event : getScannedRobotEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all ScannedRobotEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a>, 
<a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getStatusEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&gt;&nbsp;getStatusEvents()</pre>
<div class="block">Returns a vector containing all StatusEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (StatusEvent event : getStatusEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all StatusEvents currently in the robot's queue</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.1</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#onStatus-robocode.StatusEvent-"><code>onStatus(StatusEvent)</code></a>, 
<a href="../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>, 
<a href="../robocode/AdvancedRobot.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdjustGunForRobotTurn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdjustGunForRobotTurn</h4>
<pre>public&nbsp;boolean&nbsp;isAdjustGunForRobotTurn()</pre>
<div class="block">Checks if the gun is set to adjust for the robot turning, i.e. to turn
 independent from the robot's body turn.
 <p>
 This call returns <code>true</code> if the gun is set to turn independent of
 the turn of the robot's body. Otherwise, <code>false</code> is returned,
 meaning that the gun is set to turn with the robot's body turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the gun is set to turn independent of the robot
         turning; <code>false</code> if the gun is set to turn with the robot
         turning</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>setAdjustGunForRobotTurn(boolean)</code></a>, 
<a href="../robocode/AdvancedRobot.html#isAdjustRadarForRobotTurn--"><code>isAdjustRadarForRobotTurn()</code></a>, 
<a href="../robocode/AdvancedRobot.html#isAdjustRadarForGunTurn--"><code>isAdjustRadarForGunTurn()</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdjustRadarForRobotTurn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdjustRadarForRobotTurn</h4>
<pre>public&nbsp;boolean&nbsp;isAdjustRadarForRobotTurn()</pre>
<div class="block">Checks if the radar is set to adjust for the robot turning, i.e. to turn
 independent from the robot's body turn.
 <p>
 This call returns <code>true</code> if the radar is set to turn independent of
 the turn of the robot. Otherwise, <code>false</code> is returned, meaning that
 the radar is set to turn with the robot's turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the radar is set to turn independent of the robot
         turning; <code>false</code> if the radar is set to turn with the robot
         turning</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/AdvancedRobot.html#isAdjustGunForRobotTurn--"><code>isAdjustGunForRobotTurn()</code></a>, 
<a href="../robocode/AdvancedRobot.html#isAdjustRadarForGunTurn--"><code>isAdjustRadarForGunTurn()</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdjustRadarForGunTurn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdjustRadarForGunTurn</h4>
<pre>public&nbsp;boolean&nbsp;isAdjustRadarForGunTurn()</pre>
<div class="block">Checks if the radar is set to adjust for the gun turning, i.e. to turn
 independent from the gun's turn.
 <p>
 This call returns <code>true</code> if the radar is set to turn independent of
 the turn of the gun. Otherwise, <code>false</code> is returned, meaning that
 the radar is set to turn with the gun's turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the radar is set to turn independent of the gun
         turning; <code>false</code> if the radar is set to turn with the gun
         turning</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a>, 
<a href="../robocode/AdvancedRobot.html#isAdjustGunForRobotTurn--"><code>isAdjustGunForRobotTurn()</code></a>, 
<a href="../robocode/AdvancedRobot.html#isAdjustRadarForRobotTurn--"><code>isAdjustRadarForRobotTurn()</code></a></dd>
</dl>
</li>
</ul>
<a name="onCustomEvent-robocode.CustomEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onCustomEvent</h4>
<pre>public&nbsp;void&nbsp;onCustomEvent(<a href="../robocode/CustomEvent.html" title="class in robocode">CustomEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a custom condition is met.
 <p>
 See the sample robots for examples of use, e.g. the <code>sample.Target</code>
 robot.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IAdvancedEvents.html#onCustomEvent-robocode.CustomEvent-">onCustomEvent</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the custom event that occurred</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-"><code>addCustomEvent(robocode.Condition)</code></a>, 
<a href="../robocode/CustomEvent.html" title="class in robocode"><code>CustomEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="setEventPriority-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEventPriority</h4>
<pre>public&nbsp;void&nbsp;setEventPriority(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass,
                             int&nbsp;priority)</pre>
<div class="block">Sets the priority of a class of events.
 <p>
 Events are sent to the onXXX handlers in order of priority.
 Higher priority events can interrupt lower priority events.
 For events with the same priority, newer events are always sent first.
 Valid priorities are 0 - 99, where 100 is reserved and 80 is the default
 event priority. Note that lower values means higher priority.
 <p>
 Example:
 <pre>
   setEventPriority("RobotDeathEvent", 15);
 </pre>
 <p>
 The default event priorities are listed here, where the events with
 highest priority are listed first:
 <pre>
         <a href="../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>:            -1 (cannot be changed)
   <a href="../robocode/PaintEvent.html" title="class in robocode"><code>PaintEvent</code></a>:             5
         <a href="../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>:     10
         <a href="../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>:         20
         <a href="../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>:          30
         <a href="../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>:      40
         <a href="../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>:        50
         <a href="../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>:  55
         <a href="../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>:     60
         <a href="../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>:       70
         <a href="../robocode/MessageEvent.html" title="class in robocode"><code>MessageEvent</code></a>:          75
         <a href="../robocode/CustomEvent.html" title="class in robocode"><code>CustomEvent</code></a>:           80
   <a href="../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>:           99
         <a href="../robocode/SkippedTurnEvent.html" title="class in robocode"><code>SkippedTurnEvent</code></a>:     100 (cannot be changed)
         <a href="../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>:             100 (cannot be changed)
 </pre>
 Hence, DeathEvent has the highest priority, and the WinEvent the lowest priority.
 <p>
 Note that you cannot change the priority for events with the special
 priority value -1 and priority value 100 as these event are reserved system events.
 Also note that you cannot change the priority of CustomEvent.
 Instead you must change the priority of the condition(s) for your custom event(s).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>eventClass</code> - the name of the event class (string) to set the
                   priority for</dd>
<dd><code>priority</code> - the new priority for that event class</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.5, the priority of DeathEvent was changed from 100 to -1 in
        order to let robots process pending events on its event queue before
        it dies. When the robot dies, it will not be able to process events.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getEventPriority-java.lang.String-"><code>getEventPriority(String)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setInterruptible-boolean-"><code>setInterruptible(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setInterruptible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInterruptible</h4>
<pre>public&nbsp;void&nbsp;setInterruptible(boolean&nbsp;interruptible)</pre>
<div class="block">Call this during an event handler to allow new events of the same
 priority to restart the event handler.

 <p>Example:
 </p>
 <pre>
   public void onScannedRobot(ScannedRobotEvent e) {
       fire(1);
       <b>setInterruptible(true);</b>
       ahead(100); // If you see a robot while moving ahead,
                   // this handler will start from the top
                   // Without setInterruptible(true), we wouldn't
                   // receive scan events at all!
       // We'll only get here if we don't see a robot during the move.
       out.println("Ok, I can't see anyone");
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_Robot.html#setInterruptible-boolean-">setInterruptible</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_Robot.html" title="class in robocode">_Robot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>interruptible</code> - <code>true</code> if the event handler should be
                      interrupted if new events of the same priority occurs; <code>false</code>
                      otherwise</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setEventPriority-java.lang.String-int-"><code>setEventPriority(String, int)</code></a>, 
<a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="setMaxTurnRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxTurnRate</h4>
<pre>public&nbsp;void&nbsp;setMaxTurnRate(double&nbsp;newMaxTurnRate)</pre>
<div class="block">Sets the maximum turn rate of the robot measured in degrees if the robot
 should turn slower than <a href="../robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a> (10 degress/turn).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newMaxTurnRate</code> - the new maximum turn rate of the robot measured in
                       degrees. Valid values are 0 - <a href="../robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a></dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRight-double-"><code>setTurnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnLeft-double-"><code>setTurnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setMaxVelocity-double-"><code>setMaxVelocity(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setMaxVelocity-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxVelocity</h4>
<pre>public&nbsp;void&nbsp;setMaxVelocity(double&nbsp;newMaxVelocity)</pre>
<div class="block">Sets the maximum velocity of the robot measured in pixels/turn if the
 robot should move slower than <a href="../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a> (8 pixels/turn).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newMaxVelocity</code> - the new maximum turn rate of the robot measured in
                       pixels/turn. Valid values are 0 - <a href="../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a></dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#ahead-double-"><code>Robot.ahead(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setAhead-double-"><code>setAhead(double)</code></a>, 
<a href="../robocode/Robot.html#back-double-"><code>Robot.back(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setBack-double-"><code>setBack(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setMaxTurnRate-double-"><code>setMaxTurnRate(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setResume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResume</h4>
<pre>public&nbsp;void&nbsp;setResume()</pre>
<div class="block">Sets the robot to resume the movement stopped by <a href="../robocode/Robot.html#stop--"><code>stop()</code></a>
 or <a href="../robocode/AdvancedRobot.html#setStop--"><code>setStop()</code></a>, if any.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a> or take an action that executes.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#resume--"><code>resume()</code></a>, 
<a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, 
<a href="../robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setStop--"><code>setStop()</code></a>, 
<a href="../robocode/AdvancedRobot.html#setStop-boolean-"><code>setStop(boolean)</code></a>, 
<a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a></dd>
</dl>
</li>
</ul>
<a name="setStop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStop</h4>
<pre>public&nbsp;void&nbsp;setStop()</pre>
<div class="block">This call is identical to <a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, but returns immediately, and
 will not execute until you call <a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a> or take an action that
 executes.
 <p>
 If there is already movement saved from a previous stop, this will have
 no effect.
 <p>
 This call is equivalent to calling <code>setStop(false)</code>;</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, 
<a href="../robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a>, 
<a href="../robocode/Robot.html#resume--"><code>resume()</code></a>, 
<a href="../robocode/AdvancedRobot.html#setResume--"><code>setResume()</code></a>, 
<a href="../robocode/AdvancedRobot.html#setStop-boolean-"><code>setStop(boolean)</code></a>, 
<a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a></dd>
</dl>
</li>
</ul>
<a name="setStop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStop</h4>
<pre>public&nbsp;void&nbsp;setStop(boolean&nbsp;overwrite)</pre>
<div class="block">This call is identical to <a href="../robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a>, but
 returns immediately, and will not execute until you call
 <a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a> or take an action that executes.
 <p>
 If there is already movement saved from a previous stop, you can
 overwrite it by calling <code>setStop(true)</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overwrite</code> - <code>true</code> if the movement saved from a previous stop
                  should be overwritten; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#stop--"><code>stop()</code></a>, 
<a href="../robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a>, 
<a href="../robocode/Robot.html#resume--"><code>resume()</code></a>, 
<a href="../robocode/AdvancedRobot.html#setResume--"><code>setResume()</code></a>, 
<a href="../robocode/AdvancedRobot.html#setStop--"><code>setStop()</code></a>, 
<a href="../robocode/AdvancedRobot.html#execute--"><code>execute()</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnGunLeft-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnGunLeft</h4>
<pre>public&nbsp;void&nbsp;setTurnGunLeft(double&nbsp;degrees)</pre>
<div class="block">Sets the robot's gun to turn left by degrees when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Set the gun to turn 180 degrees to the left
   setTurnGunLeft(180);

   // Set the gun to turn 90 degrees to the right instead of left
   // (overrides the previous order)
   setTurnGunLeft(-90);

   ...
   // Executes the last setTurnGunLeft()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's gun to the left.
                If <code>degrees</code> &gt; 0 the robot's gun is set to turn left.
                If <code>degrees</code> &lt; 0 the robot's gun is set to turn right.
                If <code>degrees</code> = 0 the robot's gun is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-"><code>setTurnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunRight-double-"><code>setTurnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunRightRadians-double-"><code>setTurnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnGunRight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnGunRight</h4>
<pre>public&nbsp;void&nbsp;setTurnGunRight(double&nbsp;degrees)</pre>
<div class="block">Sets the robot's gun to turn right by degrees when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Set the gun to turn 180 degrees to the right
   setTurnGunRight(180);

   // Set the gun to turn 90 degrees to the left instead of right
   // (overrides the previous order)
   setTurnGunRight(-90);

   ...
   // Executes the last setTurnGunRight()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's gun to the right.
                If <code>degrees</code> &gt; 0 the robot's gun is set to turn right.
                If <code>degrees</code> &lt; 0 the robot's gun is set to turn left.
                If <code>degrees</code> = 0 the robot's gun is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnGunRightRadians-double-"><code>setTurnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunLeft-double-"><code>setTurnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-"><code>setTurnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRadarLeft-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRadarLeft</h4>
<pre>public&nbsp;void&nbsp;setTurnRadarLeft(double&nbsp;degrees)</pre>
<div class="block">Sets the robot's radar to turn left by degrees when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Set the radar to turn 180 degrees to the left
   setTurnRadarLeft(180);

   // Set the radar to turn 90 degrees to the right instead of left
   // (overrides the previous order)
   setTurnRadarLeft(-90);

   ...
   // Executes the last setTurnRadarLeft()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's radar to the left.
                If <code>degrees</code> &gt; 0 the robot's radar is set to turn left.
                If <code>degrees</code> &lt; 0 the robot's radar is set to turn right.
                If <code>degrees</code> = 0 the robot's radar is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-"><code>setTurnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarRight-double-"><code>setTurnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-"><code>setTurnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRadarRight-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRadarRight</h4>
<pre>public&nbsp;void&nbsp;setTurnRadarRight(double&nbsp;degrees)</pre>
<div class="block">Sets the robot's radar to turn right by degrees when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Set the radar to turn 180 degrees to the right
   setTurnRadarRight(180);

   // Set the radar to turn 90 degrees to the right instead of right
   // (overrides the previous order)
   setTurnRadarRight(-90);

   ...
   // Executes the last setTurnRadarRight()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>degrees</code> - the amount of degrees to turn the robot's radar to the right.
                If <code>degrees</code> &gt; 0 the robot's radar is set to turn right.
                If <code>degrees</code> &lt; 0 the robot's radar is set to turn left.
                If <code>degrees</code> = 0 the robot's radar is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-"><code>setTurnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarLeft-double-"><code>setTurnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-"><code>setTurnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="waitFor-robocode.Condition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>waitFor</h4>
<pre>public&nbsp;void&nbsp;waitFor(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</pre>
<div class="block">Does not return until a condition is met, i.e. when a
 <a href="../robocode/Condition.html#test--"><code>Condition.test()</code></a> returns <code>true</code>.
 <p>
 This call executes immediately.
 <p>
 See the <code>sample.Crazy</code> robot for how this method can be used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that must be met before this call returns</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Condition.html" title="class in robocode"><code>Condition</code></a>, 
<a href="../robocode/Condition.html#test--"><code>Condition.test()</code></a></dd>
</dl>
</li>
</ul>
<a name="onDeath-robocode.DeathEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDeath</h4>
<pre>public&nbsp;void&nbsp;onDeath(<a href="../robocode/DeathEvent.html" title="class in robocode">DeathEvent</a>&nbsp;event)</pre>
<div class="block">This method is called if your robot dies.
 <p>
 You should override it in your robot if you want to be informed of this
 event. Actions will have no effect if called from this section. The
 intent is to allow you to perform calculations or print something out
 when the robot is killed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IBasicEvents.html#onDeath-robocode.DeathEvent-">onDeath</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/Robot.html#onDeath-robocode.DeathEvent-">onDeath</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/Robot.html" title="class in robocode">Robot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the death event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onSkippedTurn-robocode.SkippedTurnEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onSkippedTurn</h4>
<pre>public&nbsp;void&nbsp;onSkippedTurn(<a href="../robocode/SkippedTurnEvent.html" title="class in robocode">SkippedTurnEvent</a>&nbsp;event)</pre>
<div class="block">This method is called if the robot is using too much time between
 actions. When this event occur, the robot's turn is skipped, meaning that
 it cannot take action anymore in this turn.
 <p>
 If you receive 30 skipped turn event, your robot will be removed from the
 round and loose the round.
 <p>
 You will only receive this event after taking an action. So a robot in an
 infinite loop will not receive any events, and will simply be stopped.
 <p>
 No correctly working, reasonable robot should ever receive this event
 unless it is using too many CPU cycles.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IAdvancedEvents.html#onSkippedTurn-robocode.SkippedTurnEvent-">onSkippedTurn</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the skipped turn event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/SkippedTurnEvent.html" title="class in robocode"><code>SkippedTurnEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="getHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getHeadingRadians()</pre>
<div class="block">Returns the direction that the robot's body is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#getHeadingRadians--">getHeadingRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's body is facing, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/_AdvancedRobot.html#getHeadingDegrees--"><code>_AdvancedRobot.getHeadingDegrees()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunHeadingRadians--"><code>getGunHeadingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarHeadingRadians--"><code>getRadarHeadingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnLeftRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnLeftRadians(double&nbsp;radians)</pre>
<div class="block">Sets the robot's body to turn left by radians when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Set the robot to turn 180 degrees to the left
   setTurnLeftRadians(Math.PI);

   // Set the robot to turn 90 degrees to the right instead of left
   // (overrides the previous order)
   setTurnLeftRadians(-Math.PI / 2);

   ...
   // Executes the last setTurnLeftRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#setTurnLeftRadians-double-">setTurnLeftRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's body to the left.
                If <code>radians</code> &gt; 0 the robot is set to turn left.
                If <code>radians</code> &lt; 0 the robot is set to turn right.
                If <code>radians</code> = 0 the robot is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnLeft-double-"><code>setTurnLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRight-double-"><code>setTurnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRightRadians-double-"><code>setTurnRightRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRightRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnRightRadians(double&nbsp;radians)</pre>
<div class="block">Sets the robot's body to turn right by radians when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Set the robot to turn 180 degrees to the right
   setTurnRightRadians(Math.PI);

   // Set the robot to turn 90 degrees to the left instead of right
   // (overrides the previous order)
   setTurnRightRadians(-Math.PI / 2);

   ...
   // Executes the last setTurnRightRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#setTurnRightRadians-double-">setTurnRightRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's body to the right.
                If <code>radians</code> &gt; 0 the robot is set to turn right.
                If <code>radians</code> &lt; 0 the robot is set to turn left.
                If <code>radians</code> = 0 the robot is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnRight-double-"><code>setTurnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnLeft-double-"><code>setTurnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnLeftRadians-double-"><code>setTurnLeftRadians(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnLeftRadians</h4>
<pre>public&nbsp;void&nbsp;turnLeftRadians(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's body to the left by radians.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the robot's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Turn the robot 180 degrees to the left
   turnLeftRadians(Math.PI);

   // Afterwards, turn the robot 90 degrees to the right
   turnLeftRadians(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#turnLeftRadians-double-">turnLeftRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's body to the left.
                If <code>radians</code> &gt; 0 the robot will turn right.
                If <code>radians</code> &lt; 0 the robot will turn left.
                If <code>radians</code> = 0 the robot will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRightRadians</h4>
<pre>public&nbsp;void&nbsp;turnRightRadians(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's body to the right by radians.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the robot's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's body is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Turn the robot 180 degrees to the right
   turnRightRadians(Math.PI);

   // Afterwards, turn the robot 90 degrees to the left
   turnRightRadians(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#turnRightRadians-double-">turnRightRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's body to the right.
                If <code>radians</code> &gt; 0 the robot will turn right.
                If <code>radians</code> &lt; 0 the robot will turn left.
                If <code>radians</code> = 0 the robot will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getGunHeadingRadians()</pre>
<div class="block">Returns the direction that the robot's gun is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#getGunHeadingRadians--">getGunHeadingRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's gun is facing, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/_AdvancedRobot.html#getGunHeadingDegrees--"><code>_AdvancedRobot.getGunHeadingDegrees()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getHeadingRadians--"><code>getHeadingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarHeadingRadians--"><code>getRadarHeadingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getRadarHeadingRadians()</pre>
<div class="block">Returns the direction that the robot's radar is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#getRadarHeadingRadians--">getRadarHeadingRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's radar is facing, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/_AdvancedRobot.html#getRadarHeadingDegrees--"><code>_AdvancedRobot.getRadarHeadingDegrees()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getHeadingRadians--"><code>getHeadingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunHeadingRadians--"><code>getGunHeadingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnGunLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnGunLeftRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnGunLeftRadians(double&nbsp;radians)</pre>
<div class="block">Sets the robot's gun to turn left by radians when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Set the gun to turn 180 degrees to the left
   setTurnGunLeftRadians(Math.PI);

   // Set the gun to turn 90 degrees to the right instead of left
   // (overrides the previous order)
   setTurnGunLeftRadians(-Math.PI / 2);

   ...
   // Executes the last setTurnGunLeftRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#setTurnGunLeftRadians-double-">setTurnGunLeftRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's gun to the left.
                If <code>radians</code> &gt; 0 the robot's gun is set to turn left.
                If <code>radians</code> &lt; 0 the robot's gun is set to turn right.
                If <code>radians</code> = 0 the robot's gun is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnGunLeft-double-"><code>setTurnGunLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunRight-double-"><code>setTurnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunRightRadians-double-"><code>setTurnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnGunRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnGunRightRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnGunRightRadians(double&nbsp;radians)</pre>
<div class="block">Sets the robot's gun to turn right by radians when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Set the gun to turn 180 degrees to the right
   setTurnGunRightRadians(Math.PI);

   // Set the gun to turn 90 degrees to the left instead of right
   // (overrides the previous order)
   setTurnGunRightRadians(-Math.PI / 2);

   ...
   // Executes the last setTurnGunRightRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#setTurnGunRightRadians-double-">setTurnGunRightRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's gun to the right.
                If <code>radians</code> &gt; 0 the robot's gun is set to turn left.
                If <code>radians</code> &lt; 0 the robot's gun is set to turn right.
                If <code>radians</code> = 0 the robot's gun is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnGunRight-double-"><code>setTurnGunRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunLeft-double-"><code>setTurnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-"><code>setTurnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRadarLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRadarLeftRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnRadarLeftRadians(double&nbsp;radians)</pre>
<div class="block">Sets the robot's radar to turn left by radians when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Set the radar to turn 180 degrees to the left
   setTurnRadarLeftRadians(Math.PI);

   // Set the radar to turn 90 degrees to the right instead of left
   // (overrides the previous order)
   setTurnRadarLeftRadians(-Math.PI / 2);

   ...
   // Executes the last setTurnRadarLeftRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#setTurnRadarLeftRadians-double-">setTurnRadarLeftRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's radar to the left.
                If <code>radians</code> &gt; 0 the robot's radar is set to turn left.
                If <code>radians</code> &lt; 0 the robot's radar is set to turn right.
                If <code>radians</code> = 0 the robot's radar is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnRadarLeft-double-"><code>setTurnRadarLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarRight-double-"><code>setTurnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-"><code>setTurnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>Robot.setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>Robot.setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRadarRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRadarRightRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnRadarRightRadians(double&nbsp;radians)</pre>
<div class="block">Sets the robot's radar to turn right by radians when the next execution
 takes place.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Set the radar to turn 180 degrees to the right
   setTurnRadarRightRadians(Math.PI);

   // Set the radar to turn 90 degrees to the right instead of right
   // (overrides the previous order)
   setTurnRadarRightRadians(-Math.PI / 2);

   ...
   // Executes the last setTurnRadarRightRadians()
   execute();
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#setTurnRadarRightRadians-double-">setTurnRadarRightRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's radar to the right.
                If <code>radians</code> &gt; 0 the robot's radar is set to turn left.
                If <code>radians</code> &lt; 0 the robot's radar is set to turn right.
                If <code>radians</code> = 0 the robot's radar is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#setTurnRadarRight-double-"><code>setTurnRadarRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarLeft-double-"><code>setTurnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-"><code>setTurnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>Robot.setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>Robot.setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGunLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunLeftRadians</h4>
<pre>public&nbsp;void&nbsp;turnGunLeftRadians(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's gun to the left by radians.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the gun's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Turn the robot's gun 180 degrees to the left
   turnGunLeftRadians(Math.PI);

   // Afterwards, turn the robot's gun 90 degrees to the right
   turnGunLeftRadians(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#turnGunLeftRadians-double-">turnGunLeftRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's gun to the left.
                If <code>radians</code> &gt; 0 the robot's gun will turn left.
                If <code>radians</code> &lt; 0 the robot's gun will turn right.
                If <code>radians</code> = 0 the robot's gun will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGunRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunRightRadians</h4>
<pre>public&nbsp;void&nbsp;turnGunRightRadians(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's gun to the right by radians.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the gun's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's gun is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Turn the robot's gun 180 degrees to the right
   turnGunRightRadians(Math.PI);

   // Afterwards, turn the robot's gun 90 degrees to the left
   turnGunRightRadians(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#turnGunRightRadians-double-">turnGunRightRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's gun to the right.
                If <code>radians</code> &gt; 0 the robot's gun will turn right.
                If <code>radians</code> &lt; 0 the robot's gun will turn left.
                If <code>radians</code> = 0 the robot's gun will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-"><code>turnRadarLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-"><code>turnRadarRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-"><code>Robot.setAdjustGunForRobotTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRadarLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRadarLeftRadians</h4>
<pre>public&nbsp;void&nbsp;turnRadarLeftRadians(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's radar to the left by radians.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the radar's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn right
 instead of left.
 <p>
 Example:
 <pre>
   // Turn the robot's radar 180 degrees to the left
   turnRadarLeftRadians(Math.PI);

   // Afterwards, turn the robot's radar 90 degrees to the right
   turnRadarLeftRadians(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#turnRadarLeftRadians-double-">turnRadarLeftRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's radar to the left.
                If <code>radians</code> &gt; 0 the robot's radar will turn left.
                If <code>radians</code> &lt; 0 the robot's radar will turn right.
                If <code>radians</code> = 0 the robot's radar will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>Robot.setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>Robot.setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRadarRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRadarRightRadians</h4>
<pre>public&nbsp;void&nbsp;turnRadarRightRadians(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's radar to the right by radians.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the radar's turn is 0.
 <p>
 Note that both positive and negative values can be given as input,
 where negative values means that the robot's radar is set to turn left
 instead of right.
 <p>
 Example:
 <pre>
   // Turn the robot's radar 180 degrees to the right
   turnRadarRightRadians(Math.PI);

   // Afterwards, turn the robot's radar 90 degrees to the left
   turnRadarRightRadians(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#turnRadarRightRadians-double-">turnRadarRightRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's radar to the right.
                If <code>radians</code> &gt; 0 the robot's radar will turn right.
                If <code>radians</code> &lt; 0 the robot's radar will turn left.
                If <code>radians</code> = 0 the robot's radar will not turn, but execute.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#turnRadarRight-double-"><code>Robot.turnRadarRight(double)</code></a>, 
<a href="../robocode/Robot.html#turnRadarLeft-double-"><code>Robot.turnRadarLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnLeft-double-"><code>Robot.turnLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-"><code>turnLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnRight-double-"><code>Robot.turnRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnRightRadians-double-"><code>turnRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunLeft-double-"><code>Robot.turnGunLeft(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-"><code>turnGunLeftRadians(double)</code></a>, 
<a href="../robocode/Robot.html#turnGunRight-double-"><code>Robot.turnGunRight(double)</code></a>, 
<a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-"><code>turnGunRightRadians(double)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-"><code>Robot.setAdjustRadarForRobotTurn(boolean)</code></a>, 
<a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-"><code>Robot.setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getGunTurnRemainingRadians()</pre>
<div class="block">Returns the angle remaining in the gun's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the gun is currently turning to the right. Negative values
 means that the gun is currently turning to the left.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the gun's turn, in radians</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getTurnRemaining--"><code>getTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--"><code>getTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--"><code>getRadarTurnRemainingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getRadarTurnRemainingRadians()</pre>
<div class="block">Returns the angle remaining in the radar's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the radar is currently turning to the right. Negative values
 means that the radar is currently turning to the left.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the radar's turn, in radians</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getTurnRemaining--"><code>getTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--"><code>getTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--"><code>getGunTurnRemainingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="getTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getTurnRemainingRadians()</pre>
<div class="block">Returns the angle remaining in the robot's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently turning to the right. Negative values
 means that the robot is currently turning to the left.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/_AdvancedRadiansRobot.html#getTurnRemainingRadians--">getTurnRemainingRadians</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the robot's turn, in radians</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getTurnRemaining--"><code>getTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--"><code>getGunTurnRemainingRadians()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a>, 
<a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--"><code>getRadarTurnRemainingRadians()</code></a></dd>
</dl>
</li>
</ul>
<a name="getAdvancedEventListener--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAdvancedEventListener</h4>
<pre>public final&nbsp;<a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a>&nbsp;getAdvancedEventListener()</pre>
<div class="block">Do not call this method!
 <p>
 This method is called by the game to notify this robot about advanced
 robot event. Hence, this method must be implemented so it returns your
 <a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces"><code>IAdvancedEvents</code></a> listener.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/IAdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>listener to advanced events or <code>null</code> if this robot should
         not receive the notifications.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/_RobotBase.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/BattleEndedEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/AdvancedRobot.html" target="_top">Frames</a></li>
<li><a href="AdvancedRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
