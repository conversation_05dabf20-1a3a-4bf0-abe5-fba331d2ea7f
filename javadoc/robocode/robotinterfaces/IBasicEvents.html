<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IBasicEvents (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IBasicEvents (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/IBasicEvents.html" target="_top">Frames</a></li>
<li><a href="IBasicEvents.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces</div>
<h2 title="Interface IBasicEvents" class="title">Interface IBasicEvents</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a>, <a href="../../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a>, <a href="../../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a>, <a href="../../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a>, <a href="../../robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a>, <a href="../../robocode/Robot.html" title="class in robocode">Robot</a>, <a href="../../robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IBasicEvents</span></pre>
<div class="block">An event interface for receiving basic robot events with an
 <a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><code>IBasicRobot</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><code>IBasicRobot</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit</a></span>(<a href="../../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&nbsp;event)</code>
<div class="block">This method is called when one of your bullets hits another robot.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet</a></span>(<a href="../../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&nbsp;event)</code>
<div class="block">This method is called when one of your bullets hits another bullet.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed</a></span>(<a href="../../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when one of your bullets misses, i.e.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onDeath-robocode.DeathEvent-">onDeath</a></span>(<a href="../../robocode/DeathEvent.html" title="class in robocode">DeathEvent</a>&nbsp;event)</code>
<div class="block">This method is called if your robot dies.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet</a></span>(<a href="../../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot is hit by a bullet.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot</a></span>(<a href="../../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot collides with another robot.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onHitWall-robocode.HitWallEvent-">onHitWall</a></span>(<a href="../../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot collides with a wall.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath</a></span>(<a href="../../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&nbsp;event)</code>
<div class="block">This method is called when another robot dies.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot</a></span>(<a href="../../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot sees another robot, i.e.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onStatus-robocode.StatusEvent-">onStatus</a></span>(<a href="../../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&nbsp;event)</code>
<div class="block">This method is called every turn in a battle round in order to provide
 the robot status as a complete snapshot of the robot's current state at
 that specific time.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IBasicEvents.html#onWin-robocode.WinEvent-">onWin</a></span>(<a href="../../robocode/WinEvent.html" title="class in robocode">WinEvent</a>&nbsp;event)</code>
<div class="block">This method is called if your robot wins a battle.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="onStatus-robocode.StatusEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onStatus</h4>
<pre>void&nbsp;onStatus(<a href="../../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&nbsp;event)</pre>
<div class="block">This method is called every turn in a battle round in order to provide
 the robot status as a complete snapshot of the robot's current state at
 that specific time.
 <p>
 The main benefit of this method is that you'll automatically receive all
 current data values of the robot like e.g. the x and y coordinate,
 heading, gun heat etc., which are grouped into the exact same time/turn.
 <p>
 This is the only way to map the robots data values to a specific time.
 For example, it is not possible to determine the exact time of the
 robot's heading by calling first calling <a href="../../robocode/Robot.html#getTime--"><code>Robot.getTime()</code></a> and then
 <a href="../../robocode/Robot.html#getHeading--"><code>Robot.getHeading()</code></a> afterwards, as the time <em>might</em> change
 after between the <a href="../../robocode/Robot.html#getTime--"><code>Robot.getTime()</code></a> and <a href="../../robocode/Robot.html#getHeading--"><code>Robot.getHeading()</code></a>
 call.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event containing the robot status at the time it occurred.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.5</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onBulletHit-robocode.BulletHitEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBulletHit</h4>
<pre>void&nbsp;onBulletHit(<a href="../../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when one of your bullets hits another robot.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   public void onBulletHit(BulletHitEvent event) {
       out.println("I hit " + event.getName() + "!");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the bullet-hit event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onBulletHitBullet-robocode.BulletHitBulletEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBulletHitBullet</h4>
<pre>void&nbsp;onBulletHitBullet(<a href="../../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when one of your bullets hits another bullet.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   public void onBulletHitBullet(BulletHitBulletEvent event) {
       out.println("I hit a bullet fired by " + event.getBullet().getName() + "!");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the bullet-hit-bullet event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onBulletMissed-robocode.BulletMissedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBulletMissed</h4>
<pre>void&nbsp;onBulletMissed(<a href="../../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when one of your bullets misses, i.e. hits a wall.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   public void onBulletMissed(BulletMissedEvent event) {
       out.println("Drat, I missed.");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the bullet-missed event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onDeath-robocode.DeathEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDeath</h4>
<pre>void&nbsp;onDeath(<a href="../../robocode/DeathEvent.html" title="class in robocode">DeathEvent</a>&nbsp;event)</pre>
<div class="block">This method is called if your robot dies.
 <p>
 You should override it in your robot if you want to be informed of this
 event. Actions will have no effect if called from this section. The
 intent is to allow you to perform calculations or print something out
 when the robot is killed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the death event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>, 
<a href="../../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>, 
<a href="../../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>, 
<a href="../../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitByBullet-robocode.HitByBulletEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitByBullet</h4>
<pre>void&nbsp;onHitByBullet(<a href="../../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot is hit by a bullet.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   void onHitByBullet(HitByBulletEvent event) {
       out.println(event.getRobotName() + " hit me!");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the hit-by-bullet event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitRobot-robocode.HitRobotEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitRobot</h4>
<pre>void&nbsp;onHitRobot(<a href="../../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot collides with another robot.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   void onHitRobot(HitRobotEvent event) {
       if (event.getBearing() &gt; -90 &amp;&amp; event.getBearing() &lt;= 90) {
           back(100);
       } else {
           ahead(100);
       }
   }
 </pre><p>
   -- or perhaps, for a more advanced robot --
 </p><pre>
   public void onHitRobot(HitRobotEvent event) {
       if (event.getBearing() &gt; -90 &amp;&amp; event.getBearing() &lt;= 90) {
           setBack(100);
       } else {
           setAhead(100);
       }
   }
 </pre>
 <p>
 The angle is relative to your robot's facing. So 0 is straight ahead of
 you.
 <p>
 This event can be generated if another robot hits you, in which case
 <a href="../../robocode/HitRobotEvent.html#isMyFault--"><code>event.isMyFault()</code></a> will return
 <code>false</code>. In this case, you will not be automatically stopped by the
 game -- but if you continue moving toward the robot you will hit it (and
 generate another event). If you are moving away, then you won't hit it.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the hit-robot event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onHitWall-robocode.HitWallEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onHitWall</h4>
<pre>void&nbsp;onHitWall(<a href="../../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot collides with a wall.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 The wall at the top of the screen is 0 degrees, right is 90 degrees,
 bottom is 180 degrees, left is 270 degrees. But this event is relative to
 your heading, so: The bearing is such that <a href="../../robocode/Robot.html#turnRight-double-"><code>turnRight</code></a> <a href="../../robocode/HitWallEvent.html#getBearing--"><code>(event.getBearing())</code></a> will
 point you perpendicular to the wall.
 <p>
 Example:
 <pre>
   void onHitWall(HitWallEvent event) {
       out.println("Ouch, I hit a wall bearing " + event.getBearing() + " degrees.");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the hit-wall event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onScannedRobot-robocode.ScannedRobotEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onScannedRobot</h4>
<pre>void&nbsp;onScannedRobot(<a href="../../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot sees another robot, i.e. when the
 robot's radar scan "hits" another robot.
 You should override it in your robot if you want to be informed of this
 event. (Almost all robots should override this!)
 <p>
 This event is automatically called if there is a robot in range of your
 radar.
 <p>
 Note that the robot's radar can only see robot within the range defined
 by <a href="../../robocode/Rules.html#RADAR_SCAN_RADIUS"><code>Rules.RADAR_SCAN_RADIUS</code></a> (1200 pixels).
 <p>
 Also not that the bearing of the scanned robot is relative to your
 robot's heading.
 <p>
 Example:
 <pre>
   void onScannedRobot(ScannedRobotEvent event) {
       // Assuming radar and gun are aligned...
       if (event.getDistance() &lt; 100) {
           fire(3);
       } else {
           fire(1);
       }
   }
 </pre>
 <p>
 <b>Note:</b><br>
 The game assists Robots in firing, as follows:
 <ul>
 <li>If the gun and radar are aligned (and were aligned last turn),
 <li>and the event is current,
 <li>and you call fire() before taking any other actions, <a href="../../robocode/Robot.html#fire-double-"><code>fire()</code></a> will fire directly at the robot.
 </ul>
 <p>
 In essence, this means that if you can see a robot, and it doesn't move,
 then fire will hit it.
 <p>
 AdvancedRobots will NOT be assisted in this manner, and are expected to
 examine the event to determine if <a href="../../robocode/Robot.html#fire-double-"><code>fire()</code></a> would
 hit. (i.e. you are spinning your gun around, but by the time you get the
 event, your gun is 5 degrees past the robot).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the scanned-robot event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a>, 
<a href="../../robocode/Rules.html#RADAR_SCAN_RADIUS"><code>Rules.RADAR_SCAN_RADIUS</code></a></dd>
</dl>
</li>
</ul>
<a name="onRobotDeath-robocode.RobotDeathEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onRobotDeath</h4>
<pre>void&nbsp;onRobotDeath(<a href="../../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when another robot dies.
 You should override it in your robot if you want to be informed of this
 event.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - The robot-death event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="onWin-robocode.WinEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onWin</h4>
<pre>void&nbsp;onWin(<a href="../../robocode/WinEvent.html" title="class in robocode">WinEvent</a>&nbsp;event)</pre>
<div class="block">This method is called if your robot wins a battle.
 <p>
 Your robot could perform a victory dance here! :-)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the win event set by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>, 
<a href="../../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>, 
<a href="../../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>, 
<a href="../../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/IBasicEvents.html" target="_top">Frames</a></li>
<li><a href="IBasicEvents.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
