<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IAdvancedRobotPeer (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IAdvancedRobotPeer (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IAdvancedRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces.peer</div>
<h2 title="Interface IAdvancedRobotPeer" class="title">Interface IAdvancedRobotPeer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IAdvancedRobotPeer</span>
extends <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></pre>
<div class="block">The advanced robot peer for advanced robot types like
 <a href="../../../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> and <a href="../../../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>.
 <p>
 A robot peer is the object that deals with game mechanics and rules, and
 makes sure your robot abides by them.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IBasicRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IStandardRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>ITeamRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IJuniorRobotPeer</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#addCustomEvent-robocode.Condition-">addCustomEvent</a></span>(<a href="../../../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</code>
<div class="block">Registers a custom event to be called when a condition is met.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#clearAllEvents--">clearAllEvents</a></span>()</code>
<div class="block">Clears out any pending events in the robot's event queue immediately.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/Event.html" title="class in robocode">Event</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--">getAllEvents</a></span>()</code>
<div class="block">Returns a vector containing all events currently in the robot's queue.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitBulletEvents--">getBulletHitBulletEvents</a></span>()</code>
<div class="block">Returns a vector containing all BulletHitBulletEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitEvents--">getBulletHitEvents</a></span>()</code>
<div class="block">Returns a vector containing all BulletHitEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletMissedEvents--">getBulletMissedEvents</a></span>()</code>
<div class="block">Returns a vector containing all BulletMissedEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataDirectory--">getDataDirectory</a></span>()</code>
<div class="block">Returns a file representing a data directory for the robot, which can be
 written to using <a href="../../../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or
 <a href="../../../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataFile-java.lang.String-">getDataFile</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filename)</code>
<div class="block">Returns a file in your data directory that you can write to using
 <a href="../../../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or <a href="../../../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataQuotaAvailable--">getDataQuotaAvailable</a></span>()</code>
<div class="block">Returns the data quota available in your data directory, i.e.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getEventPriority-java.lang.String-">getEventPriority</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass)</code>
<div class="block">Returns the current priority of a class of events.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitByBulletEvents--">getHitByBulletEvents</a></span>()</code>
<div class="block">Returns a vector containing all HitByBulletEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitRobotEvents--">getHitRobotEvents</a></span>()</code>
<div class="block">Returns a vector containing all HitRobotEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitWallEvents--">getHitWallEvents</a></span>()</code>
<div class="block">Returns a vector containing all HitWallEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getRobotDeathEvents--">getRobotDeathEvents</a></span>()</code>
<div class="block">Returns a vector containing all RobotDeathEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getScannedRobotEvents--">getScannedRobotEvents</a></span>()</code>
<div class="block">Returns a vector containing all ScannedRobotEvents currently in the
 robot's queue.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getStatusEvents--">getStatusEvents</a></span>()</code>
<div class="block">Returns a vector containing all StatusEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustGunForBodyTurn--">isAdjustGunForBodyTurn</a></span>()</code>
<div class="block">Checks if the gun is set to adjust for the robot turning, i.e.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForBodyTurn--">isAdjustRadarForBodyTurn</a></span>()</code>
<div class="block">Checks if the radar is set to adjust for the gun turning, i.e.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForGunTurn--">isAdjustRadarForGunTurn</a></span>()</code>
<div class="block">Checks if the radar is set to adjust for the robot turning, i.e.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#removeCustomEvent-robocode.Condition-">removeCustomEvent</a></span>(<a href="../../../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</code>
<div class="block">Removes a custom event that was previously added by calling
 <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#addCustomEvent-robocode.Condition-"><code>addCustomEvent(Condition)</code></a>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setEventPriority-java.lang.String-int-">setEventPriority</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass,
                int&nbsp;priority)</code>
<div class="block">Sets the priority of a class of events.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setInterruptible-boolean-">setInterruptible</a></span>(boolean&nbsp;interruptible)</code>
<div class="block">Call this during an event handler to allow new events of the same
 priority to restart the event handler.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxTurnRate-double-">setMaxTurnRate</a></span>(double&nbsp;newMaxTurnRate)</code>
<div class="block">Sets the maximum turn rate of the robot measured in degrees if the robot
 should turn slower than <a href="../../../robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a> (10 degress/turn).</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxVelocity-double-">setMaxVelocity</a></span>(double&nbsp;newMaxVelocity)</code>
<div class="block">Sets the maximum velocity of the robot measured in pixels/turn if the
 robot should move slower than <a href="../../../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a> (8 pixels/turn).</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-">setMove</a></span>(double&nbsp;distance)</code>
<div class="block">Sets the robot to move forward or backward by distance measured in pixels
 when the next execution takes place.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setResume--">setResume</a></span>()</code>
<div class="block">Sets the robot to resume the movement stopped by
 <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a> or
 <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setStop-boolean-"><code>setStop(boolean)</code></a>, if any.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setStop-boolean-">setStop</a></span>(boolean&nbsp;overwrite)</code>
<div class="block">This call is identical to <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a>, but returns immediately, and will not execute until you
 call <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that executes.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-">setTurnBody</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's body to turn right or left by radians when the next
 execution takes place.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnGun-double-">setTurnGun</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's gun to turn right or left by radians when the next
 execution takes place.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnRadar-double-">setTurnRadar</a></span>(double&nbsp;radians)</code>
<div class="block">Sets the robot's radar to turn right or left by radians when the next
 execution takes place.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#waitFor-robocode.Condition-">waitFor</a></span>(<a href="../../../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</code>
<div class="block">Does not return until a condition is met, i.e.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.peer.IStandardRobotPeer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.peer.<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></h3>
<code><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--">resume</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustGunForBodyTurn-boolean-">setAdjustGunForBodyTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForBodyTurn-boolean-">setAdjustRadarForBodyTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-">stop</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#turnRadar-double-">turnRadar</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.peer.IBasicRobotPeer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.peer.<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></h3>
<code><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--">execute</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#fire-double-">fire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--">getBodyHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--">getBodyTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getCall--">getCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--">getDistanceRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getEnergy--">getEnergy</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGraphics--">getGraphics</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--">getGunHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--">getGunHeat</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--">getGunTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getName--">getName</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--">getNumRounds</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumSentries--">getNumSentries</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getOthers--">getOthers</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--">getRadarHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--">getRadarTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRoundNum--">getRoundNum</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getTime--">getTime</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getVelocity--">getVelocity</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--">getX</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--">getY</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-">move</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#rescan--">rescan</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setCall--">setCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-">setFire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-">turnBody</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-">turnGun</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isAdjustGunForBodyTurn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdjustGunForBodyTurn</h4>
<pre>boolean&nbsp;isAdjustGunForBodyTurn()</pre>
<div class="block">Checks if the gun is set to adjust for the robot turning, i.e. to turn
 independent from the robot's body turn.
 <p>
 This call returns <code>true</code> if the gun is set to turn independent of
 the turn of the robot's body. Otherwise, <code>false</code> is returned,
 meaning that the gun is set to turn with the robot's body turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the gun is set to turn independent of the robot
         turning; <code>false</code> if the gun is set to turn with the robot
         turning</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustGunForBodyTurn-boolean-"><code>setAdjustGunForBodyTurn(boolean)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForBodyTurn--"><code>isAdjustRadarForBodyTurn()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForGunTurn--"><code>isAdjustRadarForGunTurn()</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdjustRadarForGunTurn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdjustRadarForGunTurn</h4>
<pre>boolean&nbsp;isAdjustRadarForGunTurn()</pre>
<div class="block">Checks if the radar is set to adjust for the robot turning, i.e. to turn
 independent from the robot's body turn.
 <p>
 This call returns <code>true</code> if the radar is set to turn independent of
 the turn of the robot. Otherwise, <code>false</code> is returned, meaning that
 the radar is set to turn with the robot's turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the radar is set to turn independent of the robot
         turning; <code>false</code> if the radar is set to turn with the robot
         turning</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForBodyTurn-boolean-"><code>setAdjustRadarForBodyTurn(boolean)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustGunForBodyTurn--"><code>isAdjustGunForBodyTurn()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForGunTurn--"><code>isAdjustRadarForGunTurn()</code></a></dd>
</dl>
</li>
</ul>
<a name="isAdjustRadarForBodyTurn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAdjustRadarForBodyTurn</h4>
<pre>boolean&nbsp;isAdjustRadarForBodyTurn()</pre>
<div class="block">Checks if the radar is set to adjust for the gun turning, i.e. to turn
 independent from the gun's turn.
 <p>
 This call returns <code>true</code> if the radar is set to turn independent of
 the turn of the gun. Otherwise, <code>false</code> is returned, meaning that
 the radar is set to turn with the gun's turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the radar is set to turn independent of the gun
         turning; <code>false</code> if the radar is set to turn with the gun
         turning</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustGunForBodyTurn--"><code>isAdjustGunForBodyTurn()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForBodyTurn--"><code>isAdjustRadarForBodyTurn()</code></a></dd>
</dl>
</li>
</ul>
<a name="setStop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStop</h4>
<pre>void&nbsp;setStop(boolean&nbsp;overwrite)</pre>
<div class="block">This call is identical to <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a>, but returns immediately, and will not execute until you
 call <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that executes.
 <p>
 If there is already movement saved from a previous stop, you can
 overwrite it by calling <code>setStop(true)</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overwrite</code> - <code>true</code> if the movement saved from a previous stop
                  should be overwritten; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--"><code>resume()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setResume--"><code>setResume()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a></dd>
</dl>
</li>
</ul>
<a name="setResume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResume</h4>
<pre>void&nbsp;setResume()</pre>
<div class="block">Sets the robot to resume the movement stopped by
 <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a> or
 <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setStop-boolean-"><code>setStop(boolean)</code></a>, if any.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that executes.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--"><code>resume()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setStop-boolean-"><code>setStop(boolean)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a></dd>
</dl>
</li>
</ul>
<a name="setMove-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMove</h4>
<pre>void&nbsp;setMove(double&nbsp;distance)</pre>
<div class="block">Sets the robot to move forward or backward by distance measured in pixels
 when the next execution takes place.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that executes.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot is set to move forward, and negative
 values means that the robot is set to move backward. If 0 is given as
 input, the robot will stop its movement, but will have to decelerate
 till it stands still, and will thus not be able to stop its movement
 immediately, but eventually.
 <p>
 Example:
 <pre>
   // Set the robot to move 50 pixels forward
   setMove(50);

   // Set the robot to move 100 pixels backward
   // (overrides the previous order)
   setMove(-100);

   ...
   // Executes the last setMove()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the distance to move measured in pixels.
                 If <code>distance</code> &gt; 0 the robot is set to move forward.
                 If <code>distance</code> &lt; 0 the robot is set to move backward.
                 If <code>distance</code> = 0 the robot is set to stop its movement.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-"><code>move(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxVelocity-double-"><code>setMaxVelocity(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-"><code>setTurnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnGun-double-"><code>setTurnGun(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnRadar-double-"><code>setTurnRadar(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnBody-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnBody</h4>
<pre>void&nbsp;setTurnBody(double&nbsp;radians)</pre>
<div class="block">Sets the robot's body to turn right or left by radians when the next
 execution takes place.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that
 executes.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot's body is set to turn right, and
 negative values means that the robot's body is set to turn left.
 If 0 is given as input, the robot's body will stop turning.
 <p>
 Example:
 <pre>
   // Set the robot's body to turn 180 degrees to the right
   setTurnBody(Math.PI);

   // Set the robot's body to turn 90 degrees to the left instead of right
   // (overrides the previous order)
   setTurnBody(-Math.PI / 2);

   ...
   // Executes the last setTurnBody()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's body.
                If <code>radians</code> &gt; 0 the robot's body is set to turn right.
                If <code>radians</code> &lt; 0 the robot's body is set to turn left.
                If <code>radians</code> = 0 the robot's body is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-"><code>turnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnGun-double-"><code>setTurnGun(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnRadar-double-"><code>setTurnRadar(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxTurnRate-double-"><code>setMaxTurnRate(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-"><code>setMove(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnGun-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnGun</h4>
<pre>void&nbsp;setTurnGun(double&nbsp;radians)</pre>
<div class="block">Sets the robot's gun to turn right or left by radians when the next
 execution takes place.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that
 executes.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot's gun is set to turn right, and
 negative values means that the robot's gun is set to turn left.
 If 0 is given as input, the robot's gun will stop turning.
 <p>
 Example:
 <pre>
   // Set the robot's gun to turn 180 degrees to the right
   setTurnGun(Math.PI);

   // Set the robot's gun to turn 90 degrees to the left instead of right
   // (overrides the previous order)
   setTurnGun(-Math.PI / 2);

   ...
   // Executes the last setTurnFun()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's gun.
                If <code>radians</code> &gt; 0 the robot's gun is set to turn right.
                If <code>radians</code> &lt; 0 the robot's gun is set to turn left.
                If <code>radians</code> = 0 the robot's gun is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-"><code>turnGun(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-"><code>setTurnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnRadar-double-"><code>setTurnRadar(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-"><code>setMove(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setTurnRadar-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRadar</h4>
<pre>void&nbsp;setTurnRadar(double&nbsp;radians)</pre>
<div class="block">Sets the robot's radar to turn right or left by radians when the next
 execution takes place.
 <p>
 This call returns immediately, and will not execute until you call
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that
 executes.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot's radar is set to turn right, and
 negative values means that the robot's radar is set to turn left.
 If 0 is given as input, the robot's radar will stop turning.
 <p>
 Example:
 <pre>
   // Set the robot's radar to turn 180 degrees to the right
   setTurnRadar(Math.PI);

   // Set the robot's radar to turn 90 degrees to the left instead of right
   // (overrides the previous order)
   setTurnRadar(-Math.PI / 2);

   ...
   // Executes the last setTurnRadar()
   execute();
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's radar.
                If <code>radians</code> &gt; 0 the robot's radar is set to turn right.
                If <code>radians</code> &lt; 0 the robot's radar is set to turn left.
                If <code>radians</code> = 0 the robot's radar is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#turnRadar-double-"><code>turnRadar(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-"><code>setTurnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnGun-double-"><code>setTurnGun(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-"><code>setMove(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setMaxTurnRate-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxTurnRate</h4>
<pre>void&nbsp;setMaxTurnRate(double&nbsp;newMaxTurnRate)</pre>
<div class="block">Sets the maximum turn rate of the robot measured in degrees if the robot
 should turn slower than <a href="../../../robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a> (10 degress/turn).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newMaxTurnRate</code> - the new maximum turn rate of the robot measured in
                       degrees. Valid values are 0 - <a href="../../../robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a></dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-"><code>turnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-"><code>setTurnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxVelocity-double-"><code>setMaxVelocity(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setMaxVelocity-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxVelocity</h4>
<pre>void&nbsp;setMaxVelocity(double&nbsp;newMaxVelocity)</pre>
<div class="block">Sets the maximum velocity of the robot measured in pixels/turn if the
 robot should move slower than <a href="../../../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a> (8 pixels/turn).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>newMaxVelocity</code> - the new maximum turn rate of the robot measured in
                       pixels/turn. Valid values are 0 - <a href="../../../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a></dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-"><code>move(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-"><code>setMove(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxTurnRate-double-"><code>setMaxTurnRate(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="waitFor-robocode.Condition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>waitFor</h4>
<pre>void&nbsp;waitFor(<a href="../../../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</pre>
<div class="block">Does not return until a condition is met, i.e. when a
 <a href="../../../robocode/Condition.html#test--"><code>Condition.test()</code></a> returns <code>true</code>.
 <p>
 This call executes immediately.
 <p>
 See the <code>sample.Crazy</code> robot for how this method can be used.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that must be met before this call returns</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/Condition.html" title="class in robocode"><code>Condition</code></a>, 
<a href="../../../robocode/Condition.html#test--"><code>Condition.test()</code></a></dd>
</dl>
</li>
</ul>
<a name="setInterruptible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInterruptible</h4>
<pre>void&nbsp;setInterruptible(boolean&nbsp;interruptible)</pre>
<div class="block">Call this during an event handler to allow new events of the same
 priority to restart the event handler.
 <p>Example:
 </p>
 <pre>
   public void onScannedRobot(ScannedRobotEvent e) {
       fire(1);
       <b>setInterruptible(true);</b>
       move(100);  // If you see a robot while moving ahead,
                   // this handler will start from the top
                   // Without setInterruptible(true), we wouldn't
                   // receive scan events at all!
       // We'll only get here if we don't see a robot during the move.
       getOut().println("Ok, I can't see anyone");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>interruptible</code> - <code>true</code> if the event handler should be
                      interrupted if new events of the same priority occurs; <code>false</code>
                      otherwise</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setEventPriority-java.lang.String-int-"><code>setEventPriority(String, int)</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="setEventPriority-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEventPriority</h4>
<pre>void&nbsp;setEventPriority(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass,
                      int&nbsp;priority)</pre>
<div class="block">Sets the priority of a class of events.
 <p>
 Events are sent to the onXXX handlers in order of priority.
 Higher priority events can interrupt lower priority events.
 For events with the same priority, newer events are always sent first.
 Valid priorities are 0 - 99, where 100 is reserved and 80 is the default
 priority.
 <p>
 Example:
 <pre>
   setEventPriority("RobotDeathEvent", 15);
 </pre>
 <p>
 The default priorities are, from highest to lowest:
 <pre>
   <a href="../../../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>:      100 (reserved)
   <a href="../../../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>:     100 (reserved)
   <a href="../../../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>:             100 (reserved)
   <a href="../../../robocode/SkippedTurnEvent.html" title="class in robocode"><code>SkippedTurnEvent</code></a>:     100 (reserved)
   <a href="../../../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>:           99
   Key and mouse events:  98
   <a href="../../../robocode/CustomEvent.html" title="class in robocode"><code>CustomEvent</code></a>:           80 (default value)
   <a href="../../../robocode/MessageEvent.html" title="class in robocode"><code>MessageEvent</code></a>:          75
   <a href="../../../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>:       70
   <a href="../../../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>:     60
   <a href="../../../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>:  55
   <a href="../../../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>:        50
   <a href="../../../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>:      40
   <a href="../../../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>:          30
   <a href="../../../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>:         20
   <a href="../../../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>:     10
   <a href="../../../robocode/PaintEvent.html" title="class in robocode"><code>PaintEvent</code></a>:             5
   <a href="../../../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>:            -1 (reserved)
 </pre>
 <p>
 Note that you cannot change the priority for events with the special
 priority value -1 or 100 (reserved) as these event are system events.
 Also note that you cannot change the priority of CustomEvent.
 Instead you must change the priority of the condition(s) for your custom
 event(s).</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>eventClass</code> - the name of the event class (string) to set the
                   priority for</dd>
<dd><code>priority</code> - the new priority for that event class</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.5, the priority of DeathEvent was changed from 100 to -1 in
        order to let robots process pending events on its event queue before
        it dies. When the robot dies, it will not be able to process events.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getEventPriority-java.lang.String-"><code>getEventPriority(String)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setInterruptible-boolean-"><code>setInterruptible(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="getEventPriority-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEventPriority</h4>
<pre>int&nbsp;getEventPriority(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;eventClass)</pre>
<div class="block">Returns the current priority of a class of events.
 An event priority is a value from 0 - 99. The higher value, the higher
 priority.
 <p>
 Example:
 <pre>
   int myHitRobotPriority = getEventPriority("HitRobotEvent");
 </pre>
 <p>
 The default priorities are, from highest to lowest:
 <pre>
   <a href="../../../robocode/RoundEndedEvent.html" title="class in robocode"><code>RoundEndedEvent</code></a>:      100 (reserved)
   <a href="../../../robocode/BattleEndedEvent.html" title="class in robocode"><code>BattleEndedEvent</code></a>:     100 (reserved)
   <a href="../../../robocode/WinEvent.html" title="class in robocode"><code>WinEvent</code></a>:             100 (reserved)
   <a href="../../../robocode/SkippedTurnEvent.html" title="class in robocode"><code>SkippedTurnEvent</code></a>:     100 (reserved)
   <a href="../../../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>:           99
   Key and mouse events:  98
   <a href="../../../robocode/CustomEvent.html" title="class in robocode"><code>CustomEvent</code></a>:           80 (default value)
   <a href="../../../robocode/MessageEvent.html" title="class in robocode"><code>MessageEvent</code></a>:          75
   <a href="../../../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>:       70
   <a href="../../../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>:     60
   <a href="../../../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>:  55
   <a href="../../../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>:        50
   <a href="../../../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>:      40
   <a href="../../../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>:          30
   <a href="../../../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>:         20
   <a href="../../../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>:     10
   <a href="../../../robocode/PaintEvent.html" title="class in robocode"><code>PaintEvent</code></a>:             5
   <a href="../../../robocode/DeathEvent.html" title="class in robocode"><code>DeathEvent</code></a>:            -1 (reserved)
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>eventClass</code> - the name of the event class (string)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current priority of a class of events</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setEventPriority-java.lang.String-int-"><code>setEventPriority(String, int)</code></a></dd>
</dl>
</li>
</ul>
<a name="addCustomEvent-robocode.Condition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCustomEvent</h4>
<pre>void&nbsp;addCustomEvent(<a href="../../../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</pre>
<div class="block">Registers a custom event to be called when a condition is met.
 When you are finished with your condition or just want to remove it you
 must call <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#removeCustomEvent-robocode.Condition-"><code>removeCustomEvent(Condition)</code></a>.
 <p>
 Example:
 <pre>
   // Create the condition for our custom event
   Condition triggerHitCondition = new Condition("triggerhit") {
       public boolean test() {
           return (getEnergy() &lt;= trigger);
       };
   }

   // Add our custom event based on our condition
   <b>addCustomEvent(triggerHitCondition);</b>
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that must be met.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the condition parameter has been set to
                              <code>null</code>.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/Condition.html" title="class in robocode"><code>Condition</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#removeCustomEvent-robocode.Condition-"><code>removeCustomEvent(Condition)</code></a></dd>
</dl>
</li>
</ul>
<a name="removeCustomEvent-robocode.Condition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeCustomEvent</h4>
<pre>void&nbsp;removeCustomEvent(<a href="../../../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</pre>
<div class="block">Removes a custom event that was previously added by calling
 <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#addCustomEvent-robocode.Condition-"><code>addCustomEvent(Condition)</code></a>.
 <p>
 Example:
 <pre>
   // Create the condition for our custom event
   Condition triggerHitCondition = new Condition("triggerhit") {
       public boolean test() {
           return (getEnergy() &lt;= trigger);
       };
   }

   // Add our custom event based on our condition
   addCustomEvent(triggerHitCondition);
   ...
   <i>do something with your robot</i>
   ...
   // Remove the custom event based on our condition
   <b>removeCustomEvent(triggerHitCondition);</b>
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that was previous added and that must be
                  removed now.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the condition parameter has been set to
                              <code>null</code>.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/Condition.html" title="class in robocode"><code>Condition</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#addCustomEvent-robocode.Condition-"><code>addCustomEvent(Condition)</code></a></dd>
</dl>
</li>
</ul>
<a name="clearAllEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clearAllEvents</h4>
<pre>void&nbsp;clearAllEvents()</pre>
<div class="block">Clears out any pending events in the robot's event queue immediately.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getAllEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/Event.html" title="class in robocode">Event</a>&gt;&nbsp;getAllEvents()</pre>
<div class="block">Returns a vector containing all events currently in the robot's queue.
 You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (Event event : getAllEvents()) {
       if (event instanceof HitRobotEvent) {
           <i>// do something with the event</i>
       } else if (event instanceof HitByBulletEvent) {
           <i>// do something with the event</i>
       }
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all events currently in the robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/Event.html" title="class in robocode"><code>Event</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#clearAllEvents--"><code>clearAllEvents()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getStatusEvents--"><code>getStatusEvents()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getScannedRobotEvents--"><code>getScannedRobotEvents()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitEvents--"><code>getBulletHitEvents()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletMissedEvents--"><code>getBulletMissedEvents()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitBulletEvents--"><code>getBulletHitBulletEvents()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getRobotDeathEvents--"><code>getRobotDeathEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getStatusEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a>&gt;&nbsp;getStatusEvents()</pre>
<div class="block">Returns a vector containing all StatusEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (StatusEvent event : getStatusEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all StatusEvents currently in the robot's
         queue</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.1</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onStatus-robocode.StatusEvent-"><code>onStatus(StatusEvent)</code></a>, 
<a href="../../../robocode/StatusEvent.html" title="class in robocode"><code>StatusEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBulletMissedEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletMissedEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a>&gt;&nbsp;getBulletMissedEvents()</pre>
<div class="block">Returns a vector containing all BulletMissedEvents currently in the
 robot's queue. You might, for example, call this while processing another
 event.
 <p>
 Example:
 <pre>
   for (BulletMissedEvent event : getBulletMissedEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all BulletMissedEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a>, 
<a href="../../../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBulletHitBulletEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletHitBulletEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a>&gt;&nbsp;getBulletHitBulletEvents()</pre>
<div class="block">Returns a vector containing all BulletHitBulletEvents currently in the
 robot's queue. You might, for example, call this while processing another
 event.
 <p>
 Example:
 <pre>
   for (BulletHitBulletEvent event : getBulletHitBulletEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all BulletHitBulletEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../../../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBulletHitEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletHitEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a>&gt;&nbsp;getBulletHitEvents()</pre>
<div class="block">Returns a vector containing all BulletHitEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (BulletHitEvent event: getBulletHitEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all BulletHitEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../../../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHitByBulletEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHitByBulletEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a>&gt;&nbsp;getHitByBulletEvents()</pre>
<div class="block">Returns a vector containing all HitByBulletEvents currently in the
 robot's queue. You might, for example, call this while processing
 another event.
 <p>
 Example:
 <pre>
   for (HitByBulletEvent event : getHitByBulletEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all HitByBulletEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onHitByBullet-robocode.HitByBulletEvent-"><code>onHitByBullet(HitByBulletEvent)</code></a>, 
<a href="../../../robocode/HitByBulletEvent.html" title="class in robocode"><code>HitByBulletEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHitRobotEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHitRobotEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a>&gt;&nbsp;getHitRobotEvents()</pre>
<div class="block">Returns a vector containing all HitRobotEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (HitRobotEvent event : getHitRobotEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all HitRobotEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onHitRobot-robocode.HitRobotEvent-"><code>onHitRobot(HitRobotEvent)</code></a>, 
<a href="../../../robocode/HitRobotEvent.html" title="class in robocode"><code>HitRobotEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHitWallEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHitWallEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a>&gt;&nbsp;getHitWallEvents()</pre>
<div class="block">Returns a vector containing all HitWallEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (HitWallEvent event : getHitWallEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all HitWallEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onHitWall-robocode.HitWallEvent-"><code>onHitWall(HitWallEvent)</code></a>, 
<a href="../../../robocode/HitWallEvent.html" title="class in robocode"><code>HitWallEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRobotDeathEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobotDeathEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a>&gt;&nbsp;getRobotDeathEvents()</pre>
<div class="block">Returns a vector containing all RobotDeathEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (RobotDeathEvent event : getRobotDeathEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all RobotDeathEvents currently in the robot's
         queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onRobotDeath-robocode.RobotDeathEvent-"><code>onRobotDeath(RobotDeathEvent)</code></a>, 
<a href="../../../robocode/RobotDeathEvent.html" title="class in robocode"><code>RobotDeathEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getScannedRobotEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScannedRobotEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a>&gt;&nbsp;getScannedRobotEvents()</pre>
<div class="block">Returns a vector containing all ScannedRobotEvents currently in the
 robot's queue. You might, for example, call this while processing another
 event.
 <p>
 Example:
 <pre>
   for (ScannedRobotEvent event : getScannedRobotEvents()) {
       <i>// do something with the event</i>
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all ScannedRobotEvents currently in the
         robot's queue</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a>, 
<a href="../../../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--"><code>getAllEvents()</code></a></dd>
</dl>
</li>
</ul>
<a name="getDataDirectory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataDirectory</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;getDataDirectory()</pre>
<div class="block">Returns a file representing a data directory for the robot, which can be
 written to using <a href="../../../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or
 <a href="../../../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.
 <p>
 The system will automatically create the directory for you, so you do not
 need to create it by yourself.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a file representing the data directory for your robot</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataFile-java.lang.String-"><code>getDataFile(String)</code></a>, 
<a href="../../../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a>, 
<a href="../../../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a></dd>
</dl>
</li>
</ul>
<a name="getDataFile-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataFile</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;getDataFile(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;filename)</pre>
<div class="block">Returns a file in your data directory that you can write to using
 <a href="../../../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or <a href="../../../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.
 <p>
 The system will automatically create the directory for you, so you do not
 need to create it by yourself.
 <p>
 Please notice that the max. size of your data file is set to 200000
 (~195 KB).
 <p>
 See the <code>sample.SittingDuck</code> to see an example of how to use this
 method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>filename</code> - the file name of the data file for your robot</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a file representing the data file for your robot</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataDirectory--"><code>getDataDirectory()</code></a>, 
<a href="../../../robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a>, 
<a href="../../../robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a></dd>
</dl>
</li>
</ul>
<a name="getDataQuotaAvailable--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDataQuotaAvailable</h4>
<pre>long&nbsp;getDataQuotaAvailable()</pre>
<div class="block">Returns the data quota available in your data directory, i.e. the amount
 of bytes left in the data directory for the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the amount of bytes left in the robot's data directory</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataDirectory--"><code>getDataDirectory()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataFile-java.lang.String-"><code>getDataFile(String)</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IAdvancedRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
