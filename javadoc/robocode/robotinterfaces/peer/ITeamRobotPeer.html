<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ITeamRobotPeer (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ITeamRobotPeer (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/ITeamRobotPeer.html" target="_top">Frames</a></li>
<li><a href="ITeamRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces.peer</div>
<h2 title="Interface ITeamRobotPeer" class="title">Interface ITeamRobotPeer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ITeamRobotPeer</span>
extends <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></pre>
<div class="block">The team robot peer for team robots like <a href="../../../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>.
 <p>
 A robot peer is the object that deals with game mechanics and rules, and
 makes sure your robot abides by them.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IBasicRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IStandardRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IAdvancedRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IJuniorRobotPeer</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#broadcastMessage-java.io.Serializable-">broadcastMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)</code>
<div class="block">Broadcasts a message to all teammates.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/MessageEvent.html" title="class in robocode">MessageEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#getMessageEvents--">getMessageEvents</a></span>()</code>
<div class="block">Returns a vector containing all MessageEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#getTeammates--">getTeammates</a></span>()</code>
<div class="block">Returns the names of all teammates, or <code>null</code> there is no
 teammates.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#isTeammate-java.lang.String-">isTeammate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Checks if a given robot name is the name of one of your teammates.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#sendMessage-java.lang.String-java.io.Serializable-">sendMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)</code>
<div class="block">Sends a message to one (or more) teammates.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.peer.IAdvancedRobotPeer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.peer.<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></h3>
<code><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#addCustomEvent-robocode.Condition-">addCustomEvent</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#clearAllEvents--">clearAllEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--">getAllEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitBulletEvents--">getBulletHitBulletEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitEvents--">getBulletHitEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletMissedEvents--">getBulletMissedEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataDirectory--">getDataDirectory</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataFile-java.lang.String-">getDataFile</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataQuotaAvailable--">getDataQuotaAvailable</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getEventPriority-java.lang.String-">getEventPriority</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitByBulletEvents--">getHitByBulletEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitRobotEvents--">getHitRobotEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitWallEvents--">getHitWallEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getRobotDeathEvents--">getRobotDeathEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getScannedRobotEvents--">getScannedRobotEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getStatusEvents--">getStatusEvents</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustGunForBodyTurn--">isAdjustGunForBodyTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForBodyTurn--">isAdjustRadarForBodyTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForGunTurn--">isAdjustRadarForGunTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#removeCustomEvent-robocode.Condition-">removeCustomEvent</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setEventPriority-java.lang.String-int-">setEventPriority</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setInterruptible-boolean-">setInterruptible</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxTurnRate-double-">setMaxTurnRate</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxVelocity-double-">setMaxVelocity</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-">setMove</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setResume--">setResume</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setStop-boolean-">setStop</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-">setTurnBody</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnGun-double-">setTurnGun</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnRadar-double-">setTurnRadar</a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#waitFor-robocode.Condition-">waitFor</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.peer.IStandardRobotPeer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.peer.<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></h3>
<code><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--">resume</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustGunForBodyTurn-boolean-">setAdjustGunForBodyTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForBodyTurn-boolean-">setAdjustRadarForBodyTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-">stop</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#turnRadar-double-">turnRadar</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.peer.IBasicRobotPeer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.peer.<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></h3>
<code><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--">execute</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#fire-double-">fire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--">getBodyHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--">getBodyTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getCall--">getCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--">getDistanceRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getEnergy--">getEnergy</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGraphics--">getGraphics</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--">getGunHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--">getGunHeat</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--">getGunTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getName--">getName</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--">getNumRounds</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumSentries--">getNumSentries</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getOthers--">getOthers</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--">getRadarHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--">getRadarTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRoundNum--">getRoundNum</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getTime--">getTime</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getVelocity--">getVelocity</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--">getX</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--">getY</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-">move</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#rescan--">rescan</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setCall--">setCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-">setFire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-">turnBody</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-">turnGun</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTeammates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeammates</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;getTeammates()</pre>
<div class="block">Returns the names of all teammates, or <code>null</code> there is no
 teammates.
 <p>
 Example:
 <pre>
   public void run() {
       // Prints out all teammates
       String[] teammates = getTeammates();
       if (teammates != null) {
           for (String member : teammates) {
               out.println(member);
           }
       }
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a String array containing the names of all your teammates, or
         <code>null</code> if there is no teammates. The length of the String array
         is equal to the number of teammates.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#isTeammate-java.lang.String-"><code>isTeammate(String)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#broadcastMessage-java.io.Serializable-"><code>broadcastMessage(Serializable)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#sendMessage-java.lang.String-java.io.Serializable-"><code>sendMessage(String, Serializable)</code></a></dd>
</dl>
</li>
</ul>
<a name="isTeammate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTeammate</h4>
<pre>boolean&nbsp;isTeammate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Checks if a given robot name is the name of one of your teammates.
 <p>
 Example:
 <pre>
   public void onScannedRobot(ScannedRobotEvent e) {
       if (isTeammate(e.getName()) {
           return;
       }
       fire(1);
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the robot name to check</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the specified name belongs to one of your
         teammates; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#getTeammates--"><code>getTeammates()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#broadcastMessage-java.io.Serializable-"><code>broadcastMessage(Serializable)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#sendMessage-java.lang.String-java.io.Serializable-"><code>sendMessage(String, Serializable)</code></a></dd>
</dl>
</li>
</ul>
<a name="broadcastMessage-java.io.Serializable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>broadcastMessage</h4>
<pre>void&nbsp;broadcastMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)
               throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Broadcasts a message to all teammates.
 <p>
 Example:
 <pre>
   public void run() {
       broadcastMessage("I'm here!");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the message to broadcast to all teammates</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - if the message could not be broadcasted to the
                     teammates</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#isTeammate-java.lang.String-"><code>isTeammate(String)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#getTeammates--"><code>getTeammates()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#sendMessage-java.lang.String-java.io.Serializable-"><code>sendMessage(String, Serializable)</code></a></dd>
</dl>
</li>
</ul>
<a name="sendMessage-java.lang.String-java.io.Serializable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sendMessage</h4>
<pre>void&nbsp;sendMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)
          throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Sends a message to one (or more) teammates.
 <p>
 Example:
 <pre>
   public void run() {
       sendMessage("sample.DroidBot", "I'm here!");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the intended recipient of the message</dd>
<dd><code>message</code> - the message to send</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - if the message could not be sent</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#isTeammate-java.lang.String-"><code>isTeammate(String)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#getTeammates--"><code>getTeammates()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html#broadcastMessage-java.io.Serializable-"><code>broadcastMessage(Serializable)</code></a></dd>
</dl>
</li>
</ul>
<a name="getMessageEvents--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getMessageEvents</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../robocode/MessageEvent.html" title="class in robocode">MessageEvent</a>&gt;&nbsp;getMessageEvents()</pre>
<div class="block">Returns a vector containing all MessageEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (MessageEvent e : getMessageEvents()) {
      // do something with e
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all MessageEvents currently in the robot's
         queue</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.2.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/ITeamEvents.html#onMessageReceived-robocode.MessageEvent-"><code>onMessageReceived(MessageEvent)</code></a>, 
<a href="../../../robocode/MessageEvent.html" title="class in robocode"><code>MessageEvent</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/ITeamRobotPeer.html" target="_top">Frames</a></li>
<li><a href="ITeamRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
