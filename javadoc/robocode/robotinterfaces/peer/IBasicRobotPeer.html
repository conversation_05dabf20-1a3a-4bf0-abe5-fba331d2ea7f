<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IBasicRobotPeer (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IBasicRobotPeer (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IBasicRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IBasicRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces.peer</div>
<h2 title="Interface IBasicRobotPeer" class="title">Interface IBasicRobotPeer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a>, <a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IJuniorRobotPeer</a>, <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a>, <a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IBasicRobotPeer</span></pre>
<div class="block">The basic robot peer for all robot types.<p>

 NOTE: This is private interface. You should build any external component (or robot)
 based on it's current methods because it will change in the future.<p>

 A robot peer is the object that deals with game mechanics and rules, and
 makes sure your robot abides by them.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IStandardRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IAdvancedRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>ITeamRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IJuniorRobotPeer</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--">execute</a></span>()</code>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../robocode/Bullet.html" title="class in robocode">Bullet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#fire-double-">fire</a></span>(double&nbsp;power)</code>
<div class="block">Immediately fires a bullet.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldHeight--">getBattleFieldHeight</a></span>()</code>
<div class="block">Returns the height of the current battlefield measured in pixels.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldWidth--">getBattleFieldWidth</a></span>()</code>
<div class="block">Returns the width of the current battlefield measured in pixels.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--">getBodyHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's body is facing, in radians.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--">getBodyTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the robot's turn, in radians.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getCall--">getCall</a></span>()</code>
<div class="block">This call <em>must</em> be made from a robot call to inform the game
 that the robot made a <code>get*</code> call like e.g.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--">getDistanceRemaining</a></span>()</code>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getEnergy--">getEnergy</a></span>()</code>
<div class="block">Returns the robot's current energy.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Graphics2D.html?is-external=true" title="class or interface in java.awt">Graphics2D</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGraphics--">getGraphics</a></span>()</code>
<div class="block">Returns a graphics context used for painting graphical items for the robot.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--">getGunCoolingRate</a></span>()</code>
<div class="block">Returns the rate at which the gun will cool down, i.e.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--">getGunHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's gun is facing, in radians.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--">getGunHeat</a></span>()</code>
<div class="block">Returns the current heat of the gun.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--">getGunTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the gun's turn, in radians.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getName--">getName</a></span>()</code>
<div class="block">Returns the robot's name.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--">getNumRounds</a></span>()</code>
<div class="block">Returns the number of rounds in the current battle.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumSentries--">getNumSentries</a></span>()</code>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getOthers--">getOthers</a></span>()</code>
<div class="block">Returns how many opponents that are left in the current round.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--">getRadarHeading</a></span>()</code>
<div class="block">Returns the direction that the robot's radar is facing, in radians.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--">getRadarTurnRemaining</a></span>()</code>
<div class="block">Returns the angle remaining in the radar's turn, in radians.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRoundNum--">getRoundNum</a></span>()</code>
<div class="block">Returns the number of the current round (0 to <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--"><code>getNumRounds()</code></a> - 1)
 in the battle.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getSentryBorderSize--">getSentryBorderSize</a></span>()</code>
<div class="block">Returns the sentry border size for a <a href="../../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getTime--">getTime</a></span>()</code>
<div class="block">Returns the game time of the current round, where the time is equal to
 the current turn in the round.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getVelocity--">getVelocity</a></span>()</code>
<div class="block">Returns the velocity of the robot measured in pixels/turn.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--">getX</a></span>()</code>
<div class="block">Returns the X position of the robot.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--">getY</a></span>()</code>
<div class="block">Returns the Y position of the robot.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-">move</a></span>(double&nbsp;distance)</code>
<div class="block">Immediately moves your robot forward or backward by distance measured in
 pixels.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#rescan--">rescan</a></span>()</code>
<div class="block">Rescan for other robots.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-">setBodyColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's body.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-">setBulletColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's bullets.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setCall--">setCall</a></span>()</code>
<div class="block">This call <em>must</em> be made from a robot call to inform the game
 that the robot made a <code>set*</code> call like e.g.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the debug property with the specified key to the specified value.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/Bullet.html" title="class in robocode">Bullet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-">setFire</a></span>(double&nbsp;power)</code>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-">setGunColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's gun.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-">setRadarColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's radar.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-">setScanColor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</code>
<div class="block">Sets the color of the robot's scan arc.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-">turnBody</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's body to the right or left by radians.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-">turnGun</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's gun to the right or left by radians.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Returns the robot's name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot's name.</dd>
</dl>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTime</h4>
<pre>long&nbsp;getTime()</pre>
<div class="block">Returns the game time of the current round, where the time is equal to
 the current turn in the round.
 <p>
 A battle consists of multiple rounds.
 <p>
 Time is reset to 0 at the beginning of every round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the game time/turn of the current round.</dd>
</dl>
</li>
</ul>
<a name="getEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnergy</h4>
<pre>double&nbsp;getEnergy()</pre>
<div class="block">Returns the robot's current energy.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot's current energy.</dd>
</dl>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>double&nbsp;getX()</pre>
<div class="block">Returns the X position of the robot. (0,0) is at the bottom left of the
 battlefield.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the X position of the robot.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--"><code>getY()</code></a></dd>
</dl>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>double&nbsp;getY()</pre>
<div class="block">Returns the Y position of the robot. (0,0) is at the bottom left of the
 battlefield.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Y position of the robot.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--"><code>getX()</code></a></dd>
</dl>
</li>
</ul>
<a name="getVelocity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVelocity</h4>
<pre>double&nbsp;getVelocity()</pre>
<div class="block">Returns the velocity of the robot measured in pixels/turn.
 <p>
 The maximum velocity of a robot is defined by
 <a href="../../../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a> (8 pixels / turn).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the velocity of the robot measured in pixels/turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a></dd>
</dl>
</li>
</ul>
<a name="getBodyHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBodyHeading</h4>
<pre>double&nbsp;getBodyHeading()</pre>
<div class="block">Returns the direction that the robot's body is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's body is facing, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--"><code>getGunHeading()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--"><code>getRadarHeading()</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeading</h4>
<pre>double&nbsp;getGunHeading()</pre>
<div class="block">Returns the direction that the robot's gun is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's gun is facing, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--"><code>getBodyHeading()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--"><code>getRadarHeading()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarHeading</h4>
<pre>double&nbsp;getRadarHeading()</pre>
<div class="block">Returns the direction that the robot's radar is facing, in radians.
 The value returned will be between 0 and 2 * PI (is excluded).
 <p>
 Note that the heading in Robocode is like a compass, where 0 means North,
 PI / 2 means East, PI means South, and 3 * PI / 2 means West.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the direction that the robot's radar is facing, in radians.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--"><code>getBodyHeading()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--"><code>getGunHeading()</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunHeat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeat</h4>
<pre>double&nbsp;getGunHeat()</pre>
<div class="block">Returns the current heat of the gun. The gun cannot fire unless this is
 0. (Calls to fire will succeed, but will not actually fire unless
 getGunHeat() == 0).
 <p>
 The amount of gun heat generated when the gun is fired is
 1 + (firePower / 5). Each turn the gun heat drops by the amount returned
 by <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, which is a battle setup.
 <p>
 Note that all guns are "hot" at the start of each round, where the gun
 heat is 3.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current gun heat</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-"><code>setFire(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getBattleFieldWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattleFieldWidth</h4>
<pre>double&nbsp;getBattleFieldWidth()</pre>
<div class="block">Returns the width of the current battlefield measured in pixels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the width of the current battlefield measured in pixels.</dd>
</dl>
</li>
</ul>
<a name="getBattleFieldHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattleFieldHeight</h4>
<pre>double&nbsp;getBattleFieldHeight()</pre>
<div class="block">Returns the height of the current battlefield measured in pixels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the height of the current battlefield measured in pixels.</dd>
</dl>
</li>
</ul>
<a name="getOthers--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOthers</h4>
<pre>int&nbsp;getOthers()</pre>
<div class="block">Returns how many opponents that are left in the current round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>how many opponents that are left in the current round.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumSentries--"><code>getNumSentries()</code></a></dd>
</dl>
</li>
</ul>
<a name="getNumSentries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumSentries</h4>
<pre>int&nbsp;getNumSentries()</pre>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>how many sentry robots that are left in the current round.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.9.1.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getOthers--"><code>getOthers()</code></a></dd>
</dl>
</li>
</ul>
<a name="getNumRounds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumRounds</h4>
<pre>int&nbsp;getNumRounds()</pre>
<div class="block">Returns the number of rounds in the current battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds in the current battle.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRoundNum--"><code>getRoundNum()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRoundNum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoundNum</h4>
<pre>int&nbsp;getRoundNum()</pre>
<div class="block">Returns the number of the current round (0 to <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--"><code>getNumRounds()</code></a> - 1)
 in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of the current round in the battle (zero indexed).</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--"><code>getNumRounds()</code></a></dd>
</dl>
</li>
</ul>
<a name="getSentryBorderSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSentryBorderSize</h4>
<pre>int&nbsp;getSentryBorderSize()</pre>
<div class="block">Returns the sentry border size for a <a href="../../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the border size in units/pixels.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.9.0.0</dd>
</dl>
</li>
</ul>
<a name="getGunCoolingRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunCoolingRate</h4>
<pre>double&nbsp;getGunCoolingRate()</pre>
<div class="block">Returns the rate at which the gun will cool down, i.e. the amount of heat
 the gun heat will drop per turn.
 <p>
 The gun cooling rate is default 0.1 / turn, but can be changed by the
 battle setup. So don't count on the cooling rate being 0.1!</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the gun cooling rate</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-"><code>setFire(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getDistanceRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistanceRemaining</h4>
<pre>double&nbsp;getDistanceRemaining()</pre>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently moving forwards. Negative values means
 that the robot is currently moving backwards. If the returned value is 0,
 the robot currently stands still.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the distance remaining in the robot's current move measured in
         pixels.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--"><code>getBodyTurnRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBodyTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBodyTurnRemaining</h4>
<pre>double&nbsp;getBodyTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the robot's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the robot is currently turning to the right. Negative values
 means that the robot is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the robot's turn, in radians</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--"><code>getDistanceRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunTurnRemaining</h4>
<pre>double&nbsp;getGunTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the gun's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the gun is currently turning to the right. Negative values
 means that the gun is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the gun's turn, in radians</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--"><code>getDistanceRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--"><code>getBodyTurnRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--"><code>getRadarTurnRemaining()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarTurnRemaining--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarTurnRemaining</h4>
<pre>double&nbsp;getRadarTurnRemaining()</pre>
<div class="block">Returns the angle remaining in the radar's turn, in radians.
 <p>
 This call returns both positive and negative values. Positive values
 means that the radar is currently turning to the right. Negative values
 means that the radar is currently turning to the left.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the angle remaining in the radar's turn, in radians</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--"><code>getDistanceRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--"><code>getBodyTurnRemaining()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--"><code>getGunTurnRemaining()</code></a></dd>
</dl>
</li>
</ul>
<a name="execute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>execute</h4>
<pre>void&nbsp;execute()</pre>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process. This call returns after the actions have been started.
 <p>
 Note that advanced robots <em>must</em> call this function in order to
 execute pending set* calls like e.g. <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-"><code>setMove(double)</code></a>,
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-"><code>setFire(double)</code></a>, <a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-"><code>setTurnBody(double)</code></a> etc.
 Otherwise, these calls will never get executed.
 <p>
 In this example the robot will move while turning:
 <pre>
   setTurnBody(90);
   setMove(100);
   execute();

   while (getDistanceRemaining() &gt; 0 &amp;&amp; getTurnRemaining() &gt; 0) {
       execute();
   }
 </pre></div>
</li>
</ul>
<a name="move-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>move</h4>
<pre>void&nbsp;move(double&nbsp;distance)</pre>
<div class="block">Immediately moves your robot forward or backward by distance measured in
 pixels.
 <p>
 This call executes immediately, and does not return until it is complete,
 i.e. when the remaining distance to move is 0.
 <p>
 If the robot collides with a wall, the move is complete, meaning that the
 robot will not move any further. If the robot collides with another
 robot, the move is complete if you are heading toward the other robot.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot is set to move forward, and negative
 values means that the robot is set to move backward.
 <p>
 Example:
 <pre>
   // Move the robot 100 pixels forward
   ahead(100);

   // Afterwards, move the robot 50 pixels backward
   ahead(-50);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the distance to move measured in pixels.
                 If <code>distance</code> &gt; 0 the robot is set to move forward.
                 If <code>distance</code> &lt; 0 the robot is set to move backward.
                 If <code>distance</code> = 0 the robot will not move anywhere, but just
                 finish its turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onHitWall-robocode.HitWallEvent-"><code>IBasicEvents.onHitWall(robocode.HitWallEvent)</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onHitRobot-robocode.HitRobotEvent-"><code>IBasicEvents.onHitRobot(robocode.HitRobotEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnBody-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnBody</h4>
<pre>void&nbsp;turnBody(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's body to the right or left by radians.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the body's turn is 0.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot's body is set to turn right, and
 negative values means that the robot's body is set to turn left.
 If 0 is given as input, the robot's body will stop turning.
 <p>
 Example:
 <pre>
   // Turn the robot's body 180 degrees to the right
   turnBody(Math.PI);

   // Afterwards, turn the robot's body 90 degrees to the left
   turnBody(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's body.
                If <code>radians</code> &gt; 0 the robot's body is set to turn right.
                If <code>radians</code> &lt; 0 the robot's body is set to turn left.
                If <code>radians</code> = 0 the robot's body is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-"><code>turnGun(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#turnRadar-double-"><code>turnRadar(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-"><code>move(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnGun-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGun</h4>
<pre>void&nbsp;turnGun(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's gun to the right or left by radians.
 This call executes immediately, and does not return until it is complete,
 i.e. when the angle remaining in the gun's turn is 0.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot's gun is set to turn right, and
 negative values means that the robot's gun is set to turn left.
 If 0 is given as input, the robot's gun will stop turning.
 <p>
 Example:
 <pre>
   // Turn the robot's gun 180 degrees to the right
   turnGun(Math.PI);

   // Afterwards, turn the robot's gun 90 degrees to the left
   turnGun(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's gun.
                If <code>radians</code> &gt; 0 the robot's gun is set to turn right.
                If <code>radians</code> &lt; 0 the robot's gun is set to turn left.
                If <code>radians</code> = 0 the robot's gun is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-"><code>turnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#turnRadar-double-"><code>turnRadar(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-"><code>move(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="fire-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fire</h4>
<pre><a href="../../../robocode/Bullet.html" title="class in robocode">Bullet</a>&nbsp;fire(double&nbsp;power)</pre>
<div class="block">Immediately fires a bullet. The bullet will travel in the direction the
 gun is pointing.
 <p>
 The specified bullet power is an amount of energy that will be taken from
 the robot's energy. Hence, the more power you want to spend on the
 bullet, the more energy is taken from your robot.
 <p>
 The bullet will do (4 * power) damage if it hits another robot. If power
 is greater than 1, it will do an additional 2 * (power - 1) damage.
 You will get (3 * power) back if you hit the other robot. You can call
 <a href="../../../robocode/Rules.html#getBulletDamage-double-"><code>Rules.getBulletDamage(double)</code></a> for getting the damage that a
 bullet with a specific bullet power will do.
 <p>
 The specified bullet power should be between
 <a href="../../../robocode/Rules.html#MIN_BULLET_POWER"><code>Rules.MIN_BULLET_POWER</code></a> and <a href="../../../robocode/Rules.html#MAX_BULLET_POWER"><code>Rules.MAX_BULLET_POWER</code></a>.
 <p>
 Note that the gun cannot fire if the gun is overheated, meaning that
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--"><code>getGunHeat()</code></a> returns a value &gt; 0.
 <p>
 A event is generated when the bullet hits a robot
 (<a href="../../../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>), wall (<a href="../../../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>), or another
 bullet (<a href="../../../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>).
 <p>
 Example:
 <pre>
   // Fire a bullet with maximum power if the gun is ready
   if (getGunHeat() == 0) {
       Bullet bullet = fire(Rules.MAX_BULLET_POWER);

       // Get the velocity of the bullet
       if (bullet != null) {
           double bulletVelocity = bullet.getVelocity();
       }
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - the amount of energy given to the bullet, and subtracted
              from the robot's energy.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../../../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a> that contains information about the bullet if it
         was actually fired, which can be used for tracking the bullet after it
         has been fired. If the bullet was not fired, <code>null</code> is returned.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-"><code>setFire(double)</code></a>, 
<a href="../../../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="setFire-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFire</h4>
<pre><a href="../../../robocode/Bullet.html" title="class in robocode">Bullet</a>&nbsp;setFire(double&nbsp;power)</pre>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.
 The bullet will travel in the direction the gun is pointing.
 <p>
 This call returns immediately, and will not execute until you call
 execute() or take an action that executes.
 <p>
 The specified bullet power is an amount of energy that will be taken from
 the robot's energy. Hence, the more power you want to spend on the
 bullet, the more energy is taken from your robot.
 <p>
 The bullet will do (4 * power) damage if it hits another robot. If power
 is greater than 1, it will do an additional 2 * (power - 1) damage.
 You will get (3 * power) back if you hit the other robot. You can call
 <a href="../../../robocode/Rules.html#getBulletDamage-double-"><code>Rules.getBulletDamage(double)</code></a> for getting the damage that a
 bullet with a specific bullet power will do.
 <p>
 The specified bullet power should be between
 <a href="../../../robocode/Rules.html#MIN_BULLET_POWER"><code>Rules.MIN_BULLET_POWER</code></a> and <a href="../../../robocode/Rules.html#MAX_BULLET_POWER"><code>Rules.MAX_BULLET_POWER</code></a>.
 <p>
 Note that the gun cannot fire if the gun is overheated, meaning that
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--"><code>getGunHeat()</code></a> returns a value &gt; 0.
 <p>
 A event is generated when the bullet hits a robot
 (<a href="../../../robocode/BulletHitEvent.html" title="class in robocode"><code>BulletHitEvent</code></a>), wall (<a href="../../../robocode/BulletMissedEvent.html" title="class in robocode"><code>BulletMissedEvent</code></a>), or another
 bullet (<a href="../../../robocode/BulletHitBulletEvent.html" title="class in robocode"><code>BulletHitBulletEvent</code></a>).
 <p>
 Example:
 <pre>
   Bullet bullet = null;

   // Fire a bullet with maximum power if the gun is ready
   if (getGunHeat() == 0) {
       bullet = setFireBullet(Rules.MAX_BULLET_POWER);
   }
   ...
   execute();
   ...
   // Get the velocity of the bullet
   if (bullet != null) {
       double bulletVelocity = bullet.getVelocity();
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - the amount of energy given to the bullet, and subtracted
              from the robot's energy.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a <a href="../../../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a> that contains information about the bullet if it
         was actually fired, which can be used for tracking the bullet after it
         has been fired. If the bullet was not fired, <code>null</code> is returned.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#fire-double-"><code>fire(double)</code></a>, 
<a href="../../../robocode/Bullet.html" title="class in robocode"><code>Bullet</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--"><code>getGunHeat()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--"><code>getGunCoolingRate()</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit(BulletHitEvent)</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet(BulletHitBulletEvent)</code></a>, 
<a href="../../../robocode/robotinterfaces/IBasicEvents.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed(BulletMissedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="setBodyColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBodyColor</h4>
<pre>void&nbsp;setBodyColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's body.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setBodyColor(Color.BLACK);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new body color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setGunColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGunColor</h4>
<pre>void&nbsp;setGunColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's gun.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setGunColor(Color.RED);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new gun color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setRadarColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRadarColor</h4>
<pre>void&nbsp;setRadarColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's radar.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setRadarColor(Color.YELLOW);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new radar color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setBulletColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBulletColor</h4>
<pre>void&nbsp;setBulletColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's bullets.
 <p>
 A <code>null</code> indicates the default white color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setBulletColor(Color.GREEN);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new bullet color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-"><code>setScanColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="setScanColor-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScanColor</h4>
<pre>void&nbsp;setScanColor(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt">Color</a>&nbsp;color)</pre>
<div class="block">Sets the color of the robot's scan arc.
 <p>
 A <code>null</code> indicates the default (blue) color.
 </p>
 <pre>
 Example:
   // Don't forget to import java.awt.Color at the top...
   import java.awt.Color;
   ...

   public void run() {
       setScanColor(Color.WHITE);
       ...
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the new scan arc color</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.1.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-"><code>setGunColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-"><code>setRadarColor(Color)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-"><code>setBulletColor(Color)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true" title="class or interface in java.awt"><code>Color</code></a></dd>
</dl>
</li>
</ul>
<a name="getCall--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCall</h4>
<pre>void&nbsp;getCall()</pre>
<div class="block">This call <em>must</em> be made from a robot call to inform the game
 that the robot made a <code>get*</code> call like e.g. <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--"><code>getX()</code></a> or
 <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getVelocity--"><code>getVelocity()</code></a>.
 <p>
 This method is used by the game to determine if the robot is inactive or
 not. Note: You should only make this call once in a <code>get*</code> method!</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setCall--"><code>setCall()</code></a></dd>
</dl>
</li>
</ul>
<a name="setCall--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCall</h4>
<pre>void&nbsp;setCall()</pre>
<div class="block">This call <em>must</em> be made from a robot call to inform the game
 that the robot made a <code>set*</code> call like e.g. <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-"><code>setFire(double)</code></a> or <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-"><code>setBodyColor(Color)</code></a>.
 <p>
 This method is used by the game to determine if the robot is inactive or
 not. Note: You should only make this call once in a <code>set*</code> method!</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getCall--"><code>getCall()</code></a></dd>
</dl>
</li>
</ul>
<a name="getGraphics--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGraphics</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Graphics2D.html?is-external=true" title="class or interface in java.awt">Graphics2D</a>&nbsp;getGraphics()</pre>
<div class="block">Returns a graphics context used for painting graphical items for the robot.
 <p>
 This method is very useful for debugging your robot.
 <p>
 Note that the robot will only be painted if the "Paint" is enabled on the
 robot's console window; otherwise the robot will never get painted (the
 reason being that all robots might have graphical items that must be
 painted, and then you might not be able to tell what graphical items that
 have been painted for your robot).
 <p>
 Also note that the coordinate system for the graphical context where you
 paint items fits for the Robocode coordinate system where (0, 0) is at
 the bottom left corner of the battlefield, where X is towards right and Y
 is upwards.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a graphics context used for painting graphical items for the robot.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.1</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IPaintEvents.html#onPaint-java.awt.Graphics2D-"><code>IPaintEvents.onPaint(Graphics2D)</code></a></dd>
</dl>
</li>
</ul>
<a name="setDebugProperty-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDebugProperty</h4>
<pre>void&nbsp;setDebugProperty(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the debug property with the specified key to the specified value.
 <p>
 This method is very useful when debugging or reviewing your robot as you
 will be able to see this property displayed in the robot console for your
 robots under the Debug Properties tab page.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - the name/key of the debug property.</dd>
<dd><code>value</code> - the new value of the debug property, where <code>null</code> or
              the empty string is used for removing this debug property.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
</dl>
</li>
</ul>
<a name="rescan--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>rescan</h4>
<pre>void&nbsp;rescan()</pre>
<div class="block">Rescan for other robots. This method is called automatically by the game,
 as long as the robot is moving, turning its body, turning its gun, or
 turning its radar.
 <p>
 Rescan will cause <a href="../../../robocode/robotinterfaces/IBasicEvents.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a> to be called if you see a robot.
 <p>
 There are 2 reasons to call <code>rescan()</code> manually:
 <ol>
 <li>You want to scan after you stop moving.
 <li>You want to interrupt the <code>onScannedRobot</code> event. This is more
 likely. If you are in <code>onScannedRobot</code> and call <code>scan()</code>,
 and you still see a robot, then the system will interrupt your
 <code>onScannedRobot</code> event immediately and start it from the top.
 </ol>
 <p>
 This call executes immediately.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/IBasicEvents.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot(ScannedRobotEvent)</code></a>, 
<a href="../../../robocode/ScannedRobotEvent.html" title="class in robocode"><code>ScannedRobotEvent</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IBasicRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IBasicRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
