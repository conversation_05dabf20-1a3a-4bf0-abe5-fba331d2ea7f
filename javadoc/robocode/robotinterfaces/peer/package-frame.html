<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode.robotinterfaces.peer (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../robocode/robotinterfaces/peer/package-summary.html" target="classFrame">robocode.robotinterfaces.peer</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IAdvancedRobotPeer</span></a></li>
<li><a href="IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IBasicRobotPeer</span></a></li>
<li><a href="IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IJuniorRobotPeer</span></a></li>
<li><a href="IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IStandardRobotPeer</span></a></li>
<li><a href="ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">ITeamRobotPeer</span></a></li>
</ul>
</div>
</body>
</html>
