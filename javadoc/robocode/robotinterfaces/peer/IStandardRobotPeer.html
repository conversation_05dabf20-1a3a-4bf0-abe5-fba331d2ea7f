<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IStandardRobotPeer (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IStandardRobotPeer (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IStandardRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IStandardRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces.peer</div>
<h2 title="Interface IStandardRobotPeer" class="title">Interface IStandardRobotPeer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dd>
</dl>
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a>, <a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IStandardRobotPeer</span>
extends <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></pre>
<div class="block">The standard robot peer for standard robot types like <a href="../../../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>,
 <a href="../../../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, and <a href="../../../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>.
 <p>
 A robot peer is the object that deals with game mechanics and rules, and
 makes sure your robot abides by them.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IBasicRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IAdvancedRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>ITeamRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IJuniorRobotPeer</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--">resume</a></span>()</code>
<div class="block">Immediately resumes the movement you stopped by <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a>, if
 any.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustGunForBodyTurn-boolean-">setAdjustGunForBodyTurn</a></span>(boolean&nbsp;adjust)</code>
<div class="block">Sets the gun to adjust for the bot's turn, so the gun behaves like it is
 turning independent of the bot's turn.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForBodyTurn-boolean-">setAdjustRadarForBodyTurn</a></span>(boolean&nbsp;adjust)</code>
<div class="block">Sets the radar to turn independent from the robot's turn.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a></span>(boolean&nbsp;adjust)</code>
<div class="block">Sets the radar to adjust for the gun's turn, so the radar behaves like it is
 turning independent of the gun's turn.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-">stop</a></span>(boolean&nbsp;overwrite)</code>
<div class="block">Immediately stops all movement, and saves it for a call to <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--"><code>resume()</code></a>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#turnRadar-double-">turnRadar</a></span>(double&nbsp;radians)</code>
<div class="block">Immediately turns the robot's radar to the right or left by radians.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.peer.IBasicRobotPeer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.peer.<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></h3>
<code><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--">execute</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#fire-double-">fire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--">getBodyHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--">getBodyTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getCall--">getCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--">getDistanceRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getEnergy--">getEnergy</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGraphics--">getGraphics</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--">getGunHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--">getGunHeat</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--">getGunTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getName--">getName</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--">getNumRounds</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumSentries--">getNumSentries</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getOthers--">getOthers</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--">getRadarHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--">getRadarTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRoundNum--">getRoundNum</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getTime--">getTime</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getVelocity--">getVelocity</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--">getX</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--">getY</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-">move</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#rescan--">rescan</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setCall--">setCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-">setFire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-">turnBody</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-">turnGun</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="stop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stop</h4>
<pre>void&nbsp;stop(boolean&nbsp;overwrite)</pre>
<div class="block">Immediately stops all movement, and saves it for a call to <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--"><code>resume()</code></a>.
 If there is already movement saved from a previous stop, you can overwrite it
 by calling <code>stop(true)</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>overwrite</code> - If there is already movement saved from a previous stop, you
                  can overwrite it by calling <code>stop(true)</code>.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--"><code>resume()</code></a></dd>
</dl>
</li>
</ul>
<a name="resume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resume</h4>
<pre>void&nbsp;resume()</pre>
<div class="block">Immediately resumes the movement you stopped by <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a>, if
 any.
 <p>
 This call executes immediately, and does not return until it is complete.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="turnRadar-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRadar</h4>
<pre>void&nbsp;turnRadar(double&nbsp;radians)</pre>
<div class="block">Immediately turns the robot's radar to the right or left by radians. This
 call executes immediately, and does not return until it is complete, i.e.
 when the angle remaining in the radar's turn is 0.
 <p>
 Note that both positive and negative values can be given as input, where
 positive values means that the robot's radar is set to turn right, and
 negative values means that the robot's radar is set to turn left. If 0 is
 given as input, the robot's radar will stop turning.
 <p>
 Example:
 
 <pre>
 // Turn the robot's radar 180 degrees to the right
 turnRadar(Math.PI);

 // Afterwards, turn the robot's radar 90 degrees to the left
 turnRadar(-Math.PI / 2);
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>radians</code> - the amount of radians to turn the robot's radar. If
                <code>radians</code> &gt; 0 the robot's radar is set to turn right. If
                <code>radians</code> &lt; 0 the robot's radar is set to turn left. If
                <code>radians</code> = 0 the robot's radar is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-"><code>IBasicRobotPeer.turnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-"><code>IBasicRobotPeer.turnGun(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-"><code>IBasicRobotPeer.move(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdjustGunForBodyTurn-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdjustGunForBodyTurn</h4>
<pre>void&nbsp;setAdjustGunForBodyTurn(boolean&nbsp;adjust)</pre>
<div class="block">Sets the gun to adjust for the bot's turn, so the gun behaves like it is
 turning independent of the bot's turn.
 <p>
 Ok, so this needs some explanation: The gun is mounted on the bot's body. So,
 normally, if the bot turns 90 degrees to the right, then the gun will turn
 with it as it is mounted on top of the bot's body. To compensate for this,
 you can call <code>setAdjustGunForBodyTurn(true)</code>. When this is set, the gun
 will turn independent from the bot's turn.
 <p>
 Note: This method is additive until you reach the maximum the gun can turn.
 The "adjust" is added to the amount you set for turning the bot, then capped
 by the physics of the game. If you turn infinite, then the adjust is ignored
 (and hence overridden).
 <p>
 Example, assuming both the robot and gun start out facing up (0 degrees):
 
 <pre>
   // Set gun to turn with the robot's turn
   setAdjustGunForBodyTurn(false); // This is the default
   turnBodyRight(Math.PI / 2);
   // At this point, both the robot and gun are facing right (90 degrees)
   turnBodyLeft(Math.PI / 2);
   // Both are back to 0 degrees

   -- or --

   // Set gun to turn independent from the robot's turn
   setAdjustGunForBodyTurn(true);
   turnBodyRight(Math.PI / 2);
   // At this point, the robot is facing right (90 degrees), but the gun is still facing up.
   turnBodyLeft(Math.PI / 2);
   // Both are back to 0 degrees.
 </pre>
 <p>
 Note: The gun compensating this way does count as "turning the gun".</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>adjust</code> - <code>true</code> if the gun must adjust for the bot's turn;
               <code>false</code> if the gun must turn with the bot's turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdjustRadarForGunTurn-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdjustRadarForGunTurn</h4>
<pre>void&nbsp;setAdjustRadarForGunTurn(boolean&nbsp;adjust)</pre>
<div class="block">Sets the radar to adjust for the gun's turn, so the radar behaves like it is
 turning independent of the gun's turn.
 <p>
 Ok, so this needs some explanation: The radar is mounted on the bot's gun.
 So, normally, if the gun turns 90 degrees to the right, then the radar will
 turn with it as it is mounted on top of the gun. To compensate for this, you
 can call <code>setAdjustRadarForGunTurn(true)</code>. When this is set, the radar
 will turn independent from the gun's turn.
 <p>
 Note: This method is additive until you reach the maximum the radar can turn.
 The "adjust" is added to the amount you set for turning the gun, then capped
 by the physics of the game. If you turn infinite, then the adjust is ignored
 (and hence overridden).
 <p>
 Example, assuming both the gun and radar start out facing up (0 degrees):
 
 <pre>
   // Set radar to turn with the gun's turn
   setAdjustRadarForGunTurn(false); // This is the default
   turnGunRight(Math.PI / 2);
   // At this point, both the radar and gun are facing right (90 degrees);

   -- or --

   // Set radar to turn independent from the gun's turn
   setAdjustRadarForGunTurn(true);
   turnGunRight(Math.PI / 2);
   // At this point, the gun is facing right (90 degrees), but the radar is still facing up.
 </pre>
 
 <p>
 Note: The radar compensating this way does count as "turning the radar".
 <p>
 Note: Calling <code>setAdjustRadarForGunTurn(boolean)</code> will automatically
 call <a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForBodyTurn-boolean-"><code>setAdjustRadarForBodyTurn(boolean)</code></a> with the same value, unless
 you have already called it earlier. This behavior is primarily for backward
 compatibility with older Robocode robots.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>adjust</code> - <code>true</code> if the radar must adjust for the gun's turn;
               <code>false</code> if the radar must turn with the gun's turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustGunForBodyTurn-boolean-"><code>setAdjustGunForBodyTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="setAdjustRadarForBodyTurn-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setAdjustRadarForBodyTurn</h4>
<pre>void&nbsp;setAdjustRadarForBodyTurn(boolean&nbsp;adjust)</pre>
<div class="block">Sets the radar to turn independent from the robot's turn.
 <p>
 Ok, so this needs some explanation: The radar is mounted on the gun, and the
 gun is mounted on the robot's body. So, normally, if the robot turns 90
 degrees to the right, the gun turns, as does the radar. Hence, if the robot
 turns 90 degrees to the right, then the gun and radar will turn with it as
 the radar is mounted on top of the gun. To compensate for this, you can call
 <code>setAdjustRadarForBodyTurn(true)</code>. When this is set, the radar will
 turn independent from the robot's turn, i.e. the radar will compensate for
 the robot's turn.
 <p>
 Note: This method is additive until you reach the maximum the radar can turn.
 The "adjust" is added to the amount you set for turning the gun, then capped
 by the physics of the game. If you turn infinite, then the adjust is ignored
 (and hence overridden).
 <p>
 Example, assuming the robot, gun, and radar all start out facing up (0
 degrees):
 
 <pre>
   // Set radar to turn with the robots's turn
   setAdjustRadarForBodyTurn(false); // This is the default
   turnRight(Math.PI / 2);
   // At this point, the body, gun, and radar are all facing right (90 degrees);

   -- or --

   // Set radar to turn independent from the robot's turn
   setAdjustRadarForBodyTurn(true);
   turnRight(Math.PI / 2);
   // At this point, the robot and gun are facing right (90 degrees), but the radar is still facing up.
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>adjust</code> - <code>true</code> if the radar must adjust for the robot's turn;
               <code>false</code> if the radar must turn with the robot's turn.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustGunForBodyTurn-boolean-"><code>setAdjustGunForBodyTurn(boolean)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForGunTurn-boolean-"><code>setAdjustRadarForGunTurn(boolean)</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IStandardRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IStandardRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
