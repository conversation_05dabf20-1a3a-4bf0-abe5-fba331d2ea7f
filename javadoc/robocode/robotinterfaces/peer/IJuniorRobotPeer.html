<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IJuniorRobotPeer (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IJuniorRobotPeer (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IJuniorRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IJuniorRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces.peer</div>
<h2 title="Interface IJuniorRobotPeer" class="title">Interface IJuniorRobotPeer</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IJuniorRobotPeer</span>
extends <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></pre>
<div class="block">The junior robot peer for junior robot types like <a href="../../../robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>.
 <p>
 A robot peer is the object that deals with game mechanics and rules, and
 makes sure your robot abides by them.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IBasicRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IStandardRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>IAdvancedRobotPeer</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><code>ITeamRobotPeer</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/robotinterfaces/peer/IJuniorRobotPeer.html#turnAndMove-double-double-">turnAndMove</a></span>(double&nbsp;distance,
           double&nbsp;radians)</code>
<div class="block">Moves this robot forward or backwards by pixels and turns this robot
 right or left by degrees at the same time.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.peer.IBasicRobotPeer">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.peer.<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></h3>
<code><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--">execute</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#fire-double-">fire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--">getBodyHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--">getBodyTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getCall--">getCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--">getDistanceRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getEnergy--">getEnergy</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGraphics--">getGraphics</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--">getGunHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--">getGunHeat</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--">getGunTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getName--">getName</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--">getNumRounds</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumSentries--">getNumSentries</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getOthers--">getOthers</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--">getRadarHeading</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--">getRadarTurnRemaining</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRoundNum--">getRoundNum</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getTime--">getTime</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getVelocity--">getVelocity</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--">getX</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--">getY</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-">move</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#rescan--">rescan</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setCall--">setCall</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-">setFire</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-">turnBody</a>, <a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-">turnGun</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="turnAndMove-double-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>turnAndMove</h4>
<pre>void&nbsp;turnAndMove(double&nbsp;distance,
                 double&nbsp;radians)</pre>
<div class="block">Moves this robot forward or backwards by pixels and turns this robot
 right or left by degrees at the same time. The robot will move in a curve
 that follows a perfect circle, and the moving and turning will end at
 exactly the same time.
 <p>
 Note that the max. velocity and max. turn rate is automatically adjusted,
 which means that the robot will move slower the sharper the turn is
 compared to the distance.
 <p>
 Note that both positive and negative values can be given as input:
 <ul>
 <li>If the <code>distance</code> parameter is set to a positive value, it
 means that the robot is set to move forward, and a negative value means
 that the robot is set to move backward. If set to 0, the robot will not
 move, but will be able to turn.
 <li>If the <code>radians</code> parameter is set to a positive value, it means
 that the robot is set to turn to the right, and a negative value means
 that the robot is set to turn to the left. If set to 0, the robot will
 not turn, but will be able to move.
 </ul></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>distance</code> - the distance to move measured in pixels.
                 If <code>distance</code> &gt; 0 the robot is set to move forward.
                 If <code>distance</code> &lt; 0 the robot is set to move backward.
                 If <code>distance</code> = 0 the robot will not move anywhere, but just
                 finish its turn.</dd>
<dd><code>radians</code> - the amount of radians to turn the robot's body.
                 If <code>radians</code> &gt; 0 the robot's body is set to turn right.
                 If <code>radians</code> &lt; 0 the robot's body is set to turn left.
                 If <code>radians</code> = 0 the robot's body is set to stop turning.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-"><code>move(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-"><code>turnBody(double)</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--"><code>getBodyHeading()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--"><code>getX()</code></a>, 
<a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--"><code>getY()</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/robotinterfaces/peer/IJuniorRobotPeer.html" target="_top">Frames</a></li>
<li><a href="IJuniorRobotPeer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
