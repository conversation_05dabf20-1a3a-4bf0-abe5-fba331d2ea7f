<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ITeamRobot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ITeamRobot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/ITeamRobot.html" target="_top">Frames</a></li>
<li><a href="ITeamRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces</div>
<h2 title="Interface ITeamRobot" class="title">Interface ITeamRobot</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a>, <a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a>, <a href="../../robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ITeamRobot</span>
extends <a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a></pre>
<div class="block">A robot interface for creating a team robot like <a href="../../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>
 that is able to receive team events.
 A team robot is an advanced type of robot that supports sending messages
 between teammates that participates in a team.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>, 
<a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><code>IBasicRobot</code></a>, 
<a href="../../robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces"><code>IJuniorRobot</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><code>IInteractiveRobot</code></a>, 
<a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><code>IAdvancedRobot</code></a>, 
<a href="../../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces"><code>ITeamRobot</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/ITeamRobot.html#getTeamEventListener--">getTeamEventListener</a></span>()</code>
<div class="block">This method is called by the game to notify this robot about team events.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IAdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a></h3>
<code><a href="../../robocode/robotinterfaces/IAdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IBasicRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></h3>
<code><a href="../../robocode/robotinterfaces/IBasicRobot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../../robocode/robotinterfaces/IBasicRobot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../../robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../../robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTeamEventListener--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTeamEventListener</h4>
<pre><a href="../../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a>&nbsp;getTeamEventListener()</pre>
<div class="block">This method is called by the game to notify this robot about team events.
 Hence, this method must be implemented so it returns your
 <a href="../../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces"><code>ITeamEvents</code></a> listener.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>listener to team events or <code>null</code> if this robot should
         not receive the notifications.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/ITeamRobot.html" target="_top">Frames</a></li>
<li><a href="ITeamRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
