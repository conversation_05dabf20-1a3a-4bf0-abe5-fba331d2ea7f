<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode.robotinterfaces (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="robocode.robotinterfaces (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/snapshot/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../robocode/robotinterfaces/peer/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;robocode.robotinterfaces</h1>
<div class="docSummary">
<div class="block">Robot Interfaces used for creating new robot types, e.g.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a></td>
<td class="colLast">
<div class="block">An event interface for receiving advanced robot events with an
 <a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><code>IAdvancedRobot</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a></td>
<td class="colLast">
<div class="block">A robot interface for creating a more advanced type of robot like
 <a href="../../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> that is able to handle advanced robot events.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></td>
<td class="colLast">
<div class="block">An event interface for receiving basic robot events with an
 <a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><code>IBasicRobot</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a></td>
<td class="colLast">
<div class="block">First extended version of the <a href="../../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><code>IBasicEvents</code></a> interface.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a></td>
<td class="colLast">
<div class="block">Second extended version of the <a href="../../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><code>IBasicEvents</code></a> interface.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></td>
<td class="colLast">
<div class="block">A robot interface for creating a basic type of robot like <a href="../../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>
 that is able to receive common robot events, but not interactive events as
 with the <a href="../../robocode/Robot.html" title="class in robocode"><code>Robot</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></td>
<td class="colLast">
<div class="block">An event interface for receiving interactive events with an
 <a href="../../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><code>IInteractiveRobot</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a></td>
<td class="colLast">
<div class="block">A robot interface for creating an interactive type of robot like
 <a href="../../robocode/Robot.html" title="class in robocode"><code>Robot</code></a> and <a href="../../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> that is able to
 receive interactive events from the keyboard or mouse.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces">IJuniorRobot</a></td>
<td class="colLast">
<div class="block">A robot interface for creating the most primitive robot type, which is a
 <a href="../../robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a></td>
<td class="colLast">
<div class="block">An event interface for receiving paint events with an
 <a href="../../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><code>IPaintRobot</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a></td>
<td class="colLast">
<div class="block">A robot interface that makes it possible for a robot to receive paint events.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a></td>
<td class="colLast">
<div class="block">An event interface for receiving robot team events with an
 <a href="../../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces"><code>ITeamRobot</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a></td>
<td class="colLast">
<div class="block">A robot interface for creating a team robot like <a href="../../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>
 that is able to receive team events.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package robocode.robotinterfaces Description">Package robocode.robotinterfaces Description</h2>
<div class="block">Robot Interfaces used for creating new robot types, e.g. with other programming languages.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/snapshot/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../robocode/robotinterfaces/peer/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
