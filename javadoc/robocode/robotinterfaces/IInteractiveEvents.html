<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IInteractiveEvents (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IInteractiveEvents (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/IInteractiveEvents.html" target="_top">Frames</a></li>
<li><a href="IInteractiveEvents.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.robotinterfaces</div>
<h2 title="Interface IInteractiveEvents" class="title">Interface IInteractiveEvents</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a>, <a href="../../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a>, <a href="../../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a>, <a href="../../robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a>, <a href="../../robocode/Robot.html" title="class in robocode">Robot</a>, <a href="../../robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IInteractiveEvents</span></pre>
<div class="block">An event interface for receiving interactive events with an
 <a href="../../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><code>IInteractiveRobot</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><code>IInteractiveRobot</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a key has been pressed.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a key has been released.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a key has been typed (pressed and released).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a mouse button has been clicked (pressed and
 released).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a mouse button has been pressed and then
 dragged.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the mouse has entered the battle view.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the mouse has exited the battle view.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the mouse has been moved.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a mouse button has been pressed.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a mouse button has been released.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseWheelEvent.html?is-external=true" title="class or interface in java.awt.event">MouseWheelEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the mouse wheel has been rotated.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="onKeyPressed-java.awt.event.KeyEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onKeyPressed</h4>
<pre>void&nbsp;onKeyPressed(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a key has been pressed.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 key events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyListener.html?is-external=true#keyPressed-java.awt.event.KeyEvent-" title="class or interface in java.awt.event"><code>KeyListener.keyPressed(KeyEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyReleased-java.awt.event.KeyEvent-"><code>onKeyReleased(KeyEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyTyped-java.awt.event.KeyEvent-"><code>onKeyTyped(KeyEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onKeyReleased-java.awt.event.KeyEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onKeyReleased</h4>
<pre>void&nbsp;onKeyReleased(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a key has been released.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 key events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyListener.html?is-external=true#keyReleased-java.awt.event.KeyEvent-" title="class or interface in java.awt.event"><code>KeyListener.keyReleased(KeyEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyPressed-java.awt.event.KeyEvent-"><code>onKeyPressed(KeyEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyTyped-java.awt.event.KeyEvent-"><code>onKeyTyped(KeyEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onKeyTyped-java.awt.event.KeyEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onKeyTyped</h4>
<pre>void&nbsp;onKeyTyped(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyEvent.html?is-external=true" title="class or interface in java.awt.event">KeyEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a key has been typed (pressed and released).
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 key events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/KeyListener.html?is-external=true#keyTyped-java.awt.event.KeyEvent-" title="class or interface in java.awt.event"><code>KeyListener.keyTyped(KeyEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyPressed-java.awt.event.KeyEvent-"><code>onKeyPressed(KeyEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onKeyReleased-java.awt.event.KeyEvent-"><code>onKeyReleased(KeyEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseClicked-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseClicked</h4>
<pre>void&nbsp;onMouseClicked(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a mouse button has been clicked (pressed and
 released).
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseClicked-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseEntered-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseEntered</h4>
<pre>void&nbsp;onMouseEntered(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the mouse has entered the battle view.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseEntered-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseExited-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseExited</h4>
<pre>void&nbsp;onMouseExited(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the mouse has exited the battle view.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseExited-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMousePressed-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMousePressed</h4>
<pre>void&nbsp;onMousePressed(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a mouse button has been pressed.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mousePressed-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseReleased-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseReleased</h4>
<pre>void&nbsp;onMouseReleased(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a mouse button has been released.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseListener.html?is-external=true#mouseReleased-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseListener.mouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseMoved-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseMoved</h4>
<pre>void&nbsp;onMouseMoved(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the mouse has been moved.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseMotionListener.html?is-external=true#mouseMoved-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseMotionListener.mouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseDragged-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMouseDragged</h4>
<pre>void&nbsp;onMouseDragged(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseEvent.html?is-external=true" title="class or interface in java.awt.event">MouseEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a mouse button has been pressed and then
 dragged.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.3.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseMotionListener.html?is-external=true#mouseDragged-java.awt.event.MouseEvent-" title="class or interface in java.awt.event"><code>MouseMotionListener.mouseDragged(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved(MouseWheelEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMouseWheelMoved-java.awt.event.MouseWheelEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onMouseWheelMoved</h4>
<pre>void&nbsp;onMouseWheelMoved(<a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseWheelEvent.html?is-external=true" title="class or interface in java.awt.event">MouseWheelEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the mouse wheel has been rotated.
 <p>
 See the <code>sample.Interactive</code> robot for an example of how to use
 mouse events.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - holds details about current event</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/event/MouseWheelListener.html?is-external=true#mouseWheelMoved-java.awt.event.MouseWheelEvent-" title="class or interface in java.awt.event"><code>MouseWheelListener.mouseWheelMoved(MouseWheelEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited(MouseEvent)</code></a>, 
<a href="../../robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged(MouseEvent)</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/robotinterfaces/IInteractiveEvents.html" target="_top">Frames</a></li>
<li><a href="IInteractiveEvents.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
