<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CustomEvent (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CustomEvent (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/Condition.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/DeathEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/CustomEvent.html" target="_top">Frames</a></li>
<li><a href="CustomEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class CustomEvent" class="title">Class CustomEvent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/Event.html" title="class in robocode">robocode.Event</a></li>
<li>
<ul class="inheritance">
<li>robocode.CustomEvent</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/Event.html" title="class in robocode">Event</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">CustomEvent</span>
extends <a href="../robocode/Event.html" title="class in robocode">Event</a></pre>
<div class="block">This event is sent to <a href="../robocode/AdvancedRobot.html#onCustomEvent-robocode.CustomEvent-"><code>onCustomEvent()</code></a> when a custom condition is met. Be sure to reset or remove
 the custom condition to avoid having it recurring repeatedly (see the
 example for the <a href="../robocode/CustomEvent.html#getCondition--"><code>getCondition()</code></a> method.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/CustomEvent.html#getCondition--"><code>getCondition()</code></a>, 
<a href="../serialized-form.html#robocode.CustomEvent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/CustomEvent.html#CustomEvent-robocode.Condition-">CustomEvent</a></span>(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</code>
<div class="block">Called by the game to create a new CustomEvent when a condition is met.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/CustomEvent.html#CustomEvent-robocode.Condition-int-">CustomEvent</a></span>(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition,
           int&nbsp;priority)</code>
<div class="block">Called by the game to create a new CustomEvent when a condition is met.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/CustomEvent.html#compareTo-robocode.Event-">compareTo</a></span>(<a href="../robocode/Event.html" title="class in robocode">Event</a>&nbsp;event)</code>
<div class="block">Compares this event to another event regarding precedence.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../robocode/Condition.html" title="class in robocode">Condition</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/CustomEvent.html#getCondition--">getCondition</a></span>()</code>
<div class="block">Returns the condition that fired, causing this event to be generated.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/CustomEvent.html#getPriority--">getPriority</a></span>()</code>
<div class="block">Returns the priority of this event.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.Event">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/Event.html" title="class in robocode">Event</a></h3>
<code><a href="../robocode/Event.html#getTime--">getTime</a>, <a href="../robocode/Event.html#setPriority-int-">setPriority</a>, <a href="../robocode/Event.html#setTime-long-">setTime</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CustomEvent-robocode.Condition-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CustomEvent</h4>
<pre>public&nbsp;CustomEvent(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition)</pre>
<div class="block">Called by the game to create a new CustomEvent when a condition is met.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that must be met</dd>
</dl>
</li>
</ul>
<a name="CustomEvent-robocode.Condition-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CustomEvent</h4>
<pre>public&nbsp;CustomEvent(<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;condition,
                   int&nbsp;priority)</pre>
<div class="block">Called by the game to create a new CustomEvent when a condition is met.
 The event will have the given priority.
 An event priority is a value from 0 - 99. The higher value, the higher
 priority. The default priority is 80.
 <p>
 This is equivalent to calling <a href="../robocode/Condition.html#setPriority-int-"><code>Condition.setPriority(int)</code></a> on the
 Condition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>condition</code> - the condition that must be met</dd>
<dd><code>priority</code> - the priority of the condition</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCondition--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCondition</h4>
<pre>public&nbsp;<a href="../robocode/Condition.html" title="class in robocode">Condition</a>&nbsp;getCondition()</pre>
<div class="block">Returns the condition that fired, causing this event to be generated.
 Use this to determine which condition fired, and to remove the custom
 event.
 <pre>
   public void onCustomEvent(CustomEvent event) {
       if (event.getCondition().getName().equals("mycondition")) {
           removeCustomEvent(event.getCondition());
           <i>// do something else</i>
       }
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the condition that fired, causing this event to be generated</dd>
</dl>
</li>
</ul>
<a name="compareTo-robocode.Event-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compareTo</h4>
<pre>public final&nbsp;int&nbsp;compareTo(<a href="../robocode/Event.html" title="class in robocode">Event</a>&nbsp;event)</pre>
<div class="block">Compares this event to another event regarding precedence.
 The event precedence is first and foremost determined by the event time,
 secondly the event priority, and lastly specific event information.
 <p>
 This method will first compare the time of each event. If the event time
 is the same for both events, then this method compared the priority of
 each event. If the event priorities are equals, then this method will
 compare the two event based on specific event information.
 <p>
 This method is called by the game in order to sort the event queue of a
 robot to make sure the events are listed in chronological order.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true#compareTo-T-" title="class or interface in java.lang">compareTo</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../robocode/Event.html" title="class in robocode">Event</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/Event.html#compareTo-robocode.Event-">compareTo</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/Event.html" title="class in robocode">Event</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event to compare to this event.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a negative value if this event has higher precedence, i.e. must
         be listed before the specified event. A positive value if this event
         has a lower precedence, i.e. must be listed after the specified event.
         0 means that the precedence of the two events are equal.</dd>
</dl>
</li>
</ul>
<a name="getPriority--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPriority</h4>
<pre>public final&nbsp;int&nbsp;getPriority()</pre>
<div class="block">Returns the priority of this event.
 <p>
 An event priority is a value from 0 - 99. The higher value, the higher priority.
 <p>
 The default priority is 80, but varies depending on the type of event.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../robocode/Event.html#getPriority--">getPriority</a></code>&nbsp;in class&nbsp;<code><a href="../robocode/Event.html" title="class in robocode">Event</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the priority of this event.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/Condition.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/DeathEvent.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/CustomEvent.html" target="_top">Frames</a></li>
<li><a href="CustomEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
