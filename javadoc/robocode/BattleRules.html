<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BattleRules (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BattleRules (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/BattleResults.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/BorderSentry.html" title="interface in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/BattleRules.html" target="_top">Frames</a></li>
<li><a href="BattleRules.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class BattleRules" class="title">Class BattleRules</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>robocode.BattleRules</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">BattleRules</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a></pre>
<div class="block">Contains the battle rules returned by <a href="../robocode/control/events/BattleStartedEvent.html#getBattleRules--"><code>BattleStartedEvent.getBattleRules()</code></a> when a battle is started and
 <a href="../robocode/control/events/BattleCompletedEvent.html#getBattleRules--"><code>BattleCompletedEvent.getBattleRules()</code></a>
 when a battle is completed.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events"><code>BattleStartedEvent</code></a>, 
<a href="../robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events"><code>BattleCompletedEvent</code></a>, 
<a href="../serialized-form.html#robocode.BattleRules">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleRules.html#getBattlefieldHeight--">getBattlefieldHeight</a></span>()</code>
<div class="block">Returns the battlefield height.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleRules.html#getBattlefieldWidth--">getBattlefieldWidth</a></span>()</code>
<div class="block">Returns the battlefield width.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleRules.html#getGunCoolingRate--">getGunCoolingRate</a></span>()</code>
<div class="block">Returns the rate at which the gun will cool down, i.e.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleRules.html#getHideEnemyNames--">getHideEnemyNames</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleRules.html#getInactivityTime--">getInactivityTime</a></span>()</code>
<div class="block">Returns the allowed inactivity time, where the robot is not taking any action, before will begin to be zapped.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleRules.html#getNumRounds--">getNumRounds</a></span>()</code>
<div class="block">Returns the number of rounds.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/BattleRules.html#getSentryBorderSize--">getSentryBorderSize</a></span>()</code>
<div class="block">Returns the sentry border size for a <a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBattlefieldWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattlefieldWidth</h4>
<pre>public&nbsp;int&nbsp;getBattlefieldWidth()</pre>
<div class="block">Returns the battlefield width.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the battlefield width.</dd>
</dl>
</li>
</ul>
<a name="getBattlefieldHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattlefieldHeight</h4>
<pre>public&nbsp;int&nbsp;getBattlefieldHeight()</pre>
<div class="block">Returns the battlefield height.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the battlefield height.</dd>
</dl>
</li>
</ul>
<a name="getNumRounds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumRounds</h4>
<pre>public&nbsp;int&nbsp;getNumRounds()</pre>
<div class="block">Returns the number of rounds.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds.</dd>
</dl>
</li>
</ul>
<a name="getGunCoolingRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunCoolingRate</h4>
<pre>public&nbsp;double&nbsp;getGunCoolingRate()</pre>
<div class="block">Returns the rate at which the gun will cool down, i.e. the amount of heat the gun heat will drop per turn.
 <p>
 The gun cooling rate is default 0.1 per turn, but can be changed by the battle setup.
 So don't count on the cooling rate being 0.1!</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the gun cooling rate.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#getGunHeat--"><code>Robot.getGunHeat()</code></a>, 
<a href="../robocode/Robot.html#fire-double-"><code>Robot.fire(double)</code></a>, 
<a href="../robocode/Robot.html#fireBullet-double-"><code>Robot.fireBullet(double)</code></a></dd>
</dl>
</li>
</ul>
<a name="getInactivityTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInactivityTime</h4>
<pre>public&nbsp;long&nbsp;getInactivityTime()</pre>
<div class="block">Returns the allowed inactivity time, where the robot is not taking any action, before will begin to be zapped.
 The inactivity time is measured in turns, and is the allowed time that a robot is allowed to omit taking
 action before being punished by the game by zapping.
 <p>
 When a robot is zapped by the game, it will loose 0.1 energy points per turn. Eventually the robot will be
 killed by zapping until the robot takes action. When the robot takes action, the inactivity time counter is
 reset. 
 <p>
 The allowed inactivity time is per default 450 turns, but can be changed by the battle setup.
 So don't count on the inactivity time being 450 turns!</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the allowed inactivity time.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html#doNothing--"><code>Robot.doNothing()</code></a>, 
<a href="../robocode/AdvancedRobot.html#execute--"><code>AdvancedRobot.execute()</code></a></dd>
</dl>
</li>
</ul>
<a name="getHideEnemyNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHideEnemyNames</h4>
<pre>public&nbsp;boolean&nbsp;getHideEnemyNames()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if the enemy names are hidden, i.e. anonymous; false otherwise.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7.3</dd>
</dl>
</li>
</ul>
<a name="getSentryBorderSize--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSentryBorderSize</h4>
<pre>public&nbsp;int&nbsp;getSentryBorderSize()</pre>
<div class="block">Returns the sentry border size for a <a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the border size in units/pixels.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>*******</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/BattleResults.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/BorderSentry.html" title="interface in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/BattleRules.html" target="_top">Frames</a></li>
<li><a href="BattleRules.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
