<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="robocode (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../robocode/annotation/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;robocode</h1>
<div class="docSummary">
<div class="block">Robot API used for writing robots for Robocode.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/BorderSentry.html" title="interface in robocode">BorderSentry</a></td>
<td class="colLast">
<div class="block">A robot that implements BorderSentry is a robot type used for keeping other robots away from the borders,
 i.e.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/Droid.html" title="interface in robocode">Droid</a></td>
<td class="colLast">
<div class="block">A Droid is an interface used on a <a href="../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a> to create a specialized team robot,
 i.e.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></td>
<td class="colLast">
<div class="block">This class is used by the system as a placeholder for all *Radians calls in
 <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></td>
<td class="colLast">
<div class="block">This class is used by the system, as well as being a placeholder for all deprecated
 (meaning, you should not use them) calls for <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/_Robot.html" title="class in robocode">_Robot</a></td>
<td class="colLast">
<div class="block">This class is used by the system, as well as being a placeholder for all deprecated
 (meaning, you should not use them) calls for <a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></td>
<td class="colLast">
<div class="block">This is the base class of all robots used by the system.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></td>
<td class="colLast">
<div class="block">A more advanced type of robot than Robot that allows non-blocking calls,
 custom events, and writes to the filesystem.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a></td>
<td class="colLast">
<div class="block">A BattleEndedEvent is sent to <a href="../robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-"><code>onBattleEnded()</code></a> when the battle is ended.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/BattleResults.html" title="class in robocode">BattleResults</a></td>
<td class="colLast">
<div class="block">Contains the battle results returned by <a href="../robocode/BattleEndedEvent.html#getResults--"><code>BattleEndedEvent.getResults()</code></a>
 when a battle has ended.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/BattleRules.html" title="class in robocode">BattleRules</a></td>
<td class="colLast">
<div class="block">Contains the battle rules returned by <a href="../robocode/control/events/BattleStartedEvent.html#getBattleRules--"><code>BattleStartedEvent.getBattleRules()</code></a> when a battle is started and
 <a href="../robocode/control/events/BattleCompletedEvent.html#getBattleRules--"><code>BattleCompletedEvent.getBattleRules()</code></a>
 when a battle is completed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/Bullet.html" title="class in robocode">Bullet</a></td>
<td class="colLast">
<div class="block">Represents a bullet.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet</code></a> when one of your bullets has hit another bullet.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit</code></a>
 when one of your bullets has hit another robot.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed</code></a> when one of your bullets has missed, i.e.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/Condition.html" title="class in robocode">Condition</a></td>
<td class="colLast">
<div class="block">Condition is used to define custom  <a href="../robocode/AdvancedRobot.html#waitFor-robocode.Condition-"><code>waitFor(Condition)</code></a> and custom events for an <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/CustomEvent.html" title="class in robocode">CustomEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/AdvancedRobot.html#onCustomEvent-robocode.CustomEvent-"><code>onCustomEvent()</code></a> when a custom condition is met.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/DeathEvent.html" title="class in robocode">DeathEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/Robot.html#onDeath-robocode.DeathEvent-"><code>onDeath()</code></a> when your
 robot dies.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/Event.html" title="class in robocode">Event</a></td>
<td class="colLast">
<div class="block">The superclass of all Robocode events.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/GunTurnCompleteCondition.html" title="class in robocode">GunTurnCompleteCondition</a></td>
<td class="colLast">
<div class="block">A prebuilt condition you can use that indicates your gun has finished
 turning.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></td>
<td class="colLast">
<div class="block">A HitByBulletEvent is sent to <a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-"><code>onHitByBullet()</code></a> when your robot has been hit by a bullet.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></td>
<td class="colLast">
<div class="block">A HitRobotEvent is sent to <a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-"><code>onHitRobot()</code></a>
 when your robot collides with another robot.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a></td>
<td class="colLast">
<div class="block">A HitWallEvent is sent to <a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-"><code>onHitWall()</code></a>
 when you collide a wall.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></td>
<td class="colLast">
<div class="block">This is the simplest robot type, which is simpler than the <a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a> and
 <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> classes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/KeyEvent.html" title="class in robocode">KeyEvent</a></td>
<td class="colLast">
<div class="block">Super class of all events that originates from the keyboard.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/KeyPressedEvent.html" title="class in robocode">KeyPressedEvent</a></td>
<td class="colLast">
<div class="block">A KeyPressedEvent is sent to <a href="../robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-"><code>onKeyPressed()</code></a> when a key has been pressed on the keyboard.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/KeyReleasedEvent.html" title="class in robocode">KeyReleasedEvent</a></td>
<td class="colLast">
<div class="block">A KeyReleasedEvent is sent to <a href="../robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-"><code>onKeyReleased()</code></a> when a key has been released on the keyboard.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/KeyTypedEvent.html" title="class in robocode">KeyTypedEvent</a></td>
<td class="colLast">
<div class="block">A KeyTypedEvent is sent to <a href="../robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-"><code>onKeyTyped()</code></a> when a key has been typed (pressed and released) on the keyboard.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/MessageEvent.html" title="class in robocode">MessageEvent</a></td>
<td class="colLast">
<div class="block">A MessageEvent is sent to <a href="../robocode/TeamRobot.html#onMessageReceived-robocode.MessageEvent-"><code>onMessageReceived()</code></a> when a teammate sends a message to your robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/MouseClickedEvent.html" title="class in robocode">MouseClickedEvent</a></td>
<td class="colLast">
<div class="block">A MouseClickedEvent is sent to <a href="../robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked()</code></a> when the mouse is clicked inside the battle view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/MouseDraggedEvent.html" title="class in robocode">MouseDraggedEvent</a></td>
<td class="colLast">
<div class="block">A MouseDraggedEvent is sent to <a href="../robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged()</code></a> when the mouse is dragged inside the battle view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/MouseEnteredEvent.html" title="class in robocode">MouseEnteredEvent</a></td>
<td class="colLast">
<div class="block">A MouseEnteredEvent is sent to <a href="../robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered()</code></a> when the mouse has entered the battle view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/MouseEvent.html" title="class in robocode">MouseEvent</a></td>
<td class="colLast">
<div class="block">Super class of all events that originates from the mouse.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/MouseExitedEvent.html" title="class in robocode">MouseExitedEvent</a></td>
<td class="colLast">
<div class="block">A MouseExitedEvent is sent to <a href="../robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited()</code></a> when the mouse has exited the battle view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/MouseMovedEvent.html" title="class in robocode">MouseMovedEvent</a></td>
<td class="colLast">
<div class="block">A MouseMovedEvent is sent to <a href="../robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved()</code></a> when the mouse has moved inside the battle view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/MousePressedEvent.html" title="class in robocode">MousePressedEvent</a></td>
<td class="colLast">
<div class="block">A MousePressedEvent is sent to <a href="../robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed()</code></a> when the mouse is pressed inside the battle view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/MouseReleasedEvent.html" title="class in robocode">MouseReleasedEvent</a></td>
<td class="colLast">
<div class="block">A MouseReleasedEvent is sent to <a href="../robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased()</code></a> when the mouse is released inside the battle view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/MouseWheelMovedEvent.html" title="class in robocode">MouseWheelMovedEvent</a></td>
<td class="colLast">
<div class="block">A MouseWheelMovedEvent is sent to <a href="../robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved()</code></a> when the mouse wheel is rotated inside the battle view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/MoveCompleteCondition.html" title="class in robocode">MoveCompleteCondition</a></td>
<td class="colLast">
<div class="block">A prebuilt condition you can use that indicates your robot has finished
 moving.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/PaintEvent.html" title="class in robocode">PaintEvent</a></td>
<td class="colLast">
<div class="block">This event occurs when your robot should paint, where the <a href="../robocode/Robot.html#onPaint-java.awt.Graphics2D-"><code>onPaint()</code></a> is called on your robot.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/RadarTurnCompleteCondition.html" title="class in robocode">RadarTurnCompleteCondition</a></td>
<td class="colLast">
<div class="block">A prebuilt condition you can use that indicates your radar has finished
 turning.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></td>
<td class="colLast">
<div class="block">This advanced robot type allows you to set a rate for each of the robot's movements.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/Robocode.html" title="class in robocode">Robocode</a></td>
<td class="colLast">
<div class="block">Robocode - A programming game involving battling AI tanks.<br>
 Copyright (c) 2001-2025 Mathew A.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></td>
<td class="colLast">
<div class="block">RobocodeFileOutputStream is similar to a <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileOutputStream.html?is-external=true" title="class or interface in java.io"><code>FileOutputStream</code></a>
 and is used for streaming/writing data out to a file, which you got
 previously by calling <a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>getDataFile()</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/RobocodeFileWriter.html" title="class in robocode">RobocodeFileWriter</a></td>
<td class="colLast">
<div class="block">RobocodeFileWriter is similar to a <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true" title="class or interface in java.io"><code>FileWriter</code></a> and is used for
 writing data out to a file, which you got by calling <a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>getDataFile()</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/Robot.html" title="class in robocode">Robot</a></td>
<td class="colLast">
<div class="block">The basic robot class that you will extend to create your own robots.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-"><code>onRobotDeath()</code></a>
 when another robot (not your robot) dies.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></td>
<td class="colLast">
<div class="block">Contains the status of a robot for a specific time/turn returned by
 <a href="../robocode/StatusEvent.html#getStatus--"><code>StatusEvent.getStatus()</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a></td>
<td class="colLast">
<div class="block">A RoundEndedEvent is sent to <a href="../robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-"><code>onRoundEnded()</code></a> when a round has ended.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/Rules.html" title="class in robocode">Rules</a></td>
<td class="colLast">
<div class="block">Constants and methods that defines the rules of Robocode.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></td>
<td class="colLast">
<div class="block">A ScannedRobotEvent is sent to <a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot()</code></a> when you scan a robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/SkippedTurnEvent.html" title="class in robocode">SkippedTurnEvent</a></td>
<td class="colLast">
<div class="block">A SkippedTurnEvent is sent to <a href="../robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-"><code>onSkippedTurn()</code></a> when your robot is forced to skipping a turn.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/StatusEvent.html" title="class in robocode">StatusEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/Robot.html#onStatus-robocode.StatusEvent-"><code>onStatus()</code></a> every
 turn in a battle to provide the status of the robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></td>
<td class="colLast">
<div class="block">A TeamRobot is a robot that is made for battles between teams of robots.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../robocode/TurnCompleteCondition.html" title="class in robocode">TurnCompleteCondition</a></td>
<td class="colLast">
<div class="block">A prebuilt condition you can use that indicates your robot has finished
 turning.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../robocode/WinEvent.html" title="class in robocode">WinEvent</a></td>
<td class="colLast">
<div class="block">This event is sent to <a href="../robocode/Robot.html#onWin-robocode.WinEvent-"><code>onWin()</code></a> when your robot
 wins the round in a battle.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package robocode Description">Package robocode Description</h2>
<div class="block">Robot API used for writing robots for Robocode.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Package</li>
<li><a href="../robocode/annotation/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
