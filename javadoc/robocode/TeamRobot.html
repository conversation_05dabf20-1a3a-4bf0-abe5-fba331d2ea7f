<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TeamRobot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TeamRobot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/StatusEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/TurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/TeamRobot.html" target="_top">Frames</a></li>
<li><a href="TeamRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class TeamRobot" class="title">Class TeamRobot</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_RobotBase.html" title="class in robocode">robocode._RobotBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_Robot.html" title="class in robocode">robocode._Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/Robot.html" title="class in robocode">robocode.Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_AdvancedRobot.html" title="class in robocode">robocode._AdvancedRobot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_AdvancedRadiansRobot.html" title="class in robocode">robocode._AdvancedRadiansRobot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/AdvancedRobot.html" title="class in robocode">robocode.AdvancedRobot</a></li>
<li>
<ul class="inheritance">
<li>robocode.TeamRobot</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>, <a href="../robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a>, <a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a>, <a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a>, <a href="../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a>, <a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, <a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, <a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, <a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>, <a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a>, <a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a>, <a href="../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TeamRobot</span>
extends <a href="../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a>
implements <a href="../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a>, <a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a></pre>
<div class="block">A TeamRobot is a robot that is made for battles between teams of robots.
 A robot team consists of one to several robots that are all a TeamRobot.
 A TeamRobot is an extension to the <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> and provides
 support for sending messages between team members.
 <p>
 Besides the robots, a robot team is put together by defining a .team file
 that is a Java property file. The filename must be the name of the team
 like e.g. <code>MyFirstTeam.team</code>. And the .team file and team robots must
 exist in the same folder (package name). But you can let the Robocode UI
 help you create the team from the menu: Robot -&gt; 'Create a robot team'.
 <p>
 The .team file contains a comma-separated list of the full name of the team
 members:
 <pre>
 team.members=sampleteam.MyFirstLeader,sampleteam.MyFirstDroid
 </pre>
 With this example, two different robots are members (MyFirstLeader and
 MyFirstDroid). But you can include any TeamRobot you want.
 <p>
 If you have not done already, you should start by creating a <a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a> or
 <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> before you make your first TeamRobot.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor), Pavel Savara (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>, 
<a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, 
<a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>, 
<a href="../robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>, 
<a href="../robocode/RateControlRobot.html" title="class in robocode"><code>RateControlRobot</code></a>, 
<a href="../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#out">out</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#TeamRobot--">TeamRobot</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#broadcastMessage-java.io.Serializable-">broadcastMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)</code>
<div class="block">Broadcasts a message to all teammates.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/MessageEvent.html" title="class in robocode">MessageEvent</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#getMessageEvents--">getMessageEvents</a></span>()</code>
<div class="block">Returns a vector containing all MessageEvents currently in the robot's
 queue.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#getTeamEventListener--">getTeamEventListener</a></span>()</code>
<div class="block">Do not call this method!</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#getTeammates--">getTeammates</a></span>()</code>
<div class="block">Returns the names of all teammates, or <code>null</code> there is no
 teammates.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#isTeammate-java.lang.String-">isTeammate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Checks if a given robot name is the name of one of your teammates.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#onMessageReceived-robocode.MessageEvent-">onMessageReceived</a></span>(<a href="../robocode/MessageEvent.html" title="class in robocode">MessageEvent</a>&nbsp;event)</code>
<div class="block">This method is called when your robot receives a message from a teammate.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/TeamRobot.html#sendMessage-java.lang.String-java.io.Serializable-">sendMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
           <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)</code>
<div class="block">Sends a message to one (or more) teammates.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.AdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></h3>
<code><a href="../robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-">addCustomEvent</a>, <a href="../robocode/AdvancedRobot.html#clearAllEvents--">clearAllEvents</a>, <a href="../robocode/AdvancedRobot.html#execute--">execute</a>, <a href="../robocode/AdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener</a>, <a href="../robocode/AdvancedRobot.html#getAllEvents--">getAllEvents</a>, <a href="../robocode/AdvancedRobot.html#getBulletHitBulletEvents--">getBulletHitBulletEvents</a>, <a href="../robocode/AdvancedRobot.html#getBulletHitEvents--">getBulletHitEvents</a>, <a href="../robocode/AdvancedRobot.html#getBulletMissedEvents--">getBulletMissedEvents</a>, <a href="../robocode/AdvancedRobot.html#getDataDirectory--">getDataDirectory</a>, <a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-">getDataFile</a>, <a href="../robocode/AdvancedRobot.html#getDataQuotaAvailable--">getDataQuotaAvailable</a>, <a href="../robocode/AdvancedRobot.html#getDistanceRemaining--">getDistanceRemaining</a>, <a href="../robocode/AdvancedRobot.html#getEventPriority-java.lang.String-">getEventPriority</a>, <a href="../robocode/AdvancedRobot.html#getGunHeadingRadians--">getGunHeadingRadians</a>, <a href="../robocode/AdvancedRobot.html#getGunTurnRemaining--">getGunTurnRemaining</a>, <a href="../robocode/AdvancedRobot.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians</a>, <a href="../robocode/AdvancedRobot.html#getHeadingRadians--">getHeadingRadians</a>, <a href="../robocode/AdvancedRobot.html#getHitByBulletEvents--">getHitByBulletEvents</a>, <a href="../robocode/AdvancedRobot.html#getHitRobotEvents--">getHitRobotEvents</a>, <a href="../robocode/AdvancedRobot.html#getHitWallEvents--">getHitWallEvents</a>, <a href="../robocode/AdvancedRobot.html#getRadarHeadingRadians--">getRadarHeadingRadians</a>, <a href="../robocode/AdvancedRobot.html#getRadarTurnRemaining--">getRadarTurnRemaining</a>, <a href="../robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians</a>, <a href="../robocode/AdvancedRobot.html#getRobotDeathEvents--">getRobotDeathEvents</a>, <a href="../robocode/AdvancedRobot.html#getScannedRobotEvents--">getScannedRobotEvents</a>, <a href="../robocode/AdvancedRobot.html#getStatusEvents--">getStatusEvents</a>, <a href="../robocode/AdvancedRobot.html#getTurnRemaining--">getTurnRemaining</a>, <a href="../robocode/AdvancedRobot.html#getTurnRemainingRadians--">getTurnRemainingRadians</a>, <a href="../robocode/AdvancedRobot.html#isAdjustGunForRobotTurn--">isAdjustGunForRobotTurn</a>, <a href="../robocode/AdvancedRobot.html#isAdjustRadarForGunTurn--">isAdjustRadarForGunTurn</a>, <a href="../robocode/AdvancedRobot.html#isAdjustRadarForRobotTurn--">isAdjustRadarForRobotTurn</a>, <a href="../robocode/AdvancedRobot.html#onCustomEvent-robocode.CustomEvent-">onCustomEvent</a>, <a href="../robocode/AdvancedRobot.html#onDeath-robocode.DeathEvent-">onDeath</a>, <a href="../robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-">onSkippedTurn</a>, <a href="../robocode/AdvancedRobot.html#removeCustomEvent-robocode.Condition-">removeCustomEvent</a>, <a href="../robocode/AdvancedRobot.html#setAhead-double-">setAhead</a>, <a href="../robocode/AdvancedRobot.html#setBack-double-">setBack</a>, <a href="../robocode/AdvancedRobot.html#setEventPriority-java.lang.String-int-">setEventPriority</a>, <a href="../robocode/AdvancedRobot.html#setFire-double-">setFire</a>, <a href="../robocode/AdvancedRobot.html#setFireBullet-double-">setFireBullet</a>, <a href="../robocode/AdvancedRobot.html#setInterruptible-boolean-">setInterruptible</a>, <a href="../robocode/AdvancedRobot.html#setMaxTurnRate-double-">setMaxTurnRate</a>, <a href="../robocode/AdvancedRobot.html#setMaxVelocity-double-">setMaxVelocity</a>, <a href="../robocode/AdvancedRobot.html#setResume--">setResume</a>, <a href="../robocode/AdvancedRobot.html#setStop--">setStop</a>, <a href="../robocode/AdvancedRobot.html#setStop-boolean-">setStop</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunLeft-double-">setTurnGunLeft</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-">setTurnGunLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunRight-double-">setTurnGunRight</a>, <a href="../robocode/AdvancedRobot.html#setTurnGunRightRadians-double-">setTurnGunRightRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnLeft-double-">setTurnLeft</a>, <a href="../robocode/AdvancedRobot.html#setTurnLeftRadians-double-">setTurnLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarLeft-double-">setTurnRadarLeft</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-">setTurnRadarLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarRight-double-">setTurnRadarRight</a>, <a href="../robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-">setTurnRadarRightRadians</a>, <a href="../robocode/AdvancedRobot.html#setTurnRight-double-">setTurnRight</a>, <a href="../robocode/AdvancedRobot.html#setTurnRightRadians-double-">setTurnRightRadians</a>, <a href="../robocode/AdvancedRobot.html#turnGunLeftRadians-double-">turnGunLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#turnGunRightRadians-double-">turnGunRightRadians</a>, <a href="../robocode/AdvancedRobot.html#turnLeftRadians-double-">turnLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#turnRadarLeftRadians-double-">turnRadarLeftRadians</a>, <a href="../robocode/AdvancedRobot.html#turnRadarRightRadians-double-">turnRadarRightRadians</a>, <a href="../robocode/AdvancedRobot.html#turnRightRadians-double-">turnRightRadians</a>, <a href="../robocode/AdvancedRobot.html#waitFor-robocode.Condition-">waitFor</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._AdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></h3>
<code><a href="../robocode/_AdvancedRobot.html#endTurn--">endTurn</a>, <a href="../robocode/_AdvancedRobot.html#getGunHeadingDegrees--">getGunHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getHeadingDegrees--">getHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getMaxWaitCount--">getMaxWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#getRadarHeadingDegrees--">getRadarHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getWaitCount--">getWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunLeftDegrees-double-">setTurnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunRightDegrees-double-">setTurnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnLeftDegrees-double-">setTurnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarLeftDegrees-double-">setTurnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarRightDegrees-double-">setTurnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRightDegrees-double-">setTurnRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunLeftDegrees-double-">turnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunRightDegrees-double-">turnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnLeftDegrees-double-">turnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarLeftDegrees-double-">turnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarRightDegrees-double-">turnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRightDegrees-double-">turnRightDegrees</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/Robot.html" title="class in robocode">Robot</a></h3>
<code><a href="../robocode/Robot.html#ahead-double-">ahead</a>, <a href="../robocode/Robot.html#back-double-">back</a>, <a href="../robocode/Robot.html#doNothing--">doNothing</a>, <a href="../robocode/Robot.html#fire-double-">fire</a>, <a href="../robocode/Robot.html#fireBullet-double-">fireBullet</a>, <a href="../robocode/Robot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../robocode/Robot.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../robocode/Robot.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../robocode/Robot.html#getEnergy--">getEnergy</a>, <a href="../robocode/Robot.html#getGraphics--">getGraphics</a>, <a href="../robocode/Robot.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../robocode/Robot.html#getGunHeading--">getGunHeading</a>, <a href="../robocode/Robot.html#getGunHeat--">getGunHeat</a>, <a href="../robocode/Robot.html#getHeading--">getHeading</a>, <a href="../robocode/Robot.html#getHeight--">getHeight</a>, <a href="../robocode/Robot.html#getInteractiveEventListener--">getInteractiveEventListener</a>, <a href="../robocode/Robot.html#getName--">getName</a>, <a href="../robocode/Robot.html#getNumRounds--">getNumRounds</a>, <a href="../robocode/Robot.html#getNumSentries--">getNumSentries</a>, <a href="../robocode/Robot.html#getOthers--">getOthers</a>, <a href="../robocode/Robot.html#getPaintEventListener--">getPaintEventListener</a>, <a href="../robocode/Robot.html#getRadarHeading--">getRadarHeading</a>, <a href="../robocode/Robot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../robocode/Robot.html#getRoundNum--">getRoundNum</a>, <a href="../robocode/Robot.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../robocode/Robot.html#getTime--">getTime</a>, <a href="../robocode/Robot.html#getVelocity--">getVelocity</a>, <a href="../robocode/Robot.html#getWidth--">getWidth</a>, <a href="../robocode/Robot.html#getX--">getX</a>, <a href="../robocode/Robot.html#getY--">getY</a>, <a href="../robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded</a>, <a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit</a>, <a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet</a>, <a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed</a>, <a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet</a>, <a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot</a>, <a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-">onHitWall</a>, <a href="../robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed</a>, <a href="../robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased</a>, <a href="../robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped</a>, <a href="../robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked</a>, <a href="../robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged</a>, <a href="../robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered</a>, <a href="../robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited</a>, <a href="../robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved</a>, <a href="../robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed</a>, <a href="../robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased</a>, <a href="../robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved</a>, <a href="../robocode/Robot.html#onPaint-java.awt.Graphics2D-">onPaint</a>, <a href="../robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath</a>, <a href="../robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded</a>, <a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot</a>, <a href="../robocode/Robot.html#onStatus-robocode.StatusEvent-">onStatus</a>, <a href="../robocode/Robot.html#onWin-robocode.WinEvent-">onWin</a>, <a href="../robocode/Robot.html#resume--">resume</a>, <a href="../robocode/Robot.html#run--">run</a>, <a href="../robocode/Robot.html#scan--">scan</a>, <a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-">setAdjustGunForRobotTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-">setAdjustRadarForRobotTurn</a>, <a href="../robocode/Robot.html#setAllColors-java.awt.Color-">setAllColors</a>, <a href="../robocode/Robot.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../robocode/Robot.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../robocode/Robot.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../robocode/Robot.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../robocode/Robot.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../robocode/Robot.html#stop--">stop</a>, <a href="../robocode/Robot.html#stop-boolean-">stop</a>, <a href="../robocode/Robot.html#turnGunLeft-double-">turnGunLeft</a>, <a href="../robocode/Robot.html#turnGunRight-double-">turnGunRight</a>, <a href="../robocode/Robot.html#turnLeft-double-">turnLeft</a>, <a href="../robocode/Robot.html#turnRadarLeft-double-">turnRadarLeft</a>, <a href="../robocode/Robot.html#turnRadarRight-double-">turnRadarRight</a>, <a href="../robocode/Robot.html#turnRight-double-">turnRight</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_Robot.html" title="class in robocode">_Robot</a></h3>
<code><a href="../robocode/_Robot.html#getBattleNum--">getBattleNum</a>, <a href="../robocode/_Robot.html#getGunCharge--">getGunCharge</a>, <a href="../robocode/_Robot.html#getGunImageName--">getGunImageName</a>, <a href="../robocode/_Robot.html#getLife--">getLife</a>, <a href="../robocode/_Robot.html#getNumBattles--">getNumBattles</a>, <a href="../robocode/_Robot.html#getRadarImageName--">getRadarImageName</a>, <a href="../robocode/_Robot.html#getRobotImageName--">getRobotImageName</a>, <a href="../robocode/_Robot.html#setGunImageName-java.lang.String-">setGunImageName</a>, <a href="../robocode/_Robot.html#setRadarImageName-java.lang.String-">setRadarImageName</a>, <a href="../robocode/_Robot.html#setRobotImageName-java.lang.String-">setRobotImageName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#finalize--">finalize</a>, <a href="../robocode/_RobotBase.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/_RobotBase.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a>, <a href="../robocode/_RobotBase.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IAdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IAdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IBasicRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IBasicRobot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TeamRobot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TeamRobot</h4>
<pre>public&nbsp;TeamRobot()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="broadcastMessage-java.io.Serializable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>broadcastMessage</h4>
<pre>public&nbsp;void&nbsp;broadcastMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)
                      throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Broadcasts a message to all teammates.
 <p>
 Example:
 <pre>
   public void run() {
       broadcastMessage("I'm here!");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the message to broadcast to all teammates</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - if the message could not be broadcasted to the
                     teammates</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/TeamRobot.html#isTeammate-java.lang.String-"><code>isTeammate(String)</code></a>, 
<a href="../robocode/TeamRobot.html#getTeammates--"><code>getTeammates()</code></a>, 
<a href="../robocode/TeamRobot.html#sendMessage-java.lang.String-java.io.Serializable-"><code>sendMessage(String, Serializable)</code></a></dd>
</dl>
</li>
</ul>
<a name="getMessageEvents--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageEvents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Vector.html?is-external=true" title="class or interface in java.util">Vector</a>&lt;<a href="../robocode/MessageEvent.html" title="class in robocode">MessageEvent</a>&gt;&nbsp;getMessageEvents()</pre>
<div class="block">Returns a vector containing all MessageEvents currently in the robot's
 queue. You might, for example, call this while processing another event.
 <p>
 Example:
 <pre>
   for (MessageEvent e : getMessageEvents()) {
      // do something with e
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a vector containing all MessageEvents currently in the robot's
         queue</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.2.6</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/TeamRobot.html#onMessageReceived-robocode.MessageEvent-"><code>onMessageReceived(MessageEvent)</code></a>, 
<a href="../robocode/MessageEvent.html" title="class in robocode"><code>MessageEvent</code></a></dd>
</dl>
</li>
</ul>
<a name="getTeamEventListener--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeamEventListener</h4>
<pre>public final&nbsp;<a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a>&nbsp;getTeamEventListener()</pre>
<div class="block">Do not call this method!
 <p>
 This method is called by the game to notify this robot about team events.
 Hence, this method must be implemented so it returns your
 <a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces"><code>ITeamEvents</code></a> listener.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/ITeamRobot.html#getTeamEventListener--">getTeamEventListener</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>listener to team events or <code>null</code> if this robot should
         not receive the notifications.</dd>
</dl>
</li>
</ul>
<a name="getTeammates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeammates</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;getTeammates()</pre>
<div class="block">Returns the names of all teammates, or <code>null</code> there is no
 teammates.
 <p>
 Example:
 <pre>
   public void run() {
       // Prints out all teammates
       String[] teammates = getTeammates();
       if (teammates != null) {
           for (String member : teammates) {
               out.println(member);
           }
       }
   }
 </pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a String array containing the names of all your teammates, or
         <code>null</code> if there is no teammates. The length of the String array
         is equal to the number of teammates.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/TeamRobot.html#isTeammate-java.lang.String-"><code>isTeammate(String)</code></a>, 
<a href="../robocode/TeamRobot.html#broadcastMessage-java.io.Serializable-"><code>broadcastMessage(Serializable)</code></a>, 
<a href="../robocode/TeamRobot.html#sendMessage-java.lang.String-java.io.Serializable-"><code>sendMessage(String, Serializable)</code></a></dd>
</dl>
</li>
</ul>
<a name="isTeammate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTeammate</h4>
<pre>public&nbsp;boolean&nbsp;isTeammate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Checks if a given robot name is the name of one of your teammates.
 <p>
 Example:
 <pre>
   public void onScannedRobot(ScannedRobotEvent e) {
       if (isTeammate(e.getName()) {
           return;
       }
       fire(1);
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the robot name to check</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the specified name belongs to one of your
         teammates; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/TeamRobot.html#getTeammates--"><code>getTeammates()</code></a>, 
<a href="../robocode/TeamRobot.html#broadcastMessage-java.io.Serializable-"><code>broadcastMessage(Serializable)</code></a>, 
<a href="../robocode/TeamRobot.html#sendMessage-java.lang.String-java.io.Serializable-"><code>sendMessage(String, Serializable)</code></a></dd>
</dl>
</li>
</ul>
<a name="onMessageReceived-robocode.MessageEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onMessageReceived</h4>
<pre>public&nbsp;void&nbsp;onMessageReceived(<a href="../robocode/MessageEvent.html" title="class in robocode">MessageEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when your robot receives a message from a teammate.
 You should override it in your robot if you want to be informed of this
 event.
 <p>
 Example:
 <pre>
   public void onMessageReceived(MessageEvent event) {
       out.println(event.getSender() + " sent me: " + event.getMessage());
   }
 </pre></div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../robocode/robotinterfaces/ITeamEvents.html#onMessageReceived-robocode.MessageEvent-">onMessageReceived</a></code>&nbsp;in interface&nbsp;<code><a href="../robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the message event sent by the game</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/MessageEvent.html" title="class in robocode"><code>MessageEvent</code></a>, 
<a href="../robocode/Event.html" title="class in robocode"><code>Event</code></a></dd>
</dl>
</li>
</ul>
<a name="sendMessage-java.lang.String-java.io.Serializable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>sendMessage</h4>
<pre>public&nbsp;void&nbsp;sendMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                        <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>&nbsp;message)
                 throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Sends a message to one (or more) teammates.
 <p>
 Example:
 <pre>
   public void run() {
       sendMessage("sample.DroidBot", "I'm here!");
   }
 </pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the intended recipient of the message</dd>
<dd><code>message</code> - the message to send</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - if the message could not be sent</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/TeamRobot.html#isTeammate-java.lang.String-"><code>isTeammate(String)</code></a>, 
<a href="../robocode/TeamRobot.html#getTeammates--"><code>getTeammates()</code></a>, 
<a href="../robocode/TeamRobot.html#broadcastMessage-java.io.Serializable-"><code>broadcastMessage(Serializable)</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/StatusEvent.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/TurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/TeamRobot.html" target="_top">Frames</a></li>
<li><a href="TeamRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
