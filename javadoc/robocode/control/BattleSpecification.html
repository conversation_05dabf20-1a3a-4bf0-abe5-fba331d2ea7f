<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BattleSpecification (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BattleSpecification (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/IRobocodeEngine.html" title="interface in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/BattleSpecification.html" target="_top">Frames</a></li>
<li><a href="BattleSpecification.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control</div>
<h2 title="Class BattleSpecification" class="title">Class BattleSpecification</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>robocode.control.BattleSpecification</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BattleSpecification</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a></pre>
<div class="block">A BattleSpecification defines a battle configuration used by the <a href="../../robocode/control/RobocodeEngine.html" title="class in robocode.control"><code>RobocodeEngine</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../serialized-form.html#robocode.control.BattleSpecification">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#BattleSpecification-robocode.control.BattlefieldSpecification-int-long-double-int-boolean-robocode.control.RobotSpecification:A-">BattleSpecification</a></span>(<a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                   int&nbsp;numRounds,
                   long&nbsp;inactivityTime,
                   double&nbsp;gunCoolingRate,
                   int&nbsp;sentryBorderSize,
                   boolean&nbsp;hideEnemyNames,
                   <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</code>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#BattleSpecification-robocode.control.BattlefieldSpecification-int-long-double-int-boolean-robocode.control.RobotSpecification:A-robocode.control.RobotSetup:A-">BattleSpecification</a></span>(<a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                   int&nbsp;numRounds,
                   long&nbsp;inactivityTime,
                   double&nbsp;gunCoolingRate,
                   int&nbsp;sentryBorderSize,
                   boolean&nbsp;hideEnemyNames,
                   <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots,
                   <a href="../../robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a>[]&nbsp;initialSetups)</code>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#BattleSpecification-int-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">BattleSpecification</a></span>(int&nbsp;numRounds,
                   <a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                   <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</code>
<div class="block">Creates a new BattleSpecification with the given number of rounds, battlefield size, and robots.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#BattleSpecification-int-long-double-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">BattleSpecification</a></span>(int&nbsp;numRounds,
                   long&nbsp;inactivityTime,
                   double&nbsp;gunCoolingRate,
                   <a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                   <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</code>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#BattleSpecification-int-long-double-boolean-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">BattleSpecification</a></span>(int&nbsp;numRounds,
                   long&nbsp;inactivityTime,
                   double&nbsp;gunCoolingRate,
                   boolean&nbsp;hideEnemyNames,
                   <a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                   <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</code>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getBattlefield--">getBattlefield</a></span>()</code>
<div class="block">Returns the battlefield size for this battle.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getGunCoolingRate--">getGunCoolingRate</a></span>()</code>
<div class="block">Returns the gun cooling rate of the robots in this battle.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getHideEnemyNames--">getHideEnemyNames</a></span>()</code>
<div class="block">Returns the flag specifying if the enemy names must be hidden from events sent to robots.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getInactivityTime--">getInactivityTime</a></span>()</code>
<div class="block">Returns the allowed inactivity time for the robots in this battle.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getInitialSetups--">getInitialSetups</a></span>()</code>
<div class="block">Returns the initial position and heading of each robot participating in this battle.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getNumRounds--">getNumRounds</a></span>()</code>
<div class="block">Returns the number of rounds in this battle.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getRobots--">getRobots</a></span>()</code>
<div class="block">Returns the specifications of the robots participating in this battle.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/BattleSpecification.html#getSentryBorderSize--">getSentryBorderSize</a></span>()</code>
<div class="block">Returns the sentry border size for a <a href="../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 border sentry robots cannot leave (they must stay in the border area), but it also define the
 distance from the border edges where border sentry robots are allowed/able to make damage to robots entering this
 border area.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BattleSpecification-int-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BattleSpecification</h4>
<pre>public&nbsp;BattleSpecification(int&nbsp;numRounds,
                           <a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                           <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</pre>
<div class="block">Creates a new BattleSpecification with the given number of rounds, battlefield size, and robots.
 Inactivity time for the robots defaults to 450, and the gun cooling rate defaults to 0.1.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>numRounds</code> - is the number of rounds in this battle.</dd>
<dd><code>battlefieldSize</code> - is the battlefield size.</dd>
<dd><code>robots</code> - is the robots participating in this battle.</dd>
</dl>
</li>
</ul>
<a name="BattleSpecification-int-long-double-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BattleSpecification</h4>
<pre>public&nbsp;BattleSpecification(int&nbsp;numRounds,
                           long&nbsp;inactivityTime,
                           double&nbsp;gunCoolingRate,
                           <a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                           <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</pre>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>numRounds</code> - is the number of rounds in this battle.</dd>
<dd><code>inactivityTime</code> - is the inactivity time allowed for the robots before they will loose energy.</dd>
<dd><code>gunCoolingRate</code> - is the gun cooling rate for the robots.</dd>
<dd><code>battlefieldSize</code> - is the battlefield size.</dd>
<dd><code>robots</code> - is the robots participating in this battle.</dd>
</dl>
</li>
</ul>
<a name="BattleSpecification-int-long-double-boolean-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BattleSpecification</h4>
<pre>public&nbsp;BattleSpecification(int&nbsp;numRounds,
                           long&nbsp;inactivityTime,
                           double&nbsp;gunCoolingRate,
                           boolean&nbsp;hideEnemyNames,
                           <a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                           <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</pre>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>numRounds</code> - is the number of rounds in this battle.</dd>
<dd><code>inactivityTime</code> - is the inactivity time allowed for the robots before they will loose energy.</dd>
<dd><code>gunCoolingRate</code> - is the gun cooling rate for the robots.</dd>
<dd><code>hideEnemyNames</code> - flag specifying if enemy names are hidden from robots.</dd>
<dd><code>battlefieldSize</code> - is the battlefield size.</dd>
<dd><code>robots</code> - is the robots participating in this battle.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7.3</dd>
</dl>
</li>
</ul>
<a name="BattleSpecification-robocode.control.BattlefieldSpecification-int-long-double-int-boolean-robocode.control.RobotSpecification:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BattleSpecification</h4>
<pre>public&nbsp;BattleSpecification(<a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                           int&nbsp;numRounds,
                           long&nbsp;inactivityTime,
                           double&nbsp;gunCoolingRate,
                           int&nbsp;sentryBorderSize,
                           boolean&nbsp;hideEnemyNames,
                           <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots)</pre>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battlefieldSize</code> - is the battlefield size.</dd>
<dd><code>numRounds</code> - is the number of rounds in this battle.</dd>
<dd><code>inactivityTime</code> - is the inactivity time allowed for the robots before they will loose energy.</dd>
<dd><code>gunCoolingRate</code> - is the gun cooling rate for the robots.</dd>
<dd><code>sentryBorderSize</code> - is the sentry border size for a <a href="../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a>.</dd>
<dd><code>hideEnemyNames</code> - flag specifying if enemy names are hidden from robots.</dd>
<dd><code>robots</code> - is the robots participating in this battle.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>*******</dd>
</dl>
</li>
</ul>
<a name="BattleSpecification-robocode.control.BattlefieldSpecification-int-long-double-int-boolean-robocode.control.RobotSpecification:A-robocode.control.RobotSetup:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BattleSpecification</h4>
<pre>public&nbsp;BattleSpecification(<a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;battlefieldSize,
                           int&nbsp;numRounds,
                           long&nbsp;inactivityTime,
                           double&nbsp;gunCoolingRate,
                           int&nbsp;sentryBorderSize,
                           boolean&nbsp;hideEnemyNames,
                           <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;robots,
                           <a href="../../robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a>[]&nbsp;initialSetups)</pre>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battlefieldSize</code> - is the battlefield size.</dd>
<dd><code>numRounds</code> - is the number of rounds in this battle.</dd>
<dd><code>inactivityTime</code> - is the inactivity time allowed for the robots before they will loose energy.</dd>
<dd><code>gunCoolingRate</code> - is the gun cooling rate for the robots.</dd>
<dd><code>sentryBorderSize</code> - is the sentry border size for a <a href="../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a>.</dd>
<dd><code>hideEnemyNames</code> - flag specifying if enemy names are hidden from robots.</dd>
<dd><code>robots</code> - is the robots participating in this battle.</dd>
<dd><code>initialSetups</code> - is the initial position and heading of the robots, where the indices matches the indices from the <code>robots</code> parameter.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>*******</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInactivityTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInactivityTime</h4>
<pre>public&nbsp;long&nbsp;getInactivityTime()</pre>
<div class="block">Returns the allowed inactivity time for the robots in this battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the allowed inactivity time for the robots in this battle.</dd>
</dl>
</li>
</ul>
<a name="getGunCoolingRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunCoolingRate</h4>
<pre>public&nbsp;double&nbsp;getGunCoolingRate()</pre>
<div class="block">Returns the gun cooling rate of the robots in this battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the gun cooling rate of the robots in this battle.</dd>
</dl>
</li>
</ul>
<a name="getBattlefield--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattlefield</h4>
<pre>public&nbsp;<a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a>&nbsp;getBattlefield()</pre>
<div class="block">Returns the battlefield size for this battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the battlefield size for this battle.</dd>
</dl>
</li>
</ul>
<a name="getNumRounds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumRounds</h4>
<pre>public&nbsp;int&nbsp;getNumRounds()</pre>
<div class="block">Returns the number of rounds in this battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of rounds in this battle.</dd>
</dl>
</li>
</ul>
<a name="getHideEnemyNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHideEnemyNames</h4>
<pre>public&nbsp;boolean&nbsp;getHideEnemyNames()</pre>
<div class="block">Returns the flag specifying if the enemy names must be hidden from events sent to robots.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the enemy names must be hidden; <code>false</code> otherwise.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7.3</dd>
</dl>
</li>
</ul>
<a name="getSentryBorderSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSentryBorderSize</h4>
<pre>public&nbsp;int&nbsp;getSentryBorderSize()</pre>
<div class="block">Returns the sentry border size for a <a href="../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 border sentry robots cannot leave (they must stay in the border area), but it also define the
 distance from the border edges where border sentry robots are allowed/able to make damage to robots entering this
 border area.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the border size in units/pixels that border sentry robots are restricted to.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>*******</dd>
</dl>
</li>
</ul>
<a name="getRobots--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobots</h4>
<pre>public&nbsp;<a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;getRobots()</pre>
<div class="block">Returns the specifications of the robots participating in this battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control"><code>RobotSpecification</code></a> instances - one entry for each robot.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/BattleSpecification.html#getInitialSetups--"><code>getInitialSetups()</code></a></dd>
</dl>
</li>
</ul>
<a name="getInitialSetups--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getInitialSetups</h4>
<pre>public&nbsp;<a href="../../robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a>[]&nbsp;getInitialSetups()</pre>
<div class="block">Returns the initial position and heading of each robot participating in this battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of <a href="../../robocode/control/RobotSetup.html" title="class in robocode.control"><code>RobotSetup</code></a> instances - one entry for each robot.
 The the indices of this array matches the array indices from the robot specifications (see <a href="../../robocode/control/BattleSpecification.html#getRobots--"><code>getRobots()</code></a>).</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/BattleSpecification.html#getRobots--"><code>getRobots()</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/IRobocodeEngine.html" title="interface in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/BattleSpecification.html" target="_top">Frames</a></li>
<li><a href="BattleSpecification.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
