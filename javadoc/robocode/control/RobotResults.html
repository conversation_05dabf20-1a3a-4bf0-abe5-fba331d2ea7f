<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RobotResults (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RobotResults (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/RobocodeListener.html" title="interface in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/RobotSetup.html" title="class in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/RobotResults.html" target="_top">Frames</a></li>
<li><a href="RobotResults.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode.BattleResults">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control</div>
<h2 title="Class RobotResults" class="title">Class RobotResults</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../robocode/BattleResults.html" title="class in robocode">robocode.BattleResults</a></li>
<li>
<ul class="inheritance">
<li>robocode.control.RobotResults</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RobotResults</span>
extends <a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a></pre>
<div class="block">Contains the battle results for an individual robot</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../serialized-form.html#robocode.control.RobotResults">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.robocode.BattleResults">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;robocode.<a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a></h3>
<code><a href="../../robocode/BattleResults.html#bulletDamage">bulletDamage</a>, <a href="../../robocode/BattleResults.html#bulletDamageBonus">bulletDamageBonus</a>, <a href="../../robocode/BattleResults.html#firsts">firsts</a>, <a href="../../robocode/BattleResults.html#lastSurvivorBonus">lastSurvivorBonus</a>, <a href="../../robocode/BattleResults.html#ramDamage">ramDamage</a>, <a href="../../robocode/BattleResults.html#ramDamageBonus">ramDamageBonus</a>, <a href="../../robocode/BattleResults.html#rank">rank</a>, <a href="../../robocode/BattleResults.html#score">score</a>, <a href="../../robocode/BattleResults.html#seconds">seconds</a>, <a href="../../robocode/BattleResults.html#survival">survival</a>, <a href="../../robocode/BattleResults.html#teamLeaderName">teamLeaderName</a>, <a href="../../robocode/BattleResults.html#thirds">thirds</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/RobotResults.html#RobotResults-robocode.control.RobotSpecification-robocode.BattleResults-">RobotResults</a></span>(<a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>&nbsp;robot,
            <a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&nbsp;results)</code>
<div class="block">Constructs new RobotResults based on a <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control"><code>RobotSpecification</code></a> and <a href="../../robocode/BattleResults.html" title="class in robocode"><code>BattleResults</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/RobotResults.html#RobotResults-robocode.control.RobotSpecification-java.lang.String-int-double-double-double-double-double-double-double-int-int-int-">RobotResults</a></span>(<a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>&nbsp;robot,
            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;teamLeaderName,
            int&nbsp;rank,
            double&nbsp;score,
            double&nbsp;survival,
            double&nbsp;lastSurvivorBonus,
            double&nbsp;bulletDamage,
            double&nbsp;bulletDamageBonus,
            double&nbsp;ramDamage,
            double&nbsp;ramDamageBonus,
            int&nbsp;firsts,
            int&nbsp;seconds,
            int&nbsp;thirds)</code>
<div class="block">Constructs a new RobotResults.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotResults.html#convertResults-robocode.BattleResults:A-">convertResults</a></span>(<a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]&nbsp;results)</code>
<div class="block">Converts an array of <a href="../../robocode/BattleResults.html" title="class in robocode"><code>BattleResults</code></a> into an array of <a href="../../robocode/control/RobotResults.html" title="class in robocode.control"><code>RobotResults</code></a>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotResults.html#equals-java.lang.Object-">equals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;obj)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotResults.html#getRobot--">getRobot</a></span>()</code>
<div class="block">Returns the robot these results are meant for.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotResults.html#hashCode--">hashCode</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.BattleResults">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a></h3>
<code><a href="../../robocode/BattleResults.html#compareTo-robocode.BattleResults-">compareTo</a>, <a href="../../robocode/BattleResults.html#getBulletDamage--">getBulletDamage</a>, <a href="../../robocode/BattleResults.html#getBulletDamageBonus--">getBulletDamageBonus</a>, <a href="../../robocode/BattleResults.html#getFirsts--">getFirsts</a>, <a href="../../robocode/BattleResults.html#getLastSurvivorBonus--">getLastSurvivorBonus</a>, <a href="../../robocode/BattleResults.html#getRamDamage--">getRamDamage</a>, <a href="../../robocode/BattleResults.html#getRamDamageBonus--">getRamDamageBonus</a>, <a href="../../robocode/BattleResults.html#getRank--">getRank</a>, <a href="../../robocode/BattleResults.html#getScore--">getScore</a>, <a href="../../robocode/BattleResults.html#getSeconds--">getSeconds</a>, <a href="../../robocode/BattleResults.html#getSurvival--">getSurvival</a>, <a href="../../robocode/BattleResults.html#getTeamLeaderName--">getTeamLeaderName</a>, <a href="../../robocode/BattleResults.html#getThirds--">getThirds</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RobotResults-robocode.control.RobotSpecification-java.lang.String-int-double-double-double-double-double-double-double-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RobotResults</h4>
<pre>public&nbsp;RobotResults(<a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>&nbsp;robot,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;teamLeaderName,
                    int&nbsp;rank,
                    double&nbsp;score,
                    double&nbsp;survival,
                    double&nbsp;lastSurvivorBonus,
                    double&nbsp;bulletDamage,
                    double&nbsp;bulletDamageBonus,
                    double&nbsp;ramDamage,
                    double&nbsp;ramDamageBonus,
                    int&nbsp;firsts,
                    int&nbsp;seconds,
                    int&nbsp;thirds)</pre>
<div class="block">Constructs a new RobotResults.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>robot</code> - is the robot these results are for</dd>
<dd><code>teamLeaderName</code> - is the team name</dd>
<dd><code>rank</code> - is the rank of the robot in the battle</dd>
<dd><code>score</code> - is the total score for the robot in the battle</dd>
<dd><code>survival</code> - is the survival score for the robot in the battle</dd>
<dd><code>lastSurvivorBonus</code> - is the last survivor bonus for the robot in the battle</dd>
<dd><code>bulletDamage</code> - is the bullet damage score for the robot in the battle</dd>
<dd><code>bulletDamageBonus</code> - is the bullet damage bonus for the robot in the battle</dd>
<dd><code>ramDamage</code> - is the ramming damage for the robot in the battle</dd>
<dd><code>ramDamageBonus</code> - is the ramming damage bonus for the robot in the battle</dd>
<dd><code>firsts</code> - is the number of rounds this robot placed first</dd>
<dd><code>seconds</code> - is the number of rounds this robot placed second</dd>
<dd><code>thirds</code> - is the number of rounds this robot placed third</dd>
</dl>
</li>
</ul>
<a name="RobotResults-robocode.control.RobotSpecification-robocode.BattleResults-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RobotResults</h4>
<pre>public&nbsp;RobotResults(<a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>&nbsp;robot,
                    <a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>&nbsp;results)</pre>
<div class="block">Constructs new RobotResults based on a <a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control"><code>RobotSpecification</code></a> and <a href="../../robocode/BattleResults.html" title="class in robocode"><code>BattleResults</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>robot</code> - the robot these results are for</dd>
<dd><code>results</code> - the battle results for the robot</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRobot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobot</h4>
<pre>public&nbsp;<a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>&nbsp;getRobot()</pre>
<div class="block">Returns the robot these results are meant for.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot these results are meant for.</dd>
</dl>
</li>
</ul>
<a name="convertResults-robocode.BattleResults:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertResults</h4>
<pre>public static&nbsp;<a href="../../robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a>[]&nbsp;convertResults(<a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]&nbsp;results)</pre>
<div class="block">Converts an array of <a href="../../robocode/BattleResults.html" title="class in robocode"><code>BattleResults</code></a> into an array of <a href="../../robocode/control/RobotResults.html" title="class in robocode.control"><code>RobotResults</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>results</code> - an array of BattleResults to convert.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of RobotResults converted from BattleResults.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../robocode/BattleResults.html#hashCode--">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a></code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;obj)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../robocode/BattleResults.html#equals-java.lang.Object-">equals</a></code>&nbsp;in class&nbsp;<code><a href="../../robocode/BattleResults.html" title="class in robocode">BattleResults</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/RobocodeListener.html" title="interface in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/RobotSetup.html" title="class in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/RobotResults.html" target="_top">Frames</a></li>
<li><a href="RobotResults.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode.BattleResults">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
