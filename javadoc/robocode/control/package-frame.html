<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode.control (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../robocode/control/package-summary.html" target="classFrame">robocode.control</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="IRobocodeEngine.html" title="interface in robocode.control" target="classFrame"><span class="interfaceName">IRobocodeEngine</span></a></li>
<li><a href="RobocodeListener.html" title="interface in robocode.control" target="classFrame"><span class="interfaceName">RobocodeListener</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="BattlefieldSpecification.html" title="class in robocode.control" target="classFrame">BattlefieldSpecification</a></li>
<li><a href="BattleSpecification.html" title="class in robocode.control" target="classFrame">BattleSpecification</a></li>
<li><a href="RandomFactory.html" title="class in robocode.control" target="classFrame">RandomFactory</a></li>
<li><a href="RobocodeEngine.html" title="class in robocode.control" target="classFrame">RobocodeEngine</a></li>
<li><a href="RobotResults.html" title="class in robocode.control" target="classFrame">RobotResults</a></li>
<li><a href="RobotSetup.html" title="class in robocode.control" target="classFrame">RobotSetup</a></li>
<li><a href="RobotSpecification.html" title="class in robocode.control" target="classFrame">RobotSpecification</a></li>
<li><a href="RobotTestBed.html" title="class in robocode.control" target="classFrame">RobotTestBed</a></li>
</ul>
</div>
</body>
</html>
