<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RobocodeListener (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RobocodeListener (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":38,"i1":38,"i2":38};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/RobocodeEngine.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/RobotResults.html" title="class in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/RobocodeListener.html" target="_top">Frames</a></li>
<li><a href="RobocodeListener.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control</div>
<h2 title="Interface RobocodeListener" class="title">Interface RobocodeListener</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the <a href="../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a> instead.
 <p>
 A listener interface for receiving callbacks from the <a href="../../robocode/control/RobocodeEngine.html" title="class in robocode.control"><code>RobocodeEngine</code></a>.</span></div>
</div>
<br>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public interface <span class="typeNameLabel">RobocodeListener</span></pre>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobocodeListener.html#battleAborted-robocode.control.BattleSpecification-">battleAborted</a></span>(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battle)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="../../robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>IBattleListener.onBattleFinished()</code></a> instead.
 <p>
 This method is called when a battle has been aborted.</span></div>
</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobocodeListener.html#battleComplete-robocode.control.BattleSpecification-robocode.control.RobotResults:A-">battleComplete</a></span>(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battle,
              <a href="../../robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a>[]&nbsp;results)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>IBattleListener.onBattleCompleted()</code></a> instead.
 <p>
 This method is called when a battle completes successfully.</span></div>
</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobocodeListener.html#battleMessage-java.lang.String-">battleMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="../../robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-"><code>IBattleListener.onBattleMessage()</code></a> instead.
 <p>
 This method is called when the game logs messages that is normally
 written out to the console.</span></div>
</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="battleComplete-robocode.control.BattleSpecification-robocode.control.RobotResults:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>battleComplete</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
void&nbsp;battleComplete(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battle,
                                 <a href="../../robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a>[]&nbsp;results)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Since 1.6.2. Use the
 <a href="../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>IBattleListener.onBattleCompleted()</code></a> instead.
 <p>
 This method is called when a battle completes successfully.</span></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battle</code> - information about the battle that completed</dd>
<dd><code>results</code> - an array containing the results for the individual robot</dd>
</dl>
</li>
</ul>
<a name="battleAborted-robocode.control.BattleSpecification-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>battleAborted</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
void&nbsp;battleAborted(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battle)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Since 1.6.2. Use the
 <a href="../../robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>IBattleListener.onBattleFinished()</code></a> instead.
 <p>
 This method is called when a battle has been aborted.</span></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battle</code> - information about the battle that was aborted</dd>
</dl>
</li>
</ul>
<a name="battleMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>battleMessage</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
void&nbsp;battleMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Since 1.6.2. Use the
 <a href="../../robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-"><code>IBattleListener.onBattleMessage()</code></a> instead.
 <p>
 This method is called when the game logs messages that is normally
 written out to the console.</span></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - the message logged by the game</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/RobocodeEngine.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/RobotResults.html" title="class in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/RobocodeListener.html" target="_top">Frames</a></li>
<li><a href="RobocodeListener.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
