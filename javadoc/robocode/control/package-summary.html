<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode.control (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="robocode.control (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/annotation/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../robocode/control/events/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;robocode.control</h1>
<div class="docSummary">
<div class="block">The Robocode Control API is used for controlling the Robocode
 application from another external application.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></td>
<td class="colLast">
<div class="block">Interface for the RobocodeEngine.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/control/RobocodeListener.html" title="interface in robocode.control">RobocodeListener</a></td>
<td class="colLast">Deprecated
<div class="block"><span class="deprecationComment">Since 1.6.2.</span></div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></td>
<td class="colLast">
<div class="block">Defines the size of a battlefield, which is a part of the <a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control"><code>BattleSpecification</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></td>
<td class="colLast">
<div class="block">A BattleSpecification defines a battle configuration used by the <a href="../../robocode/control/RobocodeEngine.html" title="class in robocode.control"><code>RobocodeEngine</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/control/RandomFactory.html" title="class in robocode.control">RandomFactory</a></td>
<td class="colLast">
<div class="block">The RandomFactory is used for controlling the generation of random numbers,
 and supports generating random numbers that are deterministic, which is
 useful for testing purposes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></td>
<td class="colLast">
<div class="block">The RobocodeEngine is the interface provided for external applications
 in order to let these applications run battles within the Robocode application,
 and to get the results from these battles.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></td>
<td class="colLast">
<div class="block">Contains the battle results for an individual robot</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a></td>
<td class="colLast">
<div class="block">Contains the initial position and heading for a robot.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></td>
<td class="colLast">
<div class="block">Defines the properties of a robot, which is returned from
 <a href="../../robocode/control/RobocodeEngine.html#getLocalRepository--"><code>RobocodeEngine.getLocalRepository()</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a>&lt;R extends <a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>&gt;</td>
<td class="colLast">
<div class="block">RobotTestBed provides a superclass that can be extended in order to implement JUnit tests
 for Robocode robots.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package robocode.control Description">Package robocode.control Description</h2>
<div class="block">The Robocode Control API is used for controlling the Robocode
 application from another external application. Basically, it is possible
 to run battles between selected robots and retrieve the results. However,
 it is also possible to get detailed information as snapshots during the
 battles regarding e.g. the positions and headings of the individual robots
 and bullets at a specific time during a battle.
 <p>
 The main entry class is the Robocode.Control.RobocodeEngine class, which
 must be instantiated by the application controlling Robocode. With the
 RobocodeEngine, a battle specification must be provided in order to run a
 battle. The battle specification specify the size of the battlefield, and
 which rules that must be used. In addition, the participant robots must be
 selected, which must exist in the robot directory of Robocode in
 advantage.
 
 <h2>Example</h2>
 <p>
 Here is a simple application that runs a battle in Robocode for 5 rounds
 on the default battlefield of 800x600 pixels. The robots selected for the
 battle are sample.RamFire and sample.Corners.
 
 <pre>
 import robocode.control.*;
 import robocode.control.events.*;
 
 //
 // Application that demonstrates how to run two sample robots in Robocode using the
 // RobocodeEngine from the robocode.control package.
 //
 // <AUTHOR> N. Larsen
 //
 public class BattleRunner {
 
     public static void main(String[] args) {
 
         // Disable log messages from Robocode
         RobocodeEngine.setLogMessagesEnabled(false);

         // Create the RobocodeEngine
         //   RobocodeEngine engine = new RobocodeEngine(); // Run from current working directory
         RobocodeEngine engine = new RobocodeEngine(new java.io.File("C:/Robocode")); // Run from C:/Robocode
 
         // Add our own battle listener to the RobocodeEngine 
         engine.addBattleListener(new BattleObserver());
 
         // Show the Robocode battle view
         engine.setVisible(true);
 
         // Setup the battle specification
 
         int numberOfRounds = 5;
         BattlefieldSpecification battlefield = new BattlefieldSpecification(800, 600); // 800x600
         RobotSpecification[] selectedRobots = engine.getLocalRepository("sample.RamFire,sample.Corners");
 
         BattleSpecification battleSpec = new BattleSpecification(numberOfRounds, battlefield, selectedRobots);
 
         // Run our specified battle and let it run till it is over
         engine.runBattle(battleSpec, true); // waits till the battle finishes
 
         // Cleanup our RobocodeEngine
         engine.close();
 
         // Make sure that the Java VM is shut down properly
         System.exit(0);
     }
 }
 
 //
 // Our private battle listener for handling the battle event we are interested in.
 //
 class BattleObserver extends BattleAdaptor {
 
     // Called when the battle is completed successfully with battle results
     public void onBattleCompleted(BattleCompletedEvent e) {
         System.out.println("-- Battle has completed --");
         
         // Print out the sorted results with the robot names
         System.out.println("Battle results:");
         for (robocode.BattleResults result : e.getSortedResults()) {
             System.out.println("  " + result.getTeamLeaderName() + ": " + result.getScore());
         }
     }
 
     // Called when the game sends out an information message during the battle
     public void onBattleMessage(BattleMessageEvent e) {
         System.out.println("Msg&gt; " + e.getMessage());
     }
 
     // Called when the game sends out an error message during the battle
     public void onBattleError(BattleErrorEvent e) {
         System.out.println("Err&gt; " + e.getError());
     }
 }
 </pre>
 <h2>Notice</h2>
 <p>
 In order to avoid ClassNotFoundException with your application, you will need to add most of the .jar files
 located under the /libs folder of the robocode directory to the classpath of your application.
 That is robocode.jar, picocontainer-xxx.jar etc. You can leave out roborumble.jar and the ones for UI and
 sound if you don't use the UI with the RobocodeEngine.</p></div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/annotation/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../robocode/control/events/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
