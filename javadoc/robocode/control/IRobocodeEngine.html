<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IRobocodeEngine (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IRobocodeEngine (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/RandomFactory.html" title="class in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/IRobocodeEngine.html" target="_top">Frames</a></li>
<li><a href="IRobocodeEngine.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control</div>
<h2 title="Interface IRobocodeEngine" class="title">Interface IRobocodeEngine</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IRobocodeEngine</span></pre>
<div class="block">Interface for the RobocodeEngine.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#abortCurrentBattle--">abortCurrentBattle</a></span>()</code>
<div class="block">Aborts the current battle if it is running and waits for the end.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#abortCurrentBattle-boolean-">abortCurrentBattle</a></span>(boolean&nbsp;waitTillEnd)</code>
<div class="block">Aborts the current battle if it is running.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-">addBattleListener</a></span>(<a href="../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a>&nbsp;listener)</code>
<div class="block">Adds a battle listener that must receive events occurring in battles.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#close--">close</a></span>()</code>
<div class="block">Closes the RobocodeEngine and releases any allocated resources it holds.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#getLocalRepository--">getLocalRepository</a></span>()</code>
<div class="block">Returns all robots available from the local robot repository of Robocode.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#getLocalRepository-java.lang.String-">getLocalRepository</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;selectedRobotList)</code>
<div class="block">Returns a selection of robots available from the local robot repository
 of Robocode.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Returns the installed version of Robocode controlled by this RobocodeEngine.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#removeBattleListener-robocode.control.events.IBattleListener-">removeBattleListener</a></span>(<a href="../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a>&nbsp;listener)</code>
<div class="block">Removes a battle listener that has previously been added to this object.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-">runBattle</a></span>(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification)</code>
<div class="block">Runs the specified battle.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-boolean-">runBattle</a></span>(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification,
         boolean&nbsp;waitTillOver)</code>
<div class="block">Runs the specified battle.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-">runBattle</a></span>(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;initialPositions,
         boolean&nbsp;waitTillOver)</code>
<div class="block">Runs the specified battle.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-boolean-">runBattle</a></span>(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;initialPositions,
         boolean&nbsp;waitTillOver,
         boolean&nbsp;enableRecording)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;visible)</code>
<div class="block">Shows or hides the Robocode window.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#takeScreenshot--">takeScreenshot</a></span>()</code>
<div class="block">Saves screenshot to disk, if the UI is initialized</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/IRobocodeEngine.html#waitTillBattleOver--">waitTillBattleOver</a></span>()</code>
<div class="block">Will block caller until current battle is over.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addBattleListener-robocode.control.events.IBattleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addBattleListener</h4>
<pre>void&nbsp;addBattleListener(<a href="../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a>&nbsp;listener)</pre>
<div class="block">Adds a battle listener that must receive events occurring in battles.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the battle listener that must retrieve the event from
                 the battles.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#removeBattleListener-robocode.control.events.IBattleListener-"><code>removeBattleListener(robocode.control.events.IBattleListener)</code></a></dd>
</dl>
</li>
</ul>
<a name="removeBattleListener-robocode.control.events.IBattleListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeBattleListener</h4>
<pre>void&nbsp;removeBattleListener(<a href="../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a>&nbsp;listener)</pre>
<div class="block">Removes a battle listener that has previously been added to this object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the battle listener that must be removed.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-"><code>addBattleListener(robocode.control.events.IBattleListener)</code></a></dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>void&nbsp;close()</pre>
<div class="block">Closes the RobocodeEngine and releases any allocated resources it holds.
 You should call this when you have finished using the RobocodeEngine.
 This method automatically disposes the Robocode window if it open.</div>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getVersion()</pre>
<div class="block">Returns the installed version of Robocode controlled by this RobocodeEngine.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the installed version of Robocode controlled by this RobocodeEngine.</dd>
</dl>
</li>
</ul>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>void&nbsp;setVisible(boolean&nbsp;visible)</pre>
<div class="block">Shows or hides the Robocode window.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>visible</code> - <code>true</code> if the Robocode window must be set visible;
                <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="getLocalRepository--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalRepository</h4>
<pre><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;getLocalRepository()</pre>
<div class="block">Returns all robots available from the local robot repository of Robocode.
 These robots must exists in the /robocode/robots directory, and must be
 compiled in advance, before these robot are returned with this method.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of all available robots from the local robot repository.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control"><code>RobotSpecification</code></a>, 
<a href="../../robocode/control/IRobocodeEngine.html#getLocalRepository-java.lang.String-"><code>getLocalRepository(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="getLocalRepository-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalRepository</h4>
<pre><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a>[]&nbsp;getLocalRepository(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;selectedRobotList)</pre>
<div class="block">Returns a selection of robots available from the local robot repository
 of Robocode. These robots must exists in the /robocode/robots directory,
 and must be compiled in advance, before these robot are returned with this method.
 <p>
 Notice: If a specified robot cannot be found in the repository, it will
 not be returned in the array of robots returned by this method.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>selectedRobotList</code> - a comma or space separated list of robots to
                          return. The full class name must be used for
                          specifying the individual robot, e.g.
                          "sample.Corners, sample.Crazy".</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the available robots from the local robot
         repository based on the selected robots specified with the
         <code>selectedRobotList</code> parameter.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control"><code>RobotSpecification</code></a>, 
<a href="../../robocode/control/IRobocodeEngine.html#getLocalRepository--"><code>getLocalRepository()</code></a></dd>
</dl>
</li>
</ul>
<a name="runBattle-robocode.control.BattleSpecification-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runBattle</h4>
<pre>void&nbsp;runBattle(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification)</pre>
<div class="block">Runs the specified battle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battleSpecification</code> - the specification of the battle to run including the
                            participation robots.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-boolean-"><code>runBattle(robocode.control.BattleSpecification, boolean)</code></a>, 
<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control"><code>BattleSpecification</code></a>, 
<a href="../../robocode/control/IRobocodeEngine.html#getLocalRepository--"><code>getLocalRepository()</code></a></dd>
</dl>
</li>
</ul>
<a name="runBattle-robocode.control.BattleSpecification-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runBattle</h4>
<pre>void&nbsp;runBattle(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification,
               boolean&nbsp;waitTillOver)</pre>
<div class="block">Runs the specified battle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battleSpecification</code> - the specification of the battle to run including the
                     participating robots.</dd>
<dd><code>waitTillOver</code> - will block caller till end of battle if set</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-"><code>runBattle(robocode.control.BattleSpecification)</code></a>, 
<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control"><code>BattleSpecification</code></a>, 
<a href="../../robocode/control/IRobocodeEngine.html#getLocalRepository--"><code>getLocalRepository()</code></a></dd>
</dl>
</li>
</ul>
<a name="runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runBattle</h4>
<pre>void&nbsp;runBattle(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;initialPositions,
               boolean&nbsp;waitTillOver)</pre>
<div class="block">Runs the specified battle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battleSpecification</code> - the specification of the battle to run including the
                     participating robots.</dd>
<dd><code>initialPositions</code> - a comma or space separated list like: x1,y1,heading1,
        x2,y2,heading2, which are the coordinates and heading of robot #1 and #2.
        So e.g. 0,0,180, 50,80,270 means that robot #1 has position (0,0) and
        heading 180, and robot #2 has position (50,80) and heading 270.</dd>
<dd><code>waitTillOver</code> - will block caller till end of battle if set</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>*******</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-"><code>runBattle(BattleSpecification)</code></a>, 
<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control"><code>BattleSpecification</code></a>, 
<a href="../../robocode/control/IRobocodeEngine.html#getLocalRepository--"><code>getLocalRepository()</code></a></dd>
</dl>
</li>
</ul>
<a name="runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runBattle</h4>
<pre>void&nbsp;runBattle(<a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a>&nbsp;battleSpecification,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;initialPositions,
               boolean&nbsp;waitTillOver,
               boolean&nbsp;enableRecording)</pre>
</li>
</ul>
<a name="waitTillBattleOver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>waitTillBattleOver</h4>
<pre>void&nbsp;waitTillBattleOver()</pre>
<div class="block">Will block caller until current battle is over.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-"><code>runBattle(robocode.control.BattleSpecification)</code></a>, 
<a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-boolean-"><code>runBattle(robocode.control.BattleSpecification, boolean)</code></a></dd>
</dl>
</li>
</ul>
<a name="abortCurrentBattle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>abortCurrentBattle</h4>
<pre>void&nbsp;abortCurrentBattle()</pre>
<div class="block">Aborts the current battle if it is running and waits for the end.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-"><code>runBattle(robocode.control.BattleSpecification)</code></a></dd>
</dl>
</li>
</ul>
<a name="abortCurrentBattle-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>abortCurrentBattle</h4>
<pre>void&nbsp;abortCurrentBattle(boolean&nbsp;waitTillEnd)</pre>
<div class="block">Aborts the current battle if it is running.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>waitTillEnd</code> - will block caller until the battle is over</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-"><code>runBattle(robocode.control.BattleSpecification)</code></a></dd>
</dl>
</li>
</ul>
<a name="takeScreenshot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>takeScreenshot</h4>
<pre>void&nbsp;takeScreenshot()</pre>
<div class="block">Saves screenshot to disk, if the UI is initialized</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/BattleSpecification.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../robocode/control/RandomFactory.html" title="class in robocode.control"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/IRobocodeEngine.html" target="_top">Frames</a></li>
<li><a href="IRobocodeEngine.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
