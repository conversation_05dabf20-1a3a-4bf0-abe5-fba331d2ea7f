<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IRobotSnapshot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IRobotSnapshot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/IRobotSnapshot.html" target="_top">Frames</a></li>
<li><a href="IRobotSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control.snapshot</div>
<h2 title="Interface IRobotSnapshot" class="title">Interface IRobotSnapshot</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IRobotSnapshot</span></pre>
<div class="block">Interface of a robot snapshot at a specific time in a battle.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getBodyColor--">getBodyColor</a></span>()</code>
<div class="block">Returns the color of the body.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getBodyHeading--">getBodyHeading</a></span>()</code>
<div class="block">Returns the body heading of the robot in radians.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getContestantIndex--">getContestantIndex</a></span>()</code>
<div class="block">Returns the contestant index, which is unique for each robot or team participating in a battle.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot">IDebugProperty</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getDebugProperties--">getDebugProperties</a></span>()</code>
<div class="block">Returns a snapshot of debug properties.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getEnergy--">getEnergy</a></span>()</code>
<div class="block">Returns the energy level of the robot.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getGunColor--">getGunColor</a></span>()</code>
<div class="block">Returns the color of the gun.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getGunHeading--">getGunHeading</a></span>()</code>
<div class="block">Returns the gun heading of the robot in radians.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getGunHeat--">getGunHeat</a></span>()</code>
<div class="block">Returns the gun heat of the robot.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of the robot.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getOutputStreamSnapshot--">getOutputStreamSnapshot</a></span>()</code>
<div class="block">Returns a snapshot of the output print stream for this robot.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getRadarColor--">getRadarColor</a></span>()</code>
<div class="block">Returns the color of the radar.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getRadarHeading--">getRadarHeading</a></span>()</code>
<div class="block">Returns the radar heading of the robot in radians.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getRobotIndex--">getRobotIndex</a></span>()</code>
<div class="block">Returns the index of the robot, which is unique for the specific robot and constant during a battle.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getScanColor--">getScanColor</a></span>()</code>
<div class="block">Returns the color of the scan arc.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getScoreSnapshot--">getScoreSnapshot</a></span>()</code>
<div class="block">Returns a snapshot of the current score for this robot.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getShortName--">getShortName</a></span>()</code>
<div class="block">Returns the short name of the robot.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getState--">getState</a></span>()</code>
<div class="block">Returns the robot state.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getTeamIndex--">getTeamIndex</a></span>()</code>
<div class="block">Returns the index of the team that this robot is a member of, which is unique for the specific team and constant
 during a battle.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getTeamName--">getTeamName</a></span>()</code>
<div class="block">Returns the name of the team, which can be the name of a robot if the contestant is not a team, but a robot.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getVelocity--">getVelocity</a></span>()</code>
<div class="block">Returns the velocity of the robot.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getVeryShortName--">getVeryShortName</a></span>()</code>
<div class="block">Returns the very short name of the robot.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getX--">getX</a></span>()</code>
<div class="block">Returns the X position of the robot.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getY--">getY</a></span>()</code>
<div class="block">Returns the Y position of the robot.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#isDroid--">isDroid</a></span>()</code>
<div class="block">Checks if this robot is a <a href="../../../robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#isPaintEnabled--">isPaintEnabled</a></span>()</code>
<div class="block">Checks if painting is enabled for this robot.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#isPaintRobot--">isPaintRobot</a></span>()</code>
<div class="block">Checks if this robot is a <a href="../../../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><code>IPaintRobot</code></a> or is invoking getGraphics()</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#isSentryRobot--">isSentryRobot</a></span>()</code>
<div class="block">Checks if this robot is a <a href="../../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a>.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#isSGPaintEnabled--">isSGPaintEnabled</a></span>()</code>
<div class="block">Checks if RobocodeSG painting (the point (0,0) is in the upper left corner) is enabled for this robot.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Returns the name of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the robot.</dd>
</dl>
</li>
</ul>
<a name="getShortName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShortName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getShortName()</pre>
<div class="block">Returns the short name of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the short name of the robot.</dd>
</dl>
</li>
</ul>
<a name="getVeryShortName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVeryShortName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getVeryShortName()</pre>
<div class="block">Returns the very short name of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the very short name of the robot.</dd>
</dl>
</li>
</ul>
<a name="getTeamName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeamName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTeamName()</pre>
<div class="block">Returns the name of the team, which can be the name of a robot if the contestant is not a team, but a robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the team.</dd>
</dl>
</li>
</ul>
<a name="getRobotIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobotIndex</h4>
<pre>int&nbsp;getRobotIndex()</pre>
<div class="block">Returns the index of the robot, which is unique for the specific robot and constant during a battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot index.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getTeamIndex--"><code>getTeamIndex()</code></a></dd>
</dl>
</li>
</ul>
<a name="getTeamIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTeamIndex</h4>
<pre>int&nbsp;getTeamIndex()</pre>
<div class="block">Returns the index of the team that this robot is a member of, which is unique for the specific team and constant
 during a battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the team index or -1 if the robot is not a member of a team.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7.4</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getRobotIndex--"><code>getRobotIndex()</code></a></dd>
</dl>
</li>
</ul>
<a name="getContestantIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContestantIndex</h4>
<pre>int&nbsp;getContestantIndex()</pre>
<div class="block">Returns the contestant index, which is unique for each robot or team participating in a battle.
 Note: If a team of robots is participating in a battle, this method will return the team index (see
 <a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getTeamIndex--"><code>getTeamIndex()</code></a>); otherwise the robot index (see <a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getRobotIndex--"><code>getRobotIndex()</code></a>) is used instead.
 This method is used for the battle results as scores are calculated for either a team of robots or individual
 robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the contestant index of the robot or team.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getRobotIndex--"><code>getRobotIndex()</code></a>, 
<a href="../../../robocode/control/snapshot/IRobotSnapshot.html#getTeamIndex--"><code>getTeamIndex()</code></a></dd>
</dl>
</li>
</ul>
<a name="getState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getState</h4>
<pre><a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>&nbsp;getState()</pre>
<div class="block">Returns the robot state.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the robot state.</dd>
</dl>
</li>
</ul>
<a name="getEnergy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnergy</h4>
<pre>double&nbsp;getEnergy()</pre>
<div class="block">Returns the energy level of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the energy level of the robot.</dd>
</dl>
</li>
</ul>
<a name="getVelocity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVelocity</h4>
<pre>double&nbsp;getVelocity()</pre>
<div class="block">Returns the velocity of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the velocity of the robot.</dd>
</dl>
</li>
</ul>
<a name="getBodyHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBodyHeading</h4>
<pre>double&nbsp;getBodyHeading()</pre>
<div class="block">Returns the body heading of the robot in radians.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the body heading of the robot in radians.</dd>
</dl>
</li>
</ul>
<a name="getGunHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeading</h4>
<pre>double&nbsp;getGunHeading()</pre>
<div class="block">Returns the gun heading of the robot in radians.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the gun heading of the robot in radians.</dd>
</dl>
</li>
</ul>
<a name="getRadarHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarHeading</h4>
<pre>double&nbsp;getRadarHeading()</pre>
<div class="block">Returns the radar heading of the robot in radians.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the radar heading of the robot in radians.</dd>
</dl>
</li>
</ul>
<a name="getGunHeat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeat</h4>
<pre>double&nbsp;getGunHeat()</pre>
<div class="block">Returns the gun heat of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the gun heat of the robot.</dd>
</dl>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>double&nbsp;getX()</pre>
<div class="block">Returns the X position of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the X position of the robot.</dd>
</dl>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>double&nbsp;getY()</pre>
<div class="block">Returns the Y position of the robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Y position of the robot.</dd>
</dl>
</li>
</ul>
<a name="getBodyColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBodyColor</h4>
<pre>int&nbsp;getBodyColor()</pre>
<div class="block">Returns the color of the body.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an ARGB color value. (Bits 24-31 are alpha, 16-23 are red, 8-15 are green, 0-7 are blue)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true#getRGB--" title="class or interface in java.awt"><code>Color.getRGB()</code></a></dd>
</dl>
</li>
</ul>
<a name="getGunColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunColor</h4>
<pre>int&nbsp;getGunColor()</pre>
<div class="block">Returns the color of the gun.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an ARGB color value. (Bits 24-31 are alpha, 16-23 are red, 8-15 are green, 0-7 are blue)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true#getRGB--" title="class or interface in java.awt"><code>Color.getRGB()</code></a></dd>
</dl>
</li>
</ul>
<a name="getRadarColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarColor</h4>
<pre>int&nbsp;getRadarColor()</pre>
<div class="block">Returns the color of the radar.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an ARGB color value. (Bits 24-31 are alpha, 16-23 are red, 8-15 are green, 0-7 are blue)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true#getRGB--" title="class or interface in java.awt"><code>Color.getRGB()</code></a></dd>
</dl>
</li>
</ul>
<a name="getScanColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScanColor</h4>
<pre>int&nbsp;getScanColor()</pre>
<div class="block">Returns the color of the scan arc.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an ARGB color value. (Bits 24-31 are alpha, 16-23 are red, 8-15 are green, 0-7 are blue)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true#getRGB--" title="class or interface in java.awt"><code>Color.getRGB()</code></a></dd>
</dl>
</li>
</ul>
<a name="isDroid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDroid</h4>
<pre>boolean&nbsp;isDroid()</pre>
<div class="block">Checks if this robot is a <a href="../../../robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this robot is a Droid; <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="isSentryRobot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSentryRobot</h4>
<pre>boolean&nbsp;isSentryRobot()</pre>
<div class="block">Checks if this robot is a <a href="../../../robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this robot is a BorderSentry; <code>false</code> otherwise.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>*******</dd>
</dl>
</li>
</ul>
<a name="isPaintRobot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPaintRobot</h4>
<pre>boolean&nbsp;isPaintRobot()</pre>
<div class="block">Checks if this robot is a <a href="../../../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><code>IPaintRobot</code></a> or is invoking getGraphics()</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this robot is a painting; <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="isPaintEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPaintEnabled</h4>
<pre>boolean&nbsp;isPaintEnabled()</pre>
<div class="block">Checks if painting is enabled for this robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if painting is enabled for this robot; <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="isSGPaintEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSGPaintEnabled</h4>
<pre>boolean&nbsp;isSGPaintEnabled()</pre>
<div class="block">Checks if RobocodeSG painting (the point (0,0) is in the upper left corner) is enabled for this robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if RobocodeSG painting is enabled for this robot; <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="getDebugProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDebugProperties</h4>
<pre><a href="../../../robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot">IDebugProperty</a>[]&nbsp;getDebugProperties()</pre>
<div class="block">Returns a snapshot of debug properties.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a snapshot of debug properties.</dd>
</dl>
</li>
</ul>
<a name="getOutputStreamSnapshot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutputStreamSnapshot</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getOutputStreamSnapshot()</pre>
<div class="block">Returns a snapshot of the output print stream for this robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a string containing the snapshot of the output print stream.</dd>
</dl>
</li>
</ul>
<a name="getScoreSnapshot--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getScoreSnapshot</h4>
<pre><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a>&nbsp;getScoreSnapshot()</pre>
<div class="block">Returns a snapshot of the current score for this robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a snapshot of the current score for this robot.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/IRobotSnapshot.html" target="_top">Frames</a></li>
<li><a href="IRobotSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
