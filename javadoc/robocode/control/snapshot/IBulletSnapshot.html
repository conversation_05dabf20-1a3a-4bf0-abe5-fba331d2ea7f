<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IBulletSnapshot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IBulletSnapshot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/IBulletSnapshot.html" target="_top">Frames</a></li>
<li><a href="IBulletSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control.snapshot</div>
<h2 title="Interface IBulletSnapshot" class="title">Interface IBulletSnapshot</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IBulletSnapshot</span></pre>
<div class="block">Interface of a bullet snapshot at a specific time in a battle.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getBulletId--">getBulletId</a></span>()</code>
<div class="block">Returns the ID of the bullet used for identifying the bullet in a collection of bullets.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color of the bullet.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getExplosionImageIndex--">getExplosionImageIndex</a></span>()</code>
<div class="block">Returns the explosion image index, which is different depending on the type of explosion.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getFrame--">getFrame</a></span>()</code>
<div class="block">Returns the current frame number to display, i.e.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getHeading--">getHeading</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getOwnerIndex--">getOwnerIndex</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getPaintX--">getPaintX</a></span>()</code>
<div class="block">Returns the X painting position of the bullet.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getPaintY--">getPaintY</a></span>()</code>
<div class="block">Returns the Y painting position of the bullet.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getPower--">getPower</a></span>()</code>
<div class="block">Returns the bullet power.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getState--">getState</a></span>()</code>
<div class="block">Returns the bullet state.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getVictimIndex--">getVictimIndex</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getX--">getX</a></span>()</code>
<div class="block">Returns the X position of the bullet.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getY--">getY</a></span>()</code>
<div class="block">Returns the Y position of the bullet.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#isExplosion--">isExplosion</a></span>()</code>
<div class="block">Checks if the bullet has become an explosion, i.e.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getState</h4>
<pre><a href="../../../robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a>&nbsp;getState()</pre>
<div class="block">Returns the bullet state.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bullet state.</dd>
</dl>
</li>
</ul>
<a name="getPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPower</h4>
<pre>double&nbsp;getPower()</pre>
<div class="block">Returns the bullet power.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the bullet power.</dd>
</dl>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>double&nbsp;getX()</pre>
<div class="block">Returns the X position of the bullet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the X position of the bullet.</dd>
</dl>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>double&nbsp;getY()</pre>
<div class="block">Returns the Y position of the bullet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Y position of the bullet.</dd>
</dl>
</li>
</ul>
<a name="getPaintX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaintX</h4>
<pre>double&nbsp;getPaintX()</pre>
<div class="block">Returns the X painting position of the bullet.
 Note that this is not necessarily equal to the X position of the bullet, even though
 it will be in most cases. The painting position of the bullet is needed as the bullet
 will "stick" to its victim when it has been hit, but only visually.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the X painting position of the bullet.</dd>
</dl>
</li>
</ul>
<a name="getPaintY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaintY</h4>
<pre>double&nbsp;getPaintY()</pre>
<div class="block">Returns the Y painting position of the bullet.
 Note that this is not necessarily equal to the Y position of the bullet, even though
 it will be in most cases. The painting position of the bullet is needed as the bullet
 will "stick" to its victim when it has been hit, but only visually.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the Y painting position of the bullet.</dd>
</dl>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>int&nbsp;getColor()</pre>
<div class="block">Returns the color of the bullet.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an ARGB color value. (Bits 24-31 are alpha, 16-23 are red, 8-15 are green, 0-7 are blue)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/awt/Color.html?is-external=true#getRGB--" title="class or interface in java.awt"><code>Color.getRGB()</code></a></dd>
</dl>
</li>
</ul>
<a name="getFrame--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrame</h4>
<pre>int&nbsp;getFrame()</pre>
<div class="block">Returns the current frame number to display, i.e. when the bullet explodes.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current frame number.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#isExplosion--"><code>isExplosion()</code></a>, 
<a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getExplosionImageIndex--"><code>getExplosionImageIndex()</code></a></dd>
</dl>
</li>
</ul>
<a name="isExplosion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isExplosion</h4>
<pre>boolean&nbsp;isExplosion()</pre>
<div class="block">Checks if the bullet has become an explosion, i.e. when it a robot or bullet has been hit.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the bullet is an explosion; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getFrame--"><code>getFrame()</code></a>, 
<a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getExplosionImageIndex--"><code>getExplosionImageIndex()</code></a></dd>
</dl>
</li>
</ul>
<a name="getExplosionImageIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExplosionImageIndex</h4>
<pre>int&nbsp;getExplosionImageIndex()</pre>
<div class="block">Returns the explosion image index, which is different depending on the type of explosion.
 E.g. if it is a small explosion on a robot that has been hit by this bullet,
 or a big explosion when a robot dies.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the explosion image index.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/IBulletSnapshot.html#isExplosion--"><code>isExplosion()</code></a>, 
<a href="../../../robocode/control/snapshot/IBulletSnapshot.html#getExplosionImageIndex--"><code>getExplosionImageIndex()</code></a></dd>
</dl>
</li>
</ul>
<a name="getBulletId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBulletId</h4>
<pre>int&nbsp;getBulletId()</pre>
<div class="block">Returns the ID of the bullet used for identifying the bullet in a collection of bullets.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the ID of the bullet.</dd>
</dl>
</li>
</ul>
<a name="getHeading--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeading</h4>
<pre>double&nbsp;getHeading()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>heading of the bullet</dd>
</dl>
</li>
</ul>
<a name="getVictimIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVictimIndex</h4>
<pre>int&nbsp;getVictimIndex()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>contestantIndex of the victim, or -1 if still in air</dd>
</dl>
</li>
</ul>
<a name="getOwnerIndex--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getOwnerIndex</h4>
<pre>int&nbsp;getOwnerIndex()</pre>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>contestantIndex of the owner</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/IBulletSnapshot.html" target="_top">Frames</a></li>
<li><a href="IBulletSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
