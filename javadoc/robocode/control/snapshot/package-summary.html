<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode.control.snapshot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="robocode.control.snapshot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/events/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../robocode/robotinterfaces/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;robocode.control.snapshot</h1>
<div class="docSummary">
<div class="block">Snapshots of the battle turns, robots, bullets, scores etc.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></td>
<td class="colLast">
<div class="block">Interface of a bullet snapshot at a specific time in a battle.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot">IDebugProperty</a></td>
<td class="colLast">
<div class="block">Interface of a debug property, which is a key-value pair.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></td>
<td class="colLast">
<div class="block">Interface of a robot snapshot at a specific time in a battle.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></td>
<td class="colLast">
<div class="block">Interface of a score snapshot at a specific time in a battle.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></td>
<td class="colLast">
<div class="block">Interface of a battle turn snapshot at a specific time in a battle.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></td>
<td class="colLast">
<div class="block">Defines a bullet state, which can be: just fired, moving somewhere, hitting a victim,
 hitting another bullet, hitting the wall, exploded, or inactive.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></td>
<td class="colLast">
<div class="block">Defines a robot state, which can be: active on the battlefield, hitting a wall or robot this turn, or dead.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package robocode.control.snapshot Description">Package robocode.control.snapshot Description</h2>
<div class="block">Snapshots of the battle turns, robots, bullets, scores etc.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/events/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../robocode/robotinterfaces/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
