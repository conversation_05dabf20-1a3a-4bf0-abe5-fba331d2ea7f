<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ITurnSnapshot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ITurnSnapshot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/ITurnSnapshot.html" target="_top">Frames</a></li>
<li><a href="ITurnSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control.snapshot</div>
<h2 title="Interface ITurnSnapshot" class="title">Interface ITurnSnapshot</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ITurnSnapshot</span></pre>
<div class="block">Interface of a battle turn snapshot at a specific time in a battle.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getBullets--">getBullets</a></span>()</code>
<div class="block">Returns a list of snapshots for the bullets that are currently on the battlefield.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getIndexedTeamScores--">getIndexedTeamScores</a></span>()</code>
<div class="block">Returns an array of indexed scores grouped by teams that can be used to determine the score
 for the individual team based on the team index.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getRobots--">getRobots</a></span>()</code>
<div class="block">Returns a list of snapshots for the robots participating in the battle.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getRound--">getRound</a></span>()</code>
<div class="block">Returns the current round of the battle.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getSortedTeamScores--">getSortedTeamScores</a></span>()</code>
<div class="block">Returns an array of sorted scores grouped by teams, ordered by position.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getTPS--">getTPS</a></span>()</code>
<div class="block">Returns the current TPS (turns per second) rate.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getTurn--">getTurn</a></span>()</code>
<div class="block">Returns the current turn in the battle round.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRobots--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobots</h4>
<pre><a href="../../../robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a>[]&nbsp;getRobots()</pre>
<div class="block">Returns a list of snapshots for the robots participating in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a list of snapshots for the robots participating in the battle.</dd>
</dl>
</li>
</ul>
<a name="getBullets--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBullets</h4>
<pre><a href="../../../robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a>[]&nbsp;getBullets()</pre>
<div class="block">Returns a list of snapshots for the bullets that are currently on the battlefield.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a list of snapshots for the bullets that are currently on the battlefield.</dd>
</dl>
</li>
</ul>
<a name="getTPS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTPS</h4>
<pre>int&nbsp;getTPS()</pre>
<div class="block">Returns the current TPS (turns per second) rate.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current TPS (turns per second) rate.</dd>
</dl>
</li>
</ul>
<a name="getRound--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRound</h4>
<pre>int&nbsp;getRound()</pre>
<div class="block">Returns the current round of the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current round of the battle.</dd>
</dl>
</li>
</ul>
<a name="getTurn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTurn</h4>
<pre>int&nbsp;getTurn()</pre>
<div class="block">Returns the current turn in the battle round.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current turn in the battle round.</dd>
</dl>
</li>
</ul>
<a name="getSortedTeamScores--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSortedTeamScores</h4>
<pre><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a>[]&nbsp;getSortedTeamScores()</pre>
<div class="block">Returns an array of sorted scores grouped by teams, ordered by position.
 Note that the team index cannot be used to determine the score with the sorted scores.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of sorted IScoreSnapshots, where the bigger scores are placed first in the list.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getIndexedTeamScores--"><code>getIndexedTeamScores()</code></a></dd>
</dl>
</li>
</ul>
<a name="getIndexedTeamScores--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getIndexedTeamScores</h4>
<pre><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a>[]&nbsp;getIndexedTeamScores()</pre>
<div class="block">Returns an array of indexed scores grouped by teams that can be used to determine the score
 for the individual team based on the team index.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of indexed IScoreSnapshots, where each index matches an index of a specific team.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/ITurnSnapshot.html#getSortedTeamScores--"><code>getSortedTeamScores()</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/ITurnSnapshot.html" target="_top">Frames</a></li>
<li><a href="ITurnSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
