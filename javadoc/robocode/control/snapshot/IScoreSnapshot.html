<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>IScoreSnapshot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IScoreSnapshot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/IScoreSnapshot.html" target="_top">Frames</a></li>
<li><a href="IScoreSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control.snapshot</div>
<h2 title="Interface IScoreSnapshot" class="title">Interface IScoreSnapshot</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">IScoreSnapshot</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</pre>
<div class="block">Interface of a score snapshot at a specific time in a battle.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getCurrentBulletDamageScore--">getCurrentBulletDamageScore</a></span>()</code>
<div class="block">Returns the current bullet damage score.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getCurrentBulletKillBonus--">getCurrentBulletKillBonus</a></span>()</code>
<div class="block">Returns the current bullet kill bonus.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getCurrentRammingDamageScore--">getCurrentRammingDamageScore</a></span>()</code>
<div class="block">Returns the current ramming damage score.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getCurrentRammingKillBonus--">getCurrentRammingKillBonus</a></span>()</code>
<div class="block">Returns the current ramming kill bonus.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getCurrentScore--">getCurrentScore</a></span>()</code>
<div class="block">Returns the current score.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getCurrentSurvivalBonus--">getCurrentSurvivalBonus</a></span>()</code>
<div class="block">Returns the current survival bonus.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getCurrentSurvivalScore--">getCurrentSurvivalScore</a></span>()</code>
<div class="block">Returns the current survival score.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of the contestant, i.e.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalBulletDamageScore--">getTotalBulletDamageScore</a></span>()</code>
<div class="block">Returns the total bullet damage score.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalBulletKillBonus--">getTotalBulletKillBonus</a></span>()</code>
<div class="block">Returns the total bullet kill bonus.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalFirsts--">getTotalFirsts</a></span>()</code>
<div class="block">Returns the total number of first places.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalLastSurvivorBonus--">getTotalLastSurvivorBonus</a></span>()</code>
<div class="block">Returns the total last survivor score.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalRammingDamageScore--">getTotalRammingDamageScore</a></span>()</code>
<div class="block">Returns the total ramming damage score.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalRammingKillBonus--">getTotalRammingKillBonus</a></span>()</code>
<div class="block">Returns the total ramming kill bonus.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalScore--">getTotalScore</a></span>()</code>
<div class="block">Returns the total score.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalSeconds--">getTotalSeconds</a></span>()</code>
<div class="block">Returns the total number of second places.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalSurvivalScore--">getTotalSurvivalScore</a></span>()</code>
<div class="block">Returns the total survival score.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/IScoreSnapshot.html#getTotalThirds--">getTotalThirds</a></span>()</code>
<div class="block">Returns the total number of third places.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Comparable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true#compareTo-T-" title="class or interface in java.lang">compareTo</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Returns the name of the contestant, i.e. a robot or team.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name of the contestant, i.e. a robot or team.</dd>
</dl>
</li>
</ul>
<a name="getTotalScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalScore</h4>
<pre>double&nbsp;getTotalScore()</pre>
<div class="block">Returns the total score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total score.</dd>
</dl>
</li>
</ul>
<a name="getTotalSurvivalScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalSurvivalScore</h4>
<pre>double&nbsp;getTotalSurvivalScore()</pre>
<div class="block">Returns the total survival score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total survival score.</dd>
</dl>
</li>
</ul>
<a name="getTotalLastSurvivorBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalLastSurvivorBonus</h4>
<pre>double&nbsp;getTotalLastSurvivorBonus()</pre>
<div class="block">Returns the total last survivor score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total last survivor score.</dd>
</dl>
</li>
</ul>
<a name="getTotalBulletDamageScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalBulletDamageScore</h4>
<pre>double&nbsp;getTotalBulletDamageScore()</pre>
<div class="block">Returns the total bullet damage score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total bullet damage score.</dd>
</dl>
</li>
</ul>
<a name="getTotalBulletKillBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalBulletKillBonus</h4>
<pre>double&nbsp;getTotalBulletKillBonus()</pre>
<div class="block">Returns the total bullet kill bonus.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total bullet kill bonus.</dd>
</dl>
</li>
</ul>
<a name="getTotalRammingDamageScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalRammingDamageScore</h4>
<pre>double&nbsp;getTotalRammingDamageScore()</pre>
<div class="block">Returns the total ramming damage score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total ramming damage score.</dd>
</dl>
</li>
</ul>
<a name="getTotalRammingKillBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalRammingKillBonus</h4>
<pre>double&nbsp;getTotalRammingKillBonus()</pre>
<div class="block">Returns the total ramming kill bonus.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total ramming kill bonus.</dd>
</dl>
</li>
</ul>
<a name="getTotalFirsts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalFirsts</h4>
<pre>int&nbsp;getTotalFirsts()</pre>
<div class="block">Returns the total number of first places.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total number of first places.</dd>
</dl>
</li>
</ul>
<a name="getTotalSeconds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalSeconds</h4>
<pre>int&nbsp;getTotalSeconds()</pre>
<div class="block">Returns the total number of second places.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total number of second places.</dd>
</dl>
</li>
</ul>
<a name="getTotalThirds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalThirds</h4>
<pre>int&nbsp;getTotalThirds()</pre>
<div class="block">Returns the total number of third places.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the total number of third places.</dd>
</dl>
</li>
</ul>
<a name="getCurrentScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentScore</h4>
<pre>double&nbsp;getCurrentScore()</pre>
<div class="block">Returns the current score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current score.</dd>
</dl>
</li>
</ul>
<a name="getCurrentSurvivalScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentSurvivalScore</h4>
<pre>double&nbsp;getCurrentSurvivalScore()</pre>
<div class="block">Returns the current survival score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current survival score.</dd>
</dl>
</li>
</ul>
<a name="getCurrentSurvivalBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentSurvivalBonus</h4>
<pre>double&nbsp;getCurrentSurvivalBonus()</pre>
<div class="block">Returns the current survival bonus.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current survival bonus.</dd>
</dl>
</li>
</ul>
<a name="getCurrentBulletDamageScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentBulletDamageScore</h4>
<pre>double&nbsp;getCurrentBulletDamageScore()</pre>
<div class="block">Returns the current bullet damage score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current bullet damage score.</dd>
</dl>
</li>
</ul>
<a name="getCurrentBulletKillBonus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentBulletKillBonus</h4>
<pre>double&nbsp;getCurrentBulletKillBonus()</pre>
<div class="block">Returns the current bullet kill bonus.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current bullet kill bonus.</dd>
</dl>
</li>
</ul>
<a name="getCurrentRammingDamageScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrentRammingDamageScore</h4>
<pre>double&nbsp;getCurrentRammingDamageScore()</pre>
<div class="block">Returns the current ramming damage score.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current ramming damage score.</dd>
</dl>
</li>
</ul>
<a name="getCurrentRammingKillBonus--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCurrentRammingKillBonus</h4>
<pre>double&nbsp;getCurrentRammingKillBonus()</pre>
<div class="block">Returns the current ramming kill bonus.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the current ramming kill bonus.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/IScoreSnapshot.html" target="_top">Frames</a></li>
<li><a href="IScoreSnapshot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
