<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RobotState (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RobotState (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":9,"i6":9,"i7":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/RobotState.html" target="_top">Frames</a></li>
<li><a href="RobotState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control.snapshot</div>
<h2 title="Enum RobotState" class="title">Enum RobotState</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">java.lang.Enum</a>&lt;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>&gt;</li>
<li>
<ul class="inheritance">
<li>robocode.control.snapshot.RobotState</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public enum <span class="typeNameLabel">RobotState</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&lt;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>&gt;</pre>
<div class="block">Defines a robot state, which can be: active on the battlefield, hitting a wall or robot this turn, or dead.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Flemming N. Larsen (original)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#ACTIVE">ACTIVE</a></span></code>
<div class="block">The robot is active on the battlefield and has not hit the wall or a robot at this turn.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#DEAD">DEAD</a></span></code>
<div class="block">The robot is dead.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#HIT_ROBOT">HIT_ROBOT</a></span></code>
<div class="block">The robot has hit another robot at this turn.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#HIT_WALL">HIT_WALL</a></span></code>
<div class="block">The robot has hit a wall, i.e.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#getValue--">getValue</a></span>()</code>
<div class="block">Returns the state as an integer value.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#isAlive--">isAlive</a></span>()</code>
<div class="block">Checks if the robot is alive.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#isDead--">isDead</a></span>()</code>
<div class="block">Checks if the robot is dead.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#isHitRobot--">isHitRobot</a></span>()</code>
<div class="block">Checks if the robot has hit another robot.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#isHitWall--">isHitWall</a></span>()</code>
<div class="block">Checks if the robot has hit the wall, i.e.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#toState-int-">toState</a></span>(int&nbsp;value)</code>
<div class="block">Returns a RobotState based on an integer value that represents a RobotState.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#valueOf-java.lang.String-">valueOf</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/snapshot/RobotState.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#compareTo-E-" title="class or interface in java.lang">compareTo</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#getDeclaringClass--" title="class or interface in java.lang">getDeclaringClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#name--" title="class or interface in java.lang">name</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#ordinal--" title="class or interface in java.lang">ordinal</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true#valueOf-java.lang.Class-java.lang.String-" title="class or interface in java.lang">valueOf</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="ACTIVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ACTIVE</h4>
<pre>public static final&nbsp;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a> ACTIVE</pre>
<div class="block">The robot is active on the battlefield and has not hit the wall or a robot at this turn.</div>
</li>
</ul>
<a name="HIT_WALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIT_WALL</h4>
<pre>public static final&nbsp;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a> HIT_WALL</pre>
<div class="block">The robot has hit a wall, i.e. one of the four borders, at this turn. This state only last one turn.</div>
</li>
</ul>
<a name="HIT_ROBOT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIT_ROBOT</h4>
<pre>public static final&nbsp;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a> HIT_ROBOT</pre>
<div class="block">The robot has hit another robot at this turn. This state only last one turn.</div>
</li>
</ul>
<a name="DEAD">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEAD</h4>
<pre>public static final&nbsp;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a> DEAD</pre>
<div class="block">The robot is dead.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (RobotState c : RobotState.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>&nbsp;valueOf(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/IllegalArgumentException.html?is-external=true" title="class or interface in java.lang">IllegalArgumentException</a></code> - if this enum type has no constant with the specified name</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/NullPointerException.html?is-external=true" title="class or interface in java.lang">NullPointerException</a></code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
<div class="block">Returns the state as an integer value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an integer value representing this state.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/RobotState.html#toState-int-"><code>toState(int)</code></a></dd>
</dl>
</li>
</ul>
<a name="toState-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toState</h4>
<pre>public static&nbsp;<a href="../../../robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a>&nbsp;toState(int&nbsp;value)</pre>
<div class="block">Returns a RobotState based on an integer value that represents a RobotState.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - the integer value that represents a specific RobotState.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a RobotState that corresponds to the specific integer value.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/IllegalArgumentException.html?is-external=true" title="class or interface in java.lang">IllegalArgumentException</a></code> - if the specified value does not correspond
                                  to a RobotState and hence is invalid.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/RobotState.html#getValue--"><code>getValue()</code></a></dd>
</dl>
</li>
</ul>
<a name="isAlive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAlive</h4>
<pre>public&nbsp;boolean&nbsp;isAlive()</pre>
<div class="block">Checks if the robot is alive.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the robot is alive; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/RobotState.html#isDead--"><code>isDead()</code></a></dd>
</dl>
</li>
</ul>
<a name="isDead--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDead</h4>
<pre>public&nbsp;boolean&nbsp;isDead()</pre>
<div class="block">Checks if the robot is dead.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the robot is dead; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/RobotState.html#isAlive--"><code>isAlive()</code></a></dd>
</dl>
</li>
</ul>
<a name="isHitRobot--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHitRobot</h4>
<pre>public&nbsp;boolean&nbsp;isHitRobot()</pre>
<div class="block">Checks if the robot has hit another robot.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the robot has hit a robot; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/RobotState.html#isHitWall--"><code>isHitWall()</code></a></dd>
</dl>
</li>
</ul>
<a name="isHitWall--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isHitWall</h4>
<pre>public&nbsp;boolean&nbsp;isHitWall()</pre>
<div class="block">Checks if the robot has hit the wall, i.e. one of the four borders.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the robot has hit the wall; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/snapshot/RobotState.html#isHitRobot--"><code>isHitRobot()</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/snapshot/RobotState.html" target="_top">Frames</a></li>
<li><a href="RobotState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
