<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RobotTestBed (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RobotTestBed (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":9,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":6,"i11":10,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9,"i32":9,"i33":9,"i34":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/RobotTestBed.html" target="_top">Frames</a></li>
<li><a href="RobotTestBed.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control</div>
<h2 title="Class RobotTestBed" class="title">Class RobotTestBed&lt;R extends <a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">robocode.control.events.BattleAdaptor</a></li>
<li>
<ul class="inheritance">
<li>robocode.control.RobotTestBed&lt;R&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">RobotTestBed&lt;R extends <a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>&gt;</span>
extends <a href="../../robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></pre>
<div class="block">RobotTestBed provides a superclass that can be extended in order to implement JUnit tests
 for Robocode robots.
 <p>
 The user must set the system property robocode.home to the location of the robocode installation,
 otherwise we cannot set up the Robocode engine.  If robocode.home is not a system property,
 we throw a RuntimeException.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Philip Johnson (original), Pavel Savara (contributor), Flemming N. Larsen (contributor)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#battleFieldSpec">battleFieldSpec</a></span></code>
<div class="block">The battlefield specification, which is the default.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static <a href="../../robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#engine">engine</a></span></code>
<div class="block">The Robocode game engine instance used for this test.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected robocode.control.RobotTestBed.EngineErrorsListener</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#engineErrorsListener">engineErrorsListener</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#errors">errors</a></span></code>
<div class="block">The number of errors generated during this battle so far.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/StringBuilder.html?is-external=true" title="class or interface in java.lang">StringBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#errorText">errorText</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#lastError">lastError</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#messages">messages</a></span></code>
<div class="block">The number of messages generated during this battle so far.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../robocode/control/RobotTestBed.html" title="type parameter in RobotTestBed">R</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#robotObject">robotObject</a></span></code>
<div class="block">Instance of tested robot</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#robotsPath">robotsPath</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected robocode.control.RobotTestBed.TestErrorListener</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#testErrorListener">testErrorListener</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#RobotTestBed--">RobotTestBed</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#after--">after</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#afterInit--">afterInit</a></span>()</code>
<div class="block">Called after the engine is initialized to perform additional setup.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#before--">before</a></span>()</code>
<div class="block">The setup method run before each test, which sets up the listener on the engine for testing.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#beforeInit--">beforeInit</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#cleanup--">cleanup</a></span>()</code>
<div class="block">Releases any resources used by this test bed.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#getEnemyName--">getEnemyName</a></span>()</code>
<div class="block">Must return a fully qualified enemy robot name to be in this battle.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#getExpectedErrors--">getExpectedErrors</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#getExpectedRobotCount-java.lang.String-">getExpectedRobotCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;robotList)</code>
<div class="block">Provides the number of robots in this battle.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#getInitialPositions--">getInitialPositions</a></span>()</code>
<div class="block">Returns a comma or space separated list like: x1,y1,heading1, x2,y2,heading2, which are the
 coordinates and heading of robot #1 and #2.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#getNumRounds--">getNumRounds</a></span>()</code>
<div class="block">Provides the number of rounds in this battle.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>abstract <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#getRobotName--">getRobotName</a></span>()</code>
<div class="block">Must return a fully qualified robot name to be in this battle.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isDeterministic--">isDeterministic</a></span>()</code>
<div class="block">Defaults to true, indicating that the battle is deterministic and robots will always start
 in the same position each time.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isDumpingErrors--">isDumpingErrors</a></span>()</code>
<div class="block">Gets whether error messages should be printed out.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isDumpingMessages--">isDumpingMessages</a></span>()</code>
<div class="block">Gets whether robot messages should be printed out.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isDumpingOutput--">isDumpingOutput</a></span>()</code>
<div class="block">Gets whether robot output should be printed out.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isDumpingPositions--">isDumpingPositions</a></span>()</code>
<div class="block">Gets whether robot positions should be printed during each turn.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isDumpingTurns--">isDumpingTurns</a></span>()</code>
<div class="block">Gets whether each turn should be printed out.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isEnableRecording--">isEnableRecording</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#isEnableScreenshots--">isEnableScreenshots</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#run--">run</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#run-java.lang.String-">run</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;enemyName)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#run-java.lang.String-java.lang.String-">run</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;robotName,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;enemyName)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#runBattle-java.lang.String-int-java.lang.String-">runBattle</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;robotList,
         int&nbsp;numRounds,
         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;initialPositions)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#runSetup--">runSetup</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#runTeardown--">runTeardown</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setDumpingErrors-boolean-">setDumpingErrors</a></span>(boolean&nbsp;dumpingErrors)</code>
<div class="block">Sets whether error messages should be printed out.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setDumpingMessages-boolean-">setDumpingMessages</a></span>(boolean&nbsp;dumpingMessages)</code>
<div class="block">Sets whether robot messages should be printed out.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setDumpingOutput-boolean-">setDumpingOutput</a></span>(boolean&nbsp;dumpingOutput)</code>
<div class="block">Sets whether robot output should be printed out.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setDumpingPositions-boolean-">setDumpingPositions</a></span>(boolean&nbsp;dumpingPositions)</code>
<div class="block">Sets whether robot positions should be printed during each turn.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setDumpingTurns-boolean-">setDumpingTurns</a></span>(boolean&nbsp;dumpingTurns)</code>
<div class="block">Sets whether each turn should be printed out.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setErrorDumping-boolean-">setErrorDumping</a></span>(boolean&nbsp;value)</code>
<div class="block">Thread-safe method to modify the error dumping flag.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setMessageDumping-boolean-">setMessageDumping</a></span>(boolean&nbsp;value)</code>
<div class="block">Thread-safe method to modify the message dumping flag.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setOutputDumping-boolean-">setOutputDumping</a></span>(boolean&nbsp;value)</code>
<div class="block">Thread-safe method to modify the output dumping flag.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setPositionDumping-boolean-">setPositionDumping</a></span>(boolean&nbsp;value)</code>
<div class="block">Thread-safe method to modify the position dumping flag.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/control/RobotTestBed.html#setTurnDumping-boolean-">setTurnDumping</a></span>(boolean&nbsp;value)</code>
<div class="block">Thread-safe method to modify the turn dumping flag.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.control.events.BattleAdaptor">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.control.events.<a href="../../robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></h3>
<code><a href="../../robocode/control/events/BattleAdaptor.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-">onBattleCompleted</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onBattleError-robocode.control.events.BattleErrorEvent-">onBattleError</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-">onBattleFinished</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onBattleMessage-robocode.control.events.BattleMessageEvent-">onBattleMessage</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onBattlePaused-robocode.control.events.BattlePausedEvent-">onBattlePaused</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onBattleResumed-robocode.control.events.BattleResumedEvent-">onBattleResumed</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onBattleStarted-robocode.control.events.BattleStartedEvent-">onBattleStarted</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onRoundEnded-robocode.control.events.RoundEndedEvent-">onRoundEnded</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onRoundStarted-robocode.control.events.RoundStartedEvent-">onRoundStarted</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onTurnEnded-robocode.control.events.TurnEndedEvent-">onTurnEnded</a>, <a href="../../robocode/control/events/BattleAdaptor.html#onTurnStarted-robocode.control.events.TurnStartedEvent-">onTurnStarted</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="engine">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>engine</h4>
<pre>protected static volatile&nbsp;<a href="../../robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a> engine</pre>
<div class="block">The Robocode game engine instance used for this test.</div>
</li>
</ul>
<a name="battleFieldSpec">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>battleFieldSpec</h4>
<pre>protected final&nbsp;<a href="../../robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a> battleFieldSpec</pre>
<div class="block">The battlefield specification, which is the default.</div>
</li>
</ul>
<a name="errors">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>errors</h4>
<pre>protected&nbsp;int errors</pre>
<div class="block">The number of errors generated during this battle so far.</div>
</li>
</ul>
<a name="errorText">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>errorText</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/StringBuilder.html?is-external=true" title="class or interface in java.lang">StringBuilder</a> errorText</pre>
</li>
</ul>
<a name="lastError">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastError</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a> lastError</pre>
</li>
</ul>
<a name="engineErrorsListener">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>engineErrorsListener</h4>
<pre>protected&nbsp;robocode.control.RobotTestBed.EngineErrorsListener engineErrorsListener</pre>
</li>
</ul>
<a name="testErrorListener">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>testErrorListener</h4>
<pre>protected&nbsp;robocode.control.RobotTestBed.TestErrorListener testErrorListener</pre>
</li>
</ul>
<a name="messages">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>messages</h4>
<pre>protected&nbsp;int messages</pre>
<div class="block">The number of messages generated during this battle so far.</div>
</li>
</ul>
<a name="robotsPath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>robotsPath</h4>
<pre>protected static volatile&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> robotsPath</pre>
</li>
</ul>
<a name="robotObject">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>robotObject</h4>
<pre>protected&nbsp;<a href="../../robocode/control/RobotTestBed.html" title="type parameter in RobotTestBed">R</a> extends <a href="../../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a> robotObject</pre>
<div class="block">Instance of tested robot</div>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RobotTestBed--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RobotTestBed</h4>
<pre>public&nbsp;RobotTestBed()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setPositionDumping-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPositionDumping</h4>
<pre>public static&nbsp;void&nbsp;setPositionDumping(boolean&nbsp;value)</pre>
<div class="block">Thread-safe method to modify the position dumping flag.
 Use this instead of directly modifying the isDumpingPositions field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The new value for the flag</dd>
</dl>
</li>
</ul>
<a name="setTurnDumping-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnDumping</h4>
<pre>public static&nbsp;void&nbsp;setTurnDumping(boolean&nbsp;value)</pre>
<div class="block">Thread-safe method to modify the turn dumping flag.
 Use this instead of directly modifying the isDumpingTurns field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The new value for the flag</dd>
</dl>
</li>
</ul>
<a name="setOutputDumping-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutputDumping</h4>
<pre>public static&nbsp;void&nbsp;setOutputDumping(boolean&nbsp;value)</pre>
<div class="block">Thread-safe method to modify the output dumping flag.
 Use this instead of directly modifying the isDumpingOutput field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The new value for the flag</dd>
</dl>
</li>
</ul>
<a name="setErrorDumping-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setErrorDumping</h4>
<pre>public static&nbsp;void&nbsp;setErrorDumping(boolean&nbsp;value)</pre>
<div class="block">Thread-safe method to modify the error dumping flag.
 Use this instead of directly modifying the isDumpingErrors field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The new value for the flag</dd>
</dl>
</li>
</ul>
<a name="setMessageDumping-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMessageDumping</h4>
<pre>public static&nbsp;void&nbsp;setMessageDumping(boolean&nbsp;value)</pre>
<div class="block">Thread-safe method to modify the message dumping flag.
 Use this instead of directly modifying the isDumpingMessages field.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - The new value for the flag</dd>
</dl>
</li>
</ul>
<a name="isDumpingPositions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDumpingPositions</h4>
<pre>public static&nbsp;boolean&nbsp;isDumpingPositions()</pre>
<div class="block">Gets whether robot positions should be printed during each turn.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if position dumping is enabled</dd>
</dl>
</li>
</ul>
<a name="setDumpingPositions-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDumpingPositions</h4>
<pre>public static&nbsp;void&nbsp;setDumpingPositions(boolean&nbsp;dumpingPositions)</pre>
<div class="block">Sets whether robot positions should be printed during each turn.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dumpingPositions</code> - true to enable position dumping</dd>
</dl>
</li>
</ul>
<a name="isDumpingTurns--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDumpingTurns</h4>
<pre>public static&nbsp;boolean&nbsp;isDumpingTurns()</pre>
<div class="block">Gets whether each turn should be printed out.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if turn dumping is enabled</dd>
</dl>
</li>
</ul>
<a name="setDumpingTurns-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDumpingTurns</h4>
<pre>public static&nbsp;void&nbsp;setDumpingTurns(boolean&nbsp;dumpingTurns)</pre>
<div class="block">Sets whether each turn should be printed out.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dumpingTurns</code> - true to enable turn dumping</dd>
</dl>
</li>
</ul>
<a name="isDumpingOutput--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDumpingOutput</h4>
<pre>public static&nbsp;boolean&nbsp;isDumpingOutput()</pre>
<div class="block">Gets whether robot output should be printed out.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if output dumping is enabled</dd>
</dl>
</li>
</ul>
<a name="setDumpingOutput-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDumpingOutput</h4>
<pre>public static&nbsp;void&nbsp;setDumpingOutput(boolean&nbsp;dumpingOutput)</pre>
<div class="block">Sets whether robot output should be printed out.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dumpingOutput</code> - true to enable output dumping</dd>
</dl>
</li>
</ul>
<a name="isDumpingErrors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDumpingErrors</h4>
<pre>public static&nbsp;boolean&nbsp;isDumpingErrors()</pre>
<div class="block">Gets whether error messages should be printed out.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if error dumping is enabled</dd>
</dl>
</li>
</ul>
<a name="setDumpingErrors-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDumpingErrors</h4>
<pre>public static&nbsp;void&nbsp;setDumpingErrors(boolean&nbsp;dumpingErrors)</pre>
<div class="block">Sets whether error messages should be printed out.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dumpingErrors</code> - true to enable error dumping</dd>
</dl>
</li>
</ul>
<a name="isDumpingMessages--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDumpingMessages</h4>
<pre>public static&nbsp;boolean&nbsp;isDumpingMessages()</pre>
<div class="block">Gets whether robot messages should be printed out.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if message dumping is enabled</dd>
</dl>
</li>
</ul>
<a name="setDumpingMessages-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDumpingMessages</h4>
<pre>public static&nbsp;void&nbsp;setDumpingMessages(boolean&nbsp;dumpingMessages)</pre>
<div class="block">Sets whether robot messages should be printed out.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dumpingMessages</code> - true to enable message dumping</dd>
</dl>
</li>
</ul>
<a name="getRobotName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRobotName</h4>
<pre>public abstract&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRobotName()</pre>
<div class="block">Must return a fully qualified robot name to be in this battle.
 <p>
 You must override this event to specify the robot to battle in this test case.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>robot name.</dd>
</dl>
</li>
</ul>
<a name="getEnemyName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnemyName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEnemyName()</pre>
<div class="block">Must return a fully qualified enemy robot name to be in this battle.
 <p>
 You must override this event to specify the robot to battle in this test case.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>robot name.</dd>
</dl>
</li>
</ul>
<a name="getNumRounds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumRounds</h4>
<pre>public&nbsp;int&nbsp;getNumRounds()</pre>
<div class="block">Provides the number of rounds in this battle.  Defaults to 1.
 Override this to change the number of rounds.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of rounds.</dd>
</dl>
</li>
</ul>
<a name="getInitialPositions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInitialPositions</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getInitialPositions()</pre>
<div class="block">Returns a comma or space separated list like: x1,y1,heading1, x2,y2,heading2, which are the
 coordinates and heading of robot #1 and #2. So "0,0,180, 50,80,270" means that robot #1
 has position (0,0) and heading 180, and robot #2 has position (50,80) and heading 270.
 <p>
 Override this method to explicitly specify the initial positions.
 <p>
 Defaults to null, which means that the initial positions are determined randomly.  Since
 battles are deterministic by default, the initial positions are randomly chosen but will
 always be the same each time you run the test case.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The list of initial positions.</dd>
</dl>
</li>
</ul>
<a name="getExpectedRobotCount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpectedRobotCount</h4>
<pre>public&nbsp;int&nbsp;getExpectedRobotCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;robotList)</pre>
<div class="block">Provides the number of robots in this battle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>robotList</code> - The list of robots.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of robots in this battle.</dd>
</dl>
</li>
</ul>
<a name="isDeterministic--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeterministic</h4>
<pre>public&nbsp;boolean&nbsp;isDeterministic()</pre>
<div class="block">Defaults to true, indicating that the battle is deterministic and robots will always start
 in the same position each time.
 <p>
 Override to support random initialization.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>True if the battle will be deterministic.</dd>
</dl>
</li>
</ul>
<a name="isEnableRecording--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableRecording</h4>
<pre>public&nbsp;boolean&nbsp;isEnableRecording()</pre>
</li>
</ul>
<a name="isEnableScreenshots--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnableScreenshots</h4>
<pre>public&nbsp;boolean&nbsp;isEnableScreenshots()</pre>
</li>
</ul>
<a name="beforeInit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>beforeInit</h4>
<pre>protected&nbsp;void&nbsp;beforeInit()</pre>
</li>
</ul>
<a name="afterInit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>afterInit</h4>
<pre>protected&nbsp;void&nbsp;afterInit()</pre>
<div class="block">Called after the engine is initialized to perform additional setup.
 Use this method to configure engine properties after initialization.</div>
</li>
</ul>
<a name="before--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>before</h4>
<pre>protected&nbsp;void&nbsp;before()</pre>
<div class="block">The setup method run before each test, which sets up the listener on the engine for testing.
 Don't override this method; instead, override runSetup to add behavior before the test
 battle starts.</div>
</li>
</ul>
<a name="after--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>after</h4>
<pre>protected&nbsp;void&nbsp;after()</pre>
</li>
</ul>
<a name="cleanup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cleanup</h4>
<pre>public static&nbsp;void&nbsp;cleanup()</pre>
<div class="block">Releases any resources used by this test bed.
 <p>
 This method should be called when the test framework is being shut down to ensure
 proper cleanup of resources. It will close the Robocode engine and release all
 associated resources.
 <p>
 It is recommended to call this method in a finally block or shutdown hook to
 ensure resources are properly released even if tests fail.</div>
</li>
</ul>
<a name="run-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre>protected&nbsp;void&nbsp;run(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;robotName,
                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;enemyName)</pre>
</li>
</ul>
<a name="run-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre>protected&nbsp;void&nbsp;run(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;enemyName)</pre>
</li>
</ul>
<a name="run--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>run</h4>
<pre>protected&nbsp;void&nbsp;run()</pre>
</li>
</ul>
<a name="getExpectedErrors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getExpectedErrors</h4>
<pre>protected&nbsp;int&nbsp;getExpectedErrors()</pre>
</li>
</ul>
<a name="runSetup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runSetup</h4>
<pre>protected&nbsp;void&nbsp;runSetup()</pre>
</li>
</ul>
<a name="runTeardown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>runTeardown</h4>
<pre>protected&nbsp;void&nbsp;runTeardown()</pre>
</li>
</ul>
<a name="runBattle-java.lang.String-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>runBattle</h4>
<pre>protected&nbsp;void&nbsp;runBattle(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;robotList,
                         int&nbsp;numRounds,
                         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;initialPositions)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../robocode/control/RobotSpecification.html" title="class in robocode.control"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/control/RobotTestBed.html" target="_top">Frames</a></li>
<li><a href="RobotTestBed.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
