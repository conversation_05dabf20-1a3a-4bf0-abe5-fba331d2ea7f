<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode.control.events (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="robocode.control.events (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../robocode/control/snapshot/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/events/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;robocode.control.events</h1>
<div class="docSummary">
<div class="block">Battle events that occurs during a game, and which are used for the
 robocode.control.IBattleListener class.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></td>
<td class="colLast">
<div class="block">The listener interface for receiving "interesting" battle events from the game, e.g.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></td>
<td class="colLast">
<div class="block">An abstract adapter class for receiving battle events by implementing the <a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a></td>
<td class="colLast">
<div class="block">A BattleCompletedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>onBattleCompleted()</code></a> when the battle is completed successfully and results are available.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events">BattleErrorEvent</a></td>
<td class="colLast">
<div class="block">A BattleErrorEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattleError-robocode.control.events.BattleErrorEvent-"><code>onBattleError()</code></a> when an error message is sent from the game in the during the battle.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleEvent.html" title="class in robocode.control.events">BattleEvent</a></td>
<td class="colLast">
<div class="block">This is the base class of all battle events.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events">BattleFinishedEvent</a></td>
<td class="colLast">
<div class="block">A BattleFinishedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>onBattleFinished()</code></a> when the battle is finished.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events">BattleMessageEvent</a></td>
<td class="colLast">
<div class="block">A BattleMessageEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-"><code>onBattleMessage()</code></a> when an informal message is sent from the game in the during the battle.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events">BattlePausedEvent</a></td>
<td class="colLast">
<div class="block">A BattlePausedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattlePaused-robocode.control.events.BattlePausedEvent-"><code>onBattlePaused()</code></a> when a battle has been paused.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events">BattleResumedEvent</a></td>
<td class="colLast">
<div class="block">A BattleResumedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattleResumed-robocode.control.events.BattleResumedEvent-"><code>onBattleResumed()</code></a> when a battle has been resumed (after having been paused).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a></td>
<td class="colLast">
<div class="block">A BattleStartedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattleStarted-robocode.control.events.BattleStartedEvent-"><code>onBattleStarted()</code></a> when a new battle is started.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a></td>
<td class="colLast">
<div class="block">A RoundEndedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onRoundEnded-robocode.control.events.RoundEndedEvent-"><code>onRoundEnded()</code></a> when the current round of a battle has ended.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a></td>
<td class="colLast">
<div class="block">A RoundStartedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onRoundStarted-robocode.control.events.RoundStartedEvent-"><code>onRoundStarted()</code></a> when a new round in a battle is started.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events">TurnEndedEvent</a></td>
<td class="colLast">
<div class="block">A TurnEndedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onTurnEnded-robocode.control.events.TurnEndedEvent-"><code>onTurnEnded()</code></a> when the current turn in a battle round is ended.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events">TurnStartedEvent</a></td>
<td class="colLast">
<div class="block">A TurnStartedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onTurnStarted-robocode.control.events.TurnStartedEvent-"><code>onTurnStarted()</code></a> when a new turn in a battle round is started.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package robocode.control.events Description">Package robocode.control.events Description</h2>
<div class="block">Battle events that occurs during a game, and which are used for the
 robocode.control.IBattleListener class.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../robocode/control/snapshot/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/events/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
