<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>robocode.control.events (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../robocode/control/events/package-summary.html" target="classFrame">robocode.control.events</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="IBattleListener.html" title="interface in robocode.control.events" target="classFrame"><span class="interfaceName">IBattleListener</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="BattleAdaptor.html" title="class in robocode.control.events" target="classFrame">BattleAdaptor</a></li>
<li><a href="BattleCompletedEvent.html" title="class in robocode.control.events" target="classFrame">BattleCompletedEvent</a></li>
<li><a href="BattleErrorEvent.html" title="class in robocode.control.events" target="classFrame">BattleErrorEvent</a></li>
<li><a href="BattleEvent.html" title="class in robocode.control.events" target="classFrame">BattleEvent</a></li>
<li><a href="BattleFinishedEvent.html" title="class in robocode.control.events" target="classFrame">BattleFinishedEvent</a></li>
<li><a href="BattleMessageEvent.html" title="class in robocode.control.events" target="classFrame">BattleMessageEvent</a></li>
<li><a href="BattlePausedEvent.html" title="class in robocode.control.events" target="classFrame">BattlePausedEvent</a></li>
<li><a href="BattleResumedEvent.html" title="class in robocode.control.events" target="classFrame">BattleResumedEvent</a></li>
<li><a href="BattleStartedEvent.html" title="class in robocode.control.events" target="classFrame">BattleStartedEvent</a></li>
<li><a href="RoundEndedEvent.html" title="class in robocode.control.events" target="classFrame">RoundEndedEvent</a></li>
<li><a href="RoundStartedEvent.html" title="class in robocode.control.events" target="classFrame">RoundStartedEvent</a></li>
<li><a href="TurnEndedEvent.html" title="class in robocode.control.events" target="classFrame">TurnEndedEvent</a></li>
<li><a href="TurnStartedEvent.html" title="class in robocode.control.events" target="classFrame">TurnStartedEvent</a></li>
</ul>
</div>
</body>
</html>
