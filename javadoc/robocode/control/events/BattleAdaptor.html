<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BattleAdaptor (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BattleAdaptor (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/events/BattleAdaptor.html" target="_top">Frames</a></li>
<li><a href="BattleAdaptor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control.events</div>
<h2 title="Class BattleAdaptor" class="title">Class BattleAdaptor</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>robocode.control.events.BattleAdaptor</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">BattleAdaptor</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></pre>
<div class="block">An abstract adapter class for receiving battle events by implementing the <a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a>.
 The methods in this class are empty. This class exists as convenience for creating listener objects.
 <p>
 This is handy class to use when implementing the IBattleListener.
 It saves you from implementing empty handlers for battle events you are not interested in handling.
 <p>
 <b>Example:</b>
 <pre>
   private class BattleObserver extends BattleAdaptor {
       boolean isReplay;

       public void onBattleStarted(BattleStartedEvent event) {
           isReplay = event.isReplay();
       }

       public void onBattleCompleted(BattleCompletedEvent event) {
       if (!isReplay) {
           printResultsData(event);
       }
   }
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Flemming N. Larsen (original)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#BattleAdaptor--">BattleAdaptor</a></span>()</code>
<div class="block">Creates a BattleAdaptor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-">onBattleCompleted</a></span>(<a href="../../../robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the battle has completed successfully and results are available.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onBattleError-robocode.control.events.BattleErrorEvent-">onBattleError</a></span>(<a href="../../../robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events">BattleErrorEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the game has sent an error message.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-">onBattleFinished</a></span>(<a href="../../../robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events">BattleFinishedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the battle has finished.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onBattleMessage-robocode.control.events.BattleMessageEvent-">onBattleMessage</a></span>(<a href="../../../robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events">BattleMessageEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the game has sent a new information message.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onBattlePaused-robocode.control.events.BattlePausedEvent-">onBattlePaused</a></span>(<a href="../../../robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events">BattlePausedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the battle has been paused, either by the user or the game.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onBattleResumed-robocode.control.events.BattleResumedEvent-">onBattleResumed</a></span>(<a href="../../../robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events">BattleResumedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the battle has been resumed (after having been paused).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onBattleStarted-robocode.control.events.BattleStartedEvent-">onBattleStarted</a></span>(<a href="../../../robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a new battle has started.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onRoundEnded-robocode.control.events.RoundEndedEvent-">onRoundEnded</a></span>(<a href="../../../robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the current round of a battle has ended.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onRoundStarted-robocode.control.events.RoundStartedEvent-">onRoundStarted</a></span>(<a href="../../../robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a new round in a battle has started.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onTurnEnded-robocode.control.events.TurnEndedEvent-">onTurnEnded</a></span>(<a href="../../../robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events">TurnEndedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when the current turn in a battle round is ended.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleAdaptor.html#onTurnStarted-robocode.control.events.TurnStartedEvent-">onTurnStarted</a></span>(<a href="../../../robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events">TurnStartedEvent</a>&nbsp;event)</code>
<div class="block">This method is called when a new turn in a battle round has started.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BattleAdaptor--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BattleAdaptor</h4>
<pre>public&nbsp;BattleAdaptor()</pre>
<div class="block">Creates a BattleAdaptor.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="onBattleStarted-robocode.control.events.BattleStartedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBattleStarted</h4>
<pre>public&nbsp;void&nbsp;onBattleStarted(<a href="../../../robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a new battle has started.
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onBattleStarted-robocode.control.events.BattleStartedEvent-">onBattleStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events"><code>BattleStartedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>IBattleListener.onBattleCompleted(BattleCompletedEvent)</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>IBattleListener.onBattleFinished(BattleFinishedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onBattleFinished-robocode.control.events.BattleFinishedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBattleFinished</h4>
<pre>public&nbsp;void&nbsp;onBattleFinished(<a href="../../../robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events">BattleFinishedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the battle has finished. This event is always sent as the last battle event,
 both when the battle is completed successfully, terminated due to an error, or aborted by the user.
 Hence, this events is well-suited for cleanup after the battle. 
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-">onBattleFinished</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events"><code>BattleFinishedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleStarted-robocode.control.events.BattleStartedEvent-"><code>IBattleListener.onBattleStarted(BattleStartedEvent)</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>IBattleListener.onBattleCompleted(BattleCompletedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onBattleCompleted-robocode.control.events.BattleCompletedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBattleCompleted</h4>
<pre>public&nbsp;void&nbsp;onBattleCompleted(<a href="../../../robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the battle has completed successfully and results are available.
 This event will not occur if the battle is terminated or aborted by the user before the battle is completed. 
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-">onBattleCompleted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events"><code>BattleCompletedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleStarted-robocode.control.events.BattleStartedEvent-"><code>IBattleListener.onBattleStarted(BattleStartedEvent)</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>IBattleListener.onBattleFinished(BattleFinishedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onBattlePaused-robocode.control.events.BattlePausedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBattlePaused</h4>
<pre>public&nbsp;void&nbsp;onBattlePaused(<a href="../../../robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events">BattlePausedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the battle has been paused, either by the user or the game.  
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onBattlePaused-robocode.control.events.BattlePausedEvent-">onBattlePaused</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events"><code>BattlePausedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleResumed-robocode.control.events.BattleResumedEvent-"><code>IBattleListener.onBattleResumed(BattleResumedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onBattleResumed-robocode.control.events.BattleResumedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBattleResumed</h4>
<pre>public&nbsp;void&nbsp;onBattleResumed(<a href="../../../robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events">BattleResumedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the battle has been resumed (after having been paused).  
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onBattleResumed-robocode.control.events.BattleResumedEvent-">onBattleResumed</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events"><code>BattleResumedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattlePaused-robocode.control.events.BattlePausedEvent-"><code>IBattleListener.onBattlePaused(BattlePausedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onRoundStarted-robocode.control.events.RoundStartedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onRoundStarted</h4>
<pre>public&nbsp;void&nbsp;onRoundStarted(<a href="../../../robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a new round in a battle has started.
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onRoundStarted-robocode.control.events.RoundStartedEvent-">onRoundStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events"><code>RoundEndedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onRoundEnded-robocode.control.events.RoundEndedEvent-"><code>IBattleListener.onRoundEnded(RoundEndedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onRoundEnded-robocode.control.events.RoundEndedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onRoundEnded</h4>
<pre>public&nbsp;void&nbsp;onRoundEnded(<a href="../../../robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the current round of a battle has ended. 
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onRoundEnded-robocode.control.events.RoundEndedEvent-">onRoundEnded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events"><code>RoundEndedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onRoundStarted-robocode.control.events.RoundStartedEvent-"><code>IBattleListener.onRoundStarted(RoundStartedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onTurnStarted-robocode.control.events.TurnStartedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onTurnStarted</h4>
<pre>public&nbsp;void&nbsp;onTurnStarted(<a href="../../../robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events">TurnStartedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when a new turn in a battle round has started. 
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onTurnStarted-robocode.control.events.TurnStartedEvent-">onTurnStarted</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events"><code>TurnStartedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onTurnEnded-robocode.control.events.TurnEndedEvent-"><code>IBattleListener.onTurnEnded(TurnEndedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onTurnEnded-robocode.control.events.TurnEndedEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onTurnEnded</h4>
<pre>public&nbsp;void&nbsp;onTurnEnded(<a href="../../../robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events">TurnEndedEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the current turn in a battle round is ended. 
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onTurnEnded-robocode.control.events.TurnEndedEvent-">onTurnEnded</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events"><code>TurnEndedEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onTurnStarted-robocode.control.events.TurnStartedEvent-"><code>IBattleListener.onTurnStarted(TurnStartedEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onBattleMessage-robocode.control.events.BattleMessageEvent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onBattleMessage</h4>
<pre>public&nbsp;void&nbsp;onBattleMessage(<a href="../../../robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events">BattleMessageEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the game has sent a new information message.
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-">onBattleMessage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events"><code>BattleMessageEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleError-robocode.control.events.BattleErrorEvent-"><code>IBattleListener.onBattleError(BattleErrorEvent)</code></a></dd>
</dl>
</li>
</ul>
<a name="onBattleError-robocode.control.events.BattleErrorEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onBattleError</h4>
<pre>public&nbsp;void&nbsp;onBattleError(<a href="../../../robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events">BattleErrorEvent</a>&nbsp;event)</pre>
<div class="block">This method is called when the game has sent an error message.
 <p>
 You must override this method in order to get informed about this event and receive the event details.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../robocode/control/events/IBattleListener.html#onBattleError-robocode.control.events.BattleErrorEvent-">onBattleError</a></code>&nbsp;in interface&nbsp;<code><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>event</code> - the event details.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events"><code>BattleErrorEvent</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-"><code>IBattleListener.onBattleMessage(BattleMessageEvent)</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/events/BattleAdaptor.html" target="_top">Frames</a></li>
<li><a href="BattleAdaptor.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
