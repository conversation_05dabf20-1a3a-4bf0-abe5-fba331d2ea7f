<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BattleCompletedEvent (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BattleCompletedEvent (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/events/BattleCompletedEvent.html" target="_top">Frames</a></li>
<li><a href="BattleCompletedEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.control.events</div>
<h2 title="Class BattleCompletedEvent" class="title">Class BattleCompletedEvent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../robocode/control/events/BattleEvent.html" title="class in robocode.control.events">robocode.control.events.BattleEvent</a></li>
<li>
<ul class="inheritance">
<li>robocode.control.events.BattleCompletedEvent</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">BattleCompletedEvent</span>
extends <a href="../../../robocode/control/events/BattleEvent.html" title="class in robocode.control.events">BattleEvent</a></pre>
<div class="block">A BattleCompletedEvent is sent to <a href="../../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>onBattleCompleted()</code></a> when the battle is completed successfully and results are available. This event
 will not occur if the battle is terminated or aborted by the user before the battle is completed.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Pavel Savara (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a>, 
<a href="../../../robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>IBattleListener.onBattleCompleted(BattleCompletedEvent)</code></a>, 
<a href="../../../robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events"><code>BattleStartedEvent</code></a>, 
<a href="../../../robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events"><code>BattleFinishedEvent</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleCompletedEvent.html#BattleCompletedEvent-robocode.BattleRules-robocode.BattleResults:A-">BattleCompletedEvent</a></span>(<a href="../../../robocode/BattleRules.html" title="class in robocode">BattleRules</a>&nbsp;battleRules,
                    <a href="../../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]&nbsp;results)</code>
<div class="block">Called by the game to create a new BattleCompletedEvent.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/BattleRules.html" title="class in robocode">BattleRules</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleCompletedEvent.html#getBattleRules--">getBattleRules</a></span>()</code>
<div class="block">Returns the rules used in the battle.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleCompletedEvent.html#getIndexedResults--">getIndexedResults</a></span>()</code>
<div class="block">Returns the battle results that can be used to determine the score for the individual robot based
 on the robot index.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../robocode/control/events/BattleCompletedEvent.html#getSortedResults--">getSortedResults</a></span>()</code>
<div class="block">Returns the battle results sorted on score.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BattleCompletedEvent-robocode.BattleRules-robocode.BattleResults:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BattleCompletedEvent</h4>
<pre>public&nbsp;BattleCompletedEvent(<a href="../../../robocode/BattleRules.html" title="class in robocode">BattleRules</a>&nbsp;battleRules,
                            <a href="../../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]&nbsp;results)</pre>
<div class="block">Called by the game to create a new BattleCompletedEvent.
 Please don't use this constructor as it might change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>battleRules</code> - the rules that was used in the battle.</dd>
<dd><code>results</code> - the indexed results of the battle. These are unsorted, but using robot indexes.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getBattleRules--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBattleRules</h4>
<pre>public&nbsp;<a href="../../../robocode/BattleRules.html" title="class in robocode">BattleRules</a>&nbsp;getBattleRules()</pre>
<div class="block">Returns the rules used in the battle.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the rules used in the battle.</dd>
</dl>
</li>
</ul>
<a name="getSortedResults--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSortedResults</h4>
<pre>public&nbsp;<a href="../../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]&nbsp;getSortedResults()</pre>
<div class="block">Returns the battle results sorted on score.
 Note that the robot index cannot be used to determine the score with the sorted results.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of sorted BattleResults, where the results with the bigger score are placed first in the list.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleCompletedEvent.html#getIndexedResults--"><code>getIndexedResults()</code></a></dd>
</dl>
</li>
</ul>
<a name="getIndexedResults--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getIndexedResults</h4>
<pre>public&nbsp;<a href="../../../robocode/BattleResults.html" title="class in robocode">BattleResults</a>[]&nbsp;getIndexedResults()</pre>
<div class="block">Returns the battle results that can be used to determine the score for the individual robot based
 on the robot index.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of indexed BattleResults, where each index matches an index of a specific robot.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../robocode/control/events/BattleCompletedEvent.html#getSortedResults--"><code>getSortedResults()</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?robocode/control/events/BattleCompletedEvent.html" target="_top">Frames</a></li>
<li><a href="BattleCompletedEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
