<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>_AdvancedRadiansRobot (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="_AdvancedRadiansRobot (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../robocode/_AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/_AdvancedRadiansRobot.html" target="_top">Frames</a></li>
<li><a href="_AdvancedRadiansRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class _AdvancedRadiansRobot" class="title">Class _AdvancedRadiansRobot</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_RobotBase.html" title="class in robocode">robocode._RobotBase</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_Robot.html" title="class in robocode">robocode._Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/Robot.html" title="class in robocode">robocode.Robot</a></li>
<li>
<ul class="inheritance">
<li><a href="../robocode/_AdvancedRobot.html" title="class in robocode">robocode._AdvancedRobot</a></li>
<li>
<ul class="inheritance">
<li>robocode._AdvancedRadiansRobot</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true" title="class or interface in java.lang">Runnable</a>, <a href="../robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a>, <a href="../robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a>, <a href="../robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>, <a href="../robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a>, <a href="../robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a>, <a href="../robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a>, <a href="../robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">_AdvancedRadiansRobot</span>
extends <a href="../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></pre>
<div class="block">This class is used by the system as a placeholder for all *Radians calls in
 <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>. You may refer to this class for documentation only.
 <p>
 You should create a <a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> instead.
 <p>
 (The Radians methods themselves will continue work, however).</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor), Pavel Savara (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/Robot.html" title="class in robocode"><code>Robot</code></a>, 
<a href="../robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>, 
<a href="../robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, 
<a href="../robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>, 
<a href="../robocode/RateControlRobot.html" title="class in robocode"><code>RateControlRobot</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#out">out</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#getGunHeadingRadians--">getGunHeadingRadians</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#getHeadingRadians--">getHeadingRadians</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#getRadarHeadingRadians--">getRadarHeadingRadians</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#getTurnRemainingRadians--">getTurnRemainingRadians</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#setTurnGunLeftRadians-double-">setTurnGunLeftRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#setTurnGunRightRadians-double-">setTurnGunRightRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#setTurnLeftRadians-double-">setTurnLeftRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#setTurnRadarLeftRadians-double-">setTurnRadarLeftRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#setTurnRadarRightRadians-double-">setTurnRadarRightRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#setTurnRightRadians-double-">setTurnRightRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#turnGunLeftRadians-double-">turnGunLeftRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#turnGunRightRadians-double-">turnGunRightRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#turnLeftRadians-double-">turnLeftRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#turnRadarLeftRadians-double-">turnRadarLeftRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#turnRadarRightRadians-double-">turnRadarRightRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../robocode/_AdvancedRadiansRobot.html#turnRightRadians-double-">turnRightRadians</a></span>(double&nbsp;radians)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._AdvancedRobot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></h3>
<code><a href="../robocode/_AdvancedRobot.html#endTurn--">endTurn</a>, <a href="../robocode/_AdvancedRobot.html#getGunHeadingDegrees--">getGunHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getHeadingDegrees--">getHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getMaxWaitCount--">getMaxWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#getRadarHeadingDegrees--">getRadarHeadingDegrees</a>, <a href="../robocode/_AdvancedRobot.html#getWaitCount--">getWaitCount</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunLeftDegrees-double-">setTurnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnGunRightDegrees-double-">setTurnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnLeftDegrees-double-">setTurnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarLeftDegrees-double-">setTurnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRadarRightDegrees-double-">setTurnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#setTurnRightDegrees-double-">setTurnRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunLeftDegrees-double-">turnGunLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnGunRightDegrees-double-">turnGunRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnLeftDegrees-double-">turnLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarLeftDegrees-double-">turnRadarLeftDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRadarRightDegrees-double-">turnRadarRightDegrees</a>, <a href="../robocode/_AdvancedRobot.html#turnRightDegrees-double-">turnRightDegrees</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/Robot.html" title="class in robocode">Robot</a></h3>
<code><a href="../robocode/Robot.html#ahead-double-">ahead</a>, <a href="../robocode/Robot.html#back-double-">back</a>, <a href="../robocode/Robot.html#doNothing--">doNothing</a>, <a href="../robocode/Robot.html#fire-double-">fire</a>, <a href="../robocode/Robot.html#fireBullet-double-">fireBullet</a>, <a href="../robocode/Robot.html#getBasicEventListener--">getBasicEventListener</a>, <a href="../robocode/Robot.html#getBattleFieldHeight--">getBattleFieldHeight</a>, <a href="../robocode/Robot.html#getBattleFieldWidth--">getBattleFieldWidth</a>, <a href="../robocode/Robot.html#getEnergy--">getEnergy</a>, <a href="../robocode/Robot.html#getGraphics--">getGraphics</a>, <a href="../robocode/Robot.html#getGunCoolingRate--">getGunCoolingRate</a>, <a href="../robocode/Robot.html#getGunHeading--">getGunHeading</a>, <a href="../robocode/Robot.html#getGunHeat--">getGunHeat</a>, <a href="../robocode/Robot.html#getHeading--">getHeading</a>, <a href="../robocode/Robot.html#getHeight--">getHeight</a>, <a href="../robocode/Robot.html#getInteractiveEventListener--">getInteractiveEventListener</a>, <a href="../robocode/Robot.html#getName--">getName</a>, <a href="../robocode/Robot.html#getNumRounds--">getNumRounds</a>, <a href="../robocode/Robot.html#getNumSentries--">getNumSentries</a>, <a href="../robocode/Robot.html#getOthers--">getOthers</a>, <a href="../robocode/Robot.html#getPaintEventListener--">getPaintEventListener</a>, <a href="../robocode/Robot.html#getRadarHeading--">getRadarHeading</a>, <a href="../robocode/Robot.html#getRobotRunnable--">getRobotRunnable</a>, <a href="../robocode/Robot.html#getRoundNum--">getRoundNum</a>, <a href="../robocode/Robot.html#getSentryBorderSize--">getSentryBorderSize</a>, <a href="../robocode/Robot.html#getTime--">getTime</a>, <a href="../robocode/Robot.html#getVelocity--">getVelocity</a>, <a href="../robocode/Robot.html#getWidth--">getWidth</a>, <a href="../robocode/Robot.html#getX--">getX</a>, <a href="../robocode/Robot.html#getY--">getY</a>, <a href="../robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded</a>, <a href="../robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit</a>, <a href="../robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet</a>, <a href="../robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed</a>, <a href="../robocode/Robot.html#onDeath-robocode.DeathEvent-">onDeath</a>, <a href="../robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet</a>, <a href="../robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot</a>, <a href="../robocode/Robot.html#onHitWall-robocode.HitWallEvent-">onHitWall</a>, <a href="../robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed</a>, <a href="../robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased</a>, <a href="../robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped</a>, <a href="../robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked</a>, <a href="../robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged</a>, <a href="../robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered</a>, <a href="../robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited</a>, <a href="../robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved</a>, <a href="../robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed</a>, <a href="../robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased</a>, <a href="../robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved</a>, <a href="../robocode/Robot.html#onPaint-java.awt.Graphics2D-">onPaint</a>, <a href="../robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath</a>, <a href="../robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded</a>, <a href="../robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot</a>, <a href="../robocode/Robot.html#onStatus-robocode.StatusEvent-">onStatus</a>, <a href="../robocode/Robot.html#onWin-robocode.WinEvent-">onWin</a>, <a href="../robocode/Robot.html#resume--">resume</a>, <a href="../robocode/Robot.html#run--">run</a>, <a href="../robocode/Robot.html#scan--">scan</a>, <a href="../robocode/Robot.html#setAdjustGunForRobotTurn-boolean-">setAdjustGunForRobotTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn</a>, <a href="../robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-">setAdjustRadarForRobotTurn</a>, <a href="../robocode/Robot.html#setAllColors-java.awt.Color-">setAllColors</a>, <a href="../robocode/Robot.html#setBodyColor-java.awt.Color-">setBodyColor</a>, <a href="../robocode/Robot.html#setBulletColor-java.awt.Color-">setBulletColor</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-">setColors</a>, <a href="../robocode/Robot.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty</a>, <a href="../robocode/Robot.html#setGunColor-java.awt.Color-">setGunColor</a>, <a href="../robocode/Robot.html#setRadarColor-java.awt.Color-">setRadarColor</a>, <a href="../robocode/Robot.html#setScanColor-java.awt.Color-">setScanColor</a>, <a href="../robocode/Robot.html#stop--">stop</a>, <a href="../robocode/Robot.html#stop-boolean-">stop</a>, <a href="../robocode/Robot.html#turnGunLeft-double-">turnGunLeft</a>, <a href="../robocode/Robot.html#turnGunRight-double-">turnGunRight</a>, <a href="../robocode/Robot.html#turnLeft-double-">turnLeft</a>, <a href="../robocode/Robot.html#turnRadarLeft-double-">turnRadarLeft</a>, <a href="../robocode/Robot.html#turnRadarRight-double-">turnRadarRight</a>, <a href="../robocode/Robot.html#turnRight-double-">turnRight</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._Robot">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_Robot.html" title="class in robocode">_Robot</a></h3>
<code><a href="../robocode/_Robot.html#getBattleNum--">getBattleNum</a>, <a href="../robocode/_Robot.html#getGunCharge--">getGunCharge</a>, <a href="../robocode/_Robot.html#getGunImageName--">getGunImageName</a>, <a href="../robocode/_Robot.html#getLife--">getLife</a>, <a href="../robocode/_Robot.html#getNumBattles--">getNumBattles</a>, <a href="../robocode/_Robot.html#getRadarImageName--">getRadarImageName</a>, <a href="../robocode/_Robot.html#getRobotImageName--">getRobotImageName</a>, <a href="../robocode/_Robot.html#setGunImageName-java.lang.String-">setGunImageName</a>, <a href="../robocode/_Robot.html#setInterruptible-boolean-">setInterruptible</a>, <a href="../robocode/_Robot.html#setRadarImageName-java.lang.String-">setRadarImageName</a>, <a href="../robocode/_Robot.html#setRobotImageName-java.lang.String-">setRobotImageName</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode._RobotBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;robocode.<a href="../robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></h3>
<code><a href="../robocode/_RobotBase.html#finalize--">finalize</a>, <a href="../robocode/_RobotBase.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/_RobotBase.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a>, <a href="../robocode/_RobotBase.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.robocode.robotinterfaces.IBasicRobot">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;robocode.robotinterfaces.<a href="../robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></h3>
<code><a href="../robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut</a>, <a href="../robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getHeadingRadians()</pre>
</li>
</ul>
<a name="setTurnLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnLeftRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnLeftRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="setTurnRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRightRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnRightRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="turnLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnLeftRadians</h4>
<pre>public&nbsp;void&nbsp;turnLeftRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="turnRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRightRadians</h4>
<pre>public&nbsp;void&nbsp;turnRightRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="getGunHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getGunHeadingRadians()</pre>
</li>
</ul>
<a name="getRadarHeadingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarHeadingRadians</h4>
<pre>public&nbsp;double&nbsp;getRadarHeadingRadians()</pre>
</li>
</ul>
<a name="setTurnGunLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnGunLeftRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnGunLeftRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="setTurnGunRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnGunRightRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnGunRightRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="setTurnRadarLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRadarLeftRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnRadarLeftRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="setTurnRadarRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTurnRadarRightRadians</h4>
<pre>public&nbsp;void&nbsp;setTurnRadarRightRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="turnGunLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunLeftRadians</h4>
<pre>public&nbsp;void&nbsp;turnGunLeftRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="turnGunRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnGunRightRadians</h4>
<pre>public&nbsp;void&nbsp;turnGunRightRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="turnRadarLeftRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRadarLeftRadians</h4>
<pre>public&nbsp;void&nbsp;turnRadarLeftRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="turnRadarRightRadians-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>turnRadarRightRadians</h4>
<pre>public&nbsp;void&nbsp;turnRadarRightRadians(double&nbsp;radians)</pre>
</li>
</ul>
<a name="getGunTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGunTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getGunTurnRemainingRadians()</pre>
</li>
</ul>
<a name="getRadarTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRadarTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getRadarTurnRemainingRadians()</pre>
</li>
</ul>
<a name="getTurnRemainingRadians--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTurnRemainingRadians</h4>
<pre>public&nbsp;double&nbsp;getTurnRemainingRadians()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../robocode/_AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/_AdvancedRadiansRobot.html" target="_top">Frames</a></li>
<li><a href="_AdvancedRadiansRobot.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.robocode._RobotBase">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
