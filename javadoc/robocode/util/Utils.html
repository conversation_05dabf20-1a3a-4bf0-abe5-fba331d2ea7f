<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Utils (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Utils (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/util/Utils.html" target="_top">Frames</a></li>
<li><a href="Utils.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode.util</div>
<h2 title="Class Utils" class="title">Class Utils</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>robocode.util.Utils</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Utils</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Utility class that provide methods for normalizing angles.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#NEAR_DELTA">NEAR_DELTA</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#angleToApproximateDirection-double-">angleToApproximateDirection</a></span>(double&nbsp;angle)</code>
<div class="block">Returns approximate cardinal direction for absolute angle in radians, like N,NE,E,SE,S,SW,W,NW</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#assertEquals-java.lang.String-java.lang.Object-java.lang.Object-">assertEquals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;expected,
            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;actual)</code>
<div class="block">Throws AssertionError when the params expected and actual do not equal each other.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#assertNear-java.lang.String-double-double-">assertNear</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
          double&nbsp;expected,
          double&nbsp;actual)</code>
<div class="block">Throws AssertionError when the params expected and actual do not within .00001 difference.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#assertNotNull-java.lang.String-java.lang.Object-">assertNotNull</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</code>
<div class="block">Throws AssertionError when the param value is null.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#assertTrue-java.lang.String-boolean-">assertTrue</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
          boolean&nbsp;assertion)</code>
<div class="block">Throws AssertionError when the assertion is false.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Random.html?is-external=true" title="class or interface in java.util">Random</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#getRandom--">getRandom</a></span>()</code>
<div class="block">Returns random number generator.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#isNear-double-double-">isNear</a></span>(double&nbsp;value1,
      double&nbsp;value2)</code>
<div class="block">Tests if the two <code>double</code> values are near to each other.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#normalAbsoluteAngle-double-">normalAbsoluteAngle</a></span>(double&nbsp;angle)</code>
<div class="block">Normalizes an angle to an absolute angle.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#normalAbsoluteAngleDegrees-double-">normalAbsoluteAngleDegrees</a></span>(double&nbsp;angle)</code>
<div class="block">Normalizes an angle to an absolute angle.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#normalNearAbsoluteAngle-double-">normalNearAbsoluteAngle</a></span>(double&nbsp;angle)</code>
<div class="block">Normalizes an angle to be near an absolute angle.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#normalNearAbsoluteAngleDegrees-double-">normalNearAbsoluteAngleDegrees</a></span>(double&nbsp;angle)</code>
<div class="block">Normalizes an angle to be near an absolute angle.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#normalRelativeAngle-double-">normalRelativeAngle</a></span>(double&nbsp;angle)</code>
<div class="block">Normalizes an angle to a relative angle.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../robocode/util/Utils.html#normalRelativeAngleDegrees-double-">normalRelativeAngleDegrees</a></span>(double&nbsp;angle)</code>
<div class="block">Normalizes an angle to a relative angle.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NEAR_DELTA">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NEAR_DELTA</h4>
<pre>public static final&nbsp;double NEAR_DELTA</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#robocode.util.Utils.NEAR_DELTA">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="normalAbsoluteAngle-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalAbsoluteAngle</h4>
<pre>public static&nbsp;double&nbsp;normalAbsoluteAngle(double&nbsp;angle)</pre>
<div class="block">Normalizes an angle to an absolute angle.
 The normalized angle will be in the range from 0 to 2*PI, where 2*PI
 itself is not included.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to normalize</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the normalized angle that will be in the range of [0,2*PI[</dd>
</dl>
</li>
</ul>
<a name="normalAbsoluteAngleDegrees-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalAbsoluteAngleDegrees</h4>
<pre>public static&nbsp;double&nbsp;normalAbsoluteAngleDegrees(double&nbsp;angle)</pre>
<div class="block">Normalizes an angle to an absolute angle.
 The normalized angle will be in the range from 0 to 360, where 360
 itself is not included.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to normalize</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the normalized angle that will be in the range of [0,360[</dd>
</dl>
</li>
</ul>
<a name="normalRelativeAngle-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalRelativeAngle</h4>
<pre>public static&nbsp;double&nbsp;normalRelativeAngle(double&nbsp;angle)</pre>
<div class="block">Normalizes an angle to a relative angle.
 The normalized angle will be in the range from -PI to PI, where PI
 itself is not included.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to normalize</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the normalized angle that will be in the range of [-PI,PI[</dd>
</dl>
</li>
</ul>
<a name="normalRelativeAngleDegrees-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalRelativeAngleDegrees</h4>
<pre>public static&nbsp;double&nbsp;normalRelativeAngleDegrees(double&nbsp;angle)</pre>
<div class="block">Normalizes an angle to a relative angle.
 The normalized angle will be in the range from -180 to 180, where 180
 itself is not included.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to normalize</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the normalized angle that will be in the range of [-180,180[</dd>
</dl>
</li>
</ul>
<a name="normalNearAbsoluteAngleDegrees-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalNearAbsoluteAngleDegrees</h4>
<pre>public static&nbsp;double&nbsp;normalNearAbsoluteAngleDegrees(double&nbsp;angle)</pre>
<div class="block">Normalizes an angle to be near an absolute angle.
 The normalized angle will be in the range from 0 to 360, where 360
 itself is not included.
 If the normalized angle is near to 0, 90, 180, 270 or 360, that
 angle will be returned. The <a href="../../robocode/util/Utils.html#isNear-double-double-"><code>isNear</code></a>
 method is used for defining when the angle is near one of angles listed
 above.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to normalize</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the normalized angle that will be in the range of [0,360[</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/util/Utils.html#normalAbsoluteAngle-double-"><code>normalAbsoluteAngle(double)</code></a>, 
<a href="../../robocode/util/Utils.html#isNear-double-double-"><code>isNear(double, double)</code></a></dd>
</dl>
</li>
</ul>
<a name="normalNearAbsoluteAngle-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>normalNearAbsoluteAngle</h4>
<pre>public static&nbsp;double&nbsp;normalNearAbsoluteAngle(double&nbsp;angle)</pre>
<div class="block">Normalizes an angle to be near an absolute angle.
 The normalized angle will be in the range from 0 to 2*PI, where 2*PI
 itself is not included.
 If the normalized angle is near to 0, PI/2, PI, 3*PI/2 or 2*PI, that
 angle will be returned. The <a href="../../robocode/util/Utils.html#isNear-double-double-"><code>isNear</code></a>
 method is used for defining when the angle is near one of angles listed
 above.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - the angle to normalize</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the normalized angle that will be in the range of [0,2*PI[</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../robocode/util/Utils.html#normalAbsoluteAngle-double-"><code>normalAbsoluteAngle(double)</code></a>, 
<a href="../../robocode/util/Utils.html#isNear-double-double-"><code>isNear(double, double)</code></a></dd>
</dl>
</li>
</ul>
<a name="isNear-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNear</h4>
<pre>public static&nbsp;boolean&nbsp;isNear(double&nbsp;value1,
                             double&nbsp;value2)</pre>
<div class="block">Tests if the two <code>double</code> values are near to each other.
 It is recommended to use this method instead of testing if the two
 doubles are equal using an this expression: <code>value1 == value2</code>.
 The reason being, that this expression might never become
 <code>true</code> due to the precision of double values.
 Whether or not the specified doubles are near to each other is defined by
 the following expression:
 <code>(Math.abs(value1 - value2) &amp;lt; .00001)</code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value1</code> - the first double value</dd>
<dd><code>value2</code> - the second double value</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if the two doubles are near to each other;
         <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="getRandom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRandom</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Random.html?is-external=true" title="class or interface in java.util">Random</a>&nbsp;getRandom()</pre>
<div class="block">Returns random number generator. It might be configured for repeatable behavior by setting -DRANDOMSEED option.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>random number generator</dd>
</dl>
</li>
</ul>
<a name="assertNotNull-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assertNotNull</h4>
<pre>public static&nbsp;void&nbsp;assertNotNull(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;value)</pre>
<div class="block">Throws AssertionError when the param value is null. It could be used to express validation of invariant.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - of the eventual error</dd>
<dd><code>value</code> - tested value</dd>
</dl>
</li>
</ul>
<a name="assertEquals-java.lang.String-java.lang.Object-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assertEquals</h4>
<pre>public static&nbsp;void&nbsp;assertEquals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;expected,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;actual)</pre>
<div class="block">Throws AssertionError when the params expected and actual do not equal each other. It could be used to express validation of invariant.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - of the eventual error</dd>
<dd><code>expected</code> - expected value</dd>
<dd><code>actual</code> - tested value</dd>
</dl>
</li>
</ul>
<a name="assertTrue-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assertTrue</h4>
<pre>public static&nbsp;void&nbsp;assertTrue(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
                              boolean&nbsp;assertion)</pre>
<div class="block">Throws AssertionError when the assertion is false. It could be used to express validation of invariant.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - of the eventual error</dd>
<dd><code>assertion</code> - expected to be true</dd>
</dl>
</li>
</ul>
<a name="assertNear-java.lang.String-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assertNear</h4>
<pre>public static&nbsp;void&nbsp;assertNear(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
                              double&nbsp;expected,
                              double&nbsp;actual)</pre>
<div class="block">Throws AssertionError when the params expected and actual do not within .00001 difference. It could be used to express validation of invariant.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - of the eventual error</dd>
<dd><code>expected</code> - expected value</dd>
<dd><code>actual</code> - tested value</dd>
</dl>
</li>
</ul>
<a name="angleToApproximateDirection-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>angleToApproximateDirection</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;angleToApproximateDirection(double&nbsp;angle)</pre>
<div class="block">Returns approximate cardinal direction for absolute angle in radians, like N,NE,E,SE,S,SW,W,NW</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>angle</code> - absolute angle in radians</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>N,NE,E,SE,S,SW,W,NW</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../index.html?robocode/util/Utils.html" target="_top">Frames</a></li>
<li><a href="Utils.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
