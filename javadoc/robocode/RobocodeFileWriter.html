<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RobocodeFileWriter (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RobocodeFileWriter (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/Robot.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/RobocodeFileWriter.html" target="_top">Frames</a></li>
<li><a href="RobocodeFileWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.Writer">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.io.OutputStreamWriter">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">robocode</div>
<h2 title="Class RobocodeFileWriter" class="title">Class RobocodeFileWriter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true" title="class or interface in java.io">java.io.Writer</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true" title="class or interface in java.io">java.io.OutputStreamWriter</a></li>
<li>
<ul class="inheritance">
<li>robocode.RobocodeFileWriter</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Closeable.html?is-external=true" title="class or interface in java.io">Closeable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Flushable.html?is-external=true" title="class or interface in java.io">Flushable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Appendable.html?is-external=true" title="class or interface in java.lang">Appendable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html?is-external=true" title="class or interface in java.lang">AutoCloseable</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RobocodeFileWriter</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true" title="class or interface in java.io">OutputStreamWriter</a></pre>
<div class="block">RobocodeFileWriter is similar to a <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true" title="class or interface in java.io"><code>FileWriter</code></a> and is used for
 writing data out to a file, which you got by calling <a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>getDataFile()</code></a>.
 <p>
 You should read <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true" title="class or interface in java.io"><code>FileWriter</code></a> for documentation of this class.
 <p>
 Please notice that the max. size of your data file is set to 200000
 (~195 KB).</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Mathew A. Nelson (original), Flemming N. Larsen (contributor)</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>AdvancedRobot.getDataFile(String)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true" title="class or interface in java.io"><code>FileWriter</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true" title="class or interface in java.io">Writer</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true#lock" title="class or interface in java.io">lock</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.io.File-">RobocodeFileWriter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.io.FileDescriptor-">RobocodeFileWriter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileDescriptor.html?is-external=true" title="class or interface in java.io">FileDescriptor</a>&nbsp;fd)</code>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.lang.String-">RobocodeFileWriter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.lang.String-boolean-">RobocodeFileWriter</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
                  boolean&nbsp;append)</code>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.OutputStreamWriter">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true" title="class or interface in java.io">OutputStreamWriter</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true#close--" title="class or interface in java.io">close</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true#flush--" title="class or interface in java.io">flush</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true#getEncoding--" title="class or interface in java.io">getEncoding</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true#write-char:A-int-int-" title="class or interface in java.io">write</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true#write-int-" title="class or interface in java.io">write</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/OutputStreamWriter.html?is-external=true#write-java.lang.String-int-int-" title="class or interface in java.io">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true" title="class or interface in java.io">Writer</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true#append-char-" title="class or interface in java.io">append</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true#append-java.lang.CharSequence-" title="class or interface in java.io">append</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true#append-java.lang.CharSequence-int-int-" title="class or interface in java.io">append</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true#write-char:A-" title="class or interface in java.io">write</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/io/Writer.html?is-external=true#write-java.lang.String-" title="class or interface in java.io">write</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RobocodeFileWriter-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RobocodeFileWriter</h4>
<pre>public&nbsp;RobocodeFileWriter(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Constructs a new RobocodeFileWriter.
 See <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.io.File-" title="class or interface in java.io"><code>FileWriter.FileWriter(File)</code></a> for documentation about
 this constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - the file to write to.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - if an I/O exception occurs.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.io.File-" title="class or interface in java.io"><code>FileWriter.FileWriter(File)</code></a></dd>
</dl>
</li>
</ul>
<a name="RobocodeFileWriter-java.io.FileDescriptor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RobocodeFileWriter</h4>
<pre>public&nbsp;RobocodeFileWriter(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileDescriptor.html?is-external=true" title="class or interface in java.io">FileDescriptor</a>&nbsp;fd)</pre>
<div class="block">Constructs a new RobocodeFileWriter.
 See <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.io.FileDescriptor-" title="class or interface in java.io"><code>FileWriter.FileWriter(FileDescriptor)</code></a> for
 documentation about this constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fd</code> - the file descriptor of the file to write to.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.io.FileDescriptor-" title="class or interface in java.io"><code>FileWriter.FileWriter(FileDescriptor)</code></a></dd>
</dl>
</li>
</ul>
<a name="RobocodeFileWriter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RobocodeFileWriter</h4>
<pre>public&nbsp;RobocodeFileWriter(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Constructs a new RobocodeFileWriter.
 See <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.lang.String-" title="class or interface in java.io"><code>FileWriter.FileWriter(String)</code></a> for documentation about
 this constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - the filename of the file to write to.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - if an I/O exception occurs.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.lang.String-" title="class or interface in java.io"><code>FileWriter.FileWriter(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="RobocodeFileWriter-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RobocodeFileWriter</h4>
<pre>public&nbsp;RobocodeFileWriter(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
                          boolean&nbsp;append)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Constructs a new RobocodeFileWriter.
 See <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.lang.String-boolean-" title="class or interface in java.io"><code>FileWriter.FileWriter(String, boolean)</code></a> for
 documentation about this constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - the filename of the file to write to.</dd>
<dd><code>append</code> - set this to true if the output must be appended to the file.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - if an I/O exception occurs.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true#FileWriter-java.lang.String-boolean-" title="class or interface in java.io"><code>FileWriter.FileWriter(String, boolean)</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../deprecated-list.html">Deprecated</a></li>
<li><a href="../index-all.html">Index</a></li>
<li><a href="../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../robocode/RobocodeFileOutputStream.html" title="class in robocode"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../robocode/Robot.html" title="class in robocode"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?robocode/RobocodeFileWriter.html" target="_top">Frames</a></li>
<li><a href="RobocodeFileWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.Writer">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.java.io.OutputStreamWriter">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
