<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>All Classes (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<h1 class="bar">All&nbsp;Classes</h1>
<div class="indexContainer">
<ul>
<li><a href="robocode/AdvancedRobot.html" title="class in robocode" target="classFrame">AdvancedRobot</a></li>
<li><a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events" target="classFrame">BattleAdaptor</a></li>
<li><a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events" target="classFrame">BattleCompletedEvent</a></li>
<li><a href="robocode/BattleEndedEvent.html" title="class in robocode" target="classFrame">BattleEndedEvent</a></li>
<li><a href="robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events" target="classFrame">BattleErrorEvent</a></li>
<li><a href="robocode/control/events/BattleEvent.html" title="class in robocode.control.events" target="classFrame">BattleEvent</a></li>
<li><a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control" target="classFrame">BattlefieldSpecification</a></li>
<li><a href="robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events" target="classFrame">BattleFinishedEvent</a></li>
<li><a href="robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events" target="classFrame">BattleMessageEvent</a></li>
<li><a href="robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events" target="classFrame">BattlePausedEvent</a></li>
<li><a href="robocode/BattleResults.html" title="class in robocode" target="classFrame">BattleResults</a></li>
<li><a href="robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events" target="classFrame">BattleResumedEvent</a></li>
<li><a href="robocode/BattleRules.html" title="class in robocode" target="classFrame">BattleRules</a></li>
<li><a href="robocode/control/BattleSpecification.html" title="class in robocode.control" target="classFrame">BattleSpecification</a></li>
<li><a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events" target="classFrame">BattleStartedEvent</a></li>
<li><a href="robocode/BorderSentry.html" title="interface in robocode" target="classFrame"><span class="interfaceName">BorderSentry</span></a></li>
<li><a href="robocode/Bullet.html" title="class in robocode" target="classFrame">Bullet</a></li>
<li><a href="robocode/BulletHitBulletEvent.html" title="class in robocode" target="classFrame">BulletHitBulletEvent</a></li>
<li><a href="robocode/BulletHitEvent.html" title="class in robocode" target="classFrame">BulletHitEvent</a></li>
<li><a href="robocode/BulletMissedEvent.html" title="class in robocode" target="classFrame">BulletMissedEvent</a></li>
<li><a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot" target="classFrame">BulletState</a></li>
<li><a href="robocode/Condition.html" title="class in robocode" target="classFrame">Condition</a></li>
<li><a href="robocode/CustomEvent.html" title="class in robocode" target="classFrame">CustomEvent</a></li>
<li><a href="robocode/DeathEvent.html" title="class in robocode" target="classFrame">DeathEvent</a></li>
<li><a href="robocode/Droid.html" title="interface in robocode" target="classFrame"><span class="interfaceName">Droid</span></a></li>
<li><a href="robocode/Event.html" title="class in robocode" target="classFrame">Event</a></li>
<li><a href="robocode/GunTurnCompleteCondition.html" title="class in robocode" target="classFrame">GunTurnCompleteCondition</a></li>
<li><a href="robocode/HitByBulletEvent.html" title="class in robocode" target="classFrame">HitByBulletEvent</a></li>
<li><a href="robocode/HitRobotEvent.html" title="class in robocode" target="classFrame">HitRobotEvent</a></li>
<li><a href="robocode/HitWallEvent.html" title="class in robocode" target="classFrame">HitWallEvent</a></li>
<li><a href="robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IAdvancedEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IAdvancedRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IAdvancedRobotPeer</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IBasicEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IBasicEvents2</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IBasicEvents3</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IBasicRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IBasicRobotPeer</span></a></li>
<li><a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events" target="classFrame"><span class="interfaceName">IBattleListener</span></a></li>
<li><a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot" target="classFrame"><span class="interfaceName">IBulletSnapshot</span></a></li>
<li><a href="robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot" target="classFrame"><span class="interfaceName">IDebugProperty</span></a></li>
<li><a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IInteractiveEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IInteractiveRobot</span></a></li>
<li><a href="robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IJuniorRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IJuniorRobotPeer</span></a></li>
<li><a href="robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IPaintEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">IPaintRobot</span></a></li>
<li><a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control" target="classFrame"><span class="interfaceName">IRobocodeEngine</span></a></li>
<li><a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot" target="classFrame"><span class="interfaceName">IRobotSnapshot</span></a></li>
<li><a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot" target="classFrame"><span class="interfaceName">IScoreSnapshot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">IStandardRobotPeer</span></a></li>
<li><a href="robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">ITeamEvents</span></a></li>
<li><a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces" target="classFrame"><span class="interfaceName">ITeamRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer" target="classFrame"><span class="interfaceName">ITeamRobotPeer</span></a></li>
<li><a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot" target="classFrame"><span class="interfaceName">ITurnSnapshot</span></a></li>
<li><a href="robocode/JuniorRobot.html" title="class in robocode" target="classFrame">JuniorRobot</a></li>
<li><a href="robocode/KeyEvent.html" title="class in robocode" target="classFrame">KeyEvent</a></li>
<li><a href="robocode/KeyPressedEvent.html" title="class in robocode" target="classFrame">KeyPressedEvent</a></li>
<li><a href="robocode/KeyReleasedEvent.html" title="class in robocode" target="classFrame">KeyReleasedEvent</a></li>
<li><a href="robocode/KeyTypedEvent.html" title="class in robocode" target="classFrame">KeyTypedEvent</a></li>
<li><a href="robocode/MessageEvent.html" title="class in robocode" target="classFrame">MessageEvent</a></li>
<li><a href="robocode/MouseClickedEvent.html" title="class in robocode" target="classFrame">MouseClickedEvent</a></li>
<li><a href="robocode/MouseDraggedEvent.html" title="class in robocode" target="classFrame">MouseDraggedEvent</a></li>
<li><a href="robocode/MouseEnteredEvent.html" title="class in robocode" target="classFrame">MouseEnteredEvent</a></li>
<li><a href="robocode/MouseEvent.html" title="class in robocode" target="classFrame">MouseEvent</a></li>
<li><a href="robocode/MouseExitedEvent.html" title="class in robocode" target="classFrame">MouseExitedEvent</a></li>
<li><a href="robocode/MouseMovedEvent.html" title="class in robocode" target="classFrame">MouseMovedEvent</a></li>
<li><a href="robocode/MousePressedEvent.html" title="class in robocode" target="classFrame">MousePressedEvent</a></li>
<li><a href="robocode/MouseReleasedEvent.html" title="class in robocode" target="classFrame">MouseReleasedEvent</a></li>
<li><a href="robocode/MouseWheelMovedEvent.html" title="class in robocode" target="classFrame">MouseWheelMovedEvent</a></li>
<li><a href="robocode/MoveCompleteCondition.html" title="class in robocode" target="classFrame">MoveCompleteCondition</a></li>
<li><a href="robocode/PaintEvent.html" title="class in robocode" target="classFrame">PaintEvent</a></li>
<li><a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode" target="classFrame">RadarTurnCompleteCondition</a></li>
<li><a href="robocode/control/RandomFactory.html" title="class in robocode.control" target="classFrame">RandomFactory</a></li>
<li><a href="robocode/RateControlRobot.html" title="class in robocode" target="classFrame">RateControlRobot</a></li>
<li><a href="robocode/Robocode.html" title="class in robocode" target="classFrame">Robocode</a></li>
<li><a href="robocode/control/RobocodeEngine.html" title="class in robocode.control" target="classFrame">RobocodeEngine</a></li>
<li><a href="robocode/RobocodeFileOutputStream.html" title="class in robocode" target="classFrame">RobocodeFileOutputStream</a></li>
<li><a href="robocode/RobocodeFileWriter.html" title="class in robocode" target="classFrame">RobocodeFileWriter</a></li>
<li><a href="robocode/control/RobocodeListener.html" title="interface in robocode.control" target="classFrame"><span class="interfaceName">RobocodeListener</span></a></li>
<li><a href="robocode/Robot.html" title="class in robocode" target="classFrame">Robot</a></li>
<li><a href="robocode/RobotDeathEvent.html" title="class in robocode" target="classFrame">RobotDeathEvent</a></li>
<li><a href="robocode/control/RobotResults.html" title="class in robocode.control" target="classFrame">RobotResults</a></li>
<li><a href="robocode/control/RobotSetup.html" title="class in robocode.control" target="classFrame">RobotSetup</a></li>
<li><a href="robocode/control/RobotSpecification.html" title="class in robocode.control" target="classFrame">RobotSpecification</a></li>
<li><a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot" target="classFrame">RobotState</a></li>
<li><a href="robocode/RobotStatus.html" title="class in robocode" target="classFrame">RobotStatus</a></li>
<li><a href="robocode/control/RobotTestBed.html" title="class in robocode.control" target="classFrame">RobotTestBed</a></li>
<li><a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events" target="classFrame">RoundEndedEvent</a></li>
<li><a href="robocode/RoundEndedEvent.html" title="class in robocode" target="classFrame">RoundEndedEvent</a></li>
<li><a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events" target="classFrame">RoundStartedEvent</a></li>
<li><a href="robocode/Rules.html" title="class in robocode" target="classFrame">Rules</a></li>
<li><a href="robocode/annotation/SafeStatic.html" title="annotation in robocode.annotation" target="classFrame">SafeStatic</a></li>
<li><a href="robocode/ScannedRobotEvent.html" title="class in robocode" target="classFrame">ScannedRobotEvent</a></li>
<li><a href="robocode/SkippedTurnEvent.html" title="class in robocode" target="classFrame">SkippedTurnEvent</a></li>
<li><a href="robocode/StatusEvent.html" title="class in robocode" target="classFrame">StatusEvent</a></li>
<li><a href="robocode/TeamRobot.html" title="class in robocode" target="classFrame">TeamRobot</a></li>
<li><a href="robocode/TurnCompleteCondition.html" title="class in robocode" target="classFrame">TurnCompleteCondition</a></li>
<li><a href="robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events" target="classFrame">TurnEndedEvent</a></li>
<li><a href="robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events" target="classFrame">TurnStartedEvent</a></li>
<li><a href="robocode/util/Utils.html" title="class in robocode.util" target="classFrame">Utils</a></li>
<li><a href="robocode/WinEvent.html" title="class in robocode" target="classFrame">WinEvent</a></li>
<li><a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode" target="classFrame">_AdvancedRadiansRobot</a></li>
<li><a href="robocode/_AdvancedRobot.html" title="class in robocode" target="classFrame">_AdvancedRobot</a></li>
<li><a href="robocode/_Robot.html" title="class in robocode" target="classFrame">_Robot</a></li>
<li><a href="robocode/_RobotBase.html" title="class in robocode" target="classFrame">_RobotBase</a></li>
</ul>
</div>
</body>
</html>
