<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>All Classes (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<h1 class="bar">All&nbsp;Classes</h1>
<div class="indexContainer">
<ul>
<li><a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></li>
<li><a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></li>
<li><a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a></li>
<li><a href="robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a></li>
<li><a href="robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events">BattleErrorEvent</a></li>
<li><a href="robocode/control/events/BattleEvent.html" title="class in robocode.control.events">BattleEvent</a></li>
<li><a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></li>
<li><a href="robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events">BattleFinishedEvent</a></li>
<li><a href="robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events">BattleMessageEvent</a></li>
<li><a href="robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events">BattlePausedEvent</a></li>
<li><a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></li>
<li><a href="robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events">BattleResumedEvent</a></li>
<li><a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></li>
<li><a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></li>
<li><a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a></li>
<li><a href="robocode/BorderSentry.html" title="interface in robocode"><span class="interfaceName">BorderSentry</span></a></li>
<li><a href="robocode/Bullet.html" title="class in robocode">Bullet</a></li>
<li><a href="robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a></li>
<li><a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></li>
<li><a href="robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a></li>
<li><a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></li>
<li><a href="robocode/Condition.html" title="class in robocode">Condition</a></li>
<li><a href="robocode/CustomEvent.html" title="class in robocode">CustomEvent</a></li>
<li><a href="robocode/DeathEvent.html" title="class in robocode">DeathEvent</a></li>
<li><a href="robocode/Droid.html" title="interface in robocode"><span class="interfaceName">Droid</span></a></li>
<li><a href="robocode/Event.html" title="class in robocode">Event</a></li>
<li><a href="robocode/GunTurnCompleteCondition.html" title="class in robocode">GunTurnCompleteCondition</a></li>
<li><a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></li>
<li><a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></li>
<li><a href="robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a></li>
<li><a href="robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IAdvancedEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IAdvancedRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="interfaceName">IAdvancedRobotPeer</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IBasicEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IBasicEvents2</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IBasicEvents3</span></a></li>
<li><a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IBasicRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="interfaceName">IBasicRobotPeer</span></a></li>
<li><a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><span class="interfaceName">IBattleListener</span></a></li>
<li><a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot"><span class="interfaceName">IBulletSnapshot</span></a></li>
<li><a href="robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot"><span class="interfaceName">IDebugProperty</span></a></li>
<li><a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IInteractiveEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IInteractiveRobot</span></a></li>
<li><a href="robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IJuniorRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="interfaceName">IJuniorRobotPeer</span></a></li>
<li><a href="robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IPaintEvents</span></a></li>
<li><a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">IPaintRobot</span></a></li>
<li><a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control"><span class="interfaceName">IRobocodeEngine</span></a></li>
<li><a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot"><span class="interfaceName">IRobotSnapshot</span></a></li>
<li><a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot"><span class="interfaceName">IScoreSnapshot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="interfaceName">IStandardRobotPeer</span></a></li>
<li><a href="robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">ITeamEvents</span></a></li>
<li><a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces"><span class="interfaceName">ITeamRobot</span></a></li>
<li><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="interfaceName">ITeamRobotPeer</span></a></li>
<li><a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot"><span class="interfaceName">ITurnSnapshot</span></a></li>
<li><a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></li>
<li><a href="robocode/KeyEvent.html" title="class in robocode">KeyEvent</a></li>
<li><a href="robocode/KeyPressedEvent.html" title="class in robocode">KeyPressedEvent</a></li>
<li><a href="robocode/KeyReleasedEvent.html" title="class in robocode">KeyReleasedEvent</a></li>
<li><a href="robocode/KeyTypedEvent.html" title="class in robocode">KeyTypedEvent</a></li>
<li><a href="robocode/MessageEvent.html" title="class in robocode">MessageEvent</a></li>
<li><a href="robocode/MouseClickedEvent.html" title="class in robocode">MouseClickedEvent</a></li>
<li><a href="robocode/MouseDraggedEvent.html" title="class in robocode">MouseDraggedEvent</a></li>
<li><a href="robocode/MouseEnteredEvent.html" title="class in robocode">MouseEnteredEvent</a></li>
<li><a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a></li>
<li><a href="robocode/MouseExitedEvent.html" title="class in robocode">MouseExitedEvent</a></li>
<li><a href="robocode/MouseMovedEvent.html" title="class in robocode">MouseMovedEvent</a></li>
<li><a href="robocode/MousePressedEvent.html" title="class in robocode">MousePressedEvent</a></li>
<li><a href="robocode/MouseReleasedEvent.html" title="class in robocode">MouseReleasedEvent</a></li>
<li><a href="robocode/MouseWheelMovedEvent.html" title="class in robocode">MouseWheelMovedEvent</a></li>
<li><a href="robocode/MoveCompleteCondition.html" title="class in robocode">MoveCompleteCondition</a></li>
<li><a href="robocode/PaintEvent.html" title="class in robocode">PaintEvent</a></li>
<li><a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode">RadarTurnCompleteCondition</a></li>
<li><a href="robocode/control/RandomFactory.html" title="class in robocode.control">RandomFactory</a></li>
<li><a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></li>
<li><a href="robocode/Robocode.html" title="class in robocode">Robocode</a></li>
<li><a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></li>
<li><a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></li>
<li><a href="robocode/RobocodeFileWriter.html" title="class in robocode">RobocodeFileWriter</a></li>
<li><a href="robocode/control/RobocodeListener.html" title="interface in robocode.control"><span class="interfaceName">RobocodeListener</span></a></li>
<li><a href="robocode/Robot.html" title="class in robocode">Robot</a></li>
<li><a href="robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a></li>
<li><a href="robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></li>
<li><a href="robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a></li>
<li><a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></li>
<li><a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></li>
<li><a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></li>
<li><a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></li>
<li><a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a></li>
<li><a href="robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a></li>
<li><a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a></li>
<li><a href="robocode/Rules.html" title="class in robocode">Rules</a></li>
<li><a href="robocode/annotation/SafeStatic.html" title="annotation in robocode.annotation">SafeStatic</a></li>
<li><a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></li>
<li><a href="robocode/SkippedTurnEvent.html" title="class in robocode">SkippedTurnEvent</a></li>
<li><a href="robocode/StatusEvent.html" title="class in robocode">StatusEvent</a></li>
<li><a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></li>
<li><a href="robocode/TurnCompleteCondition.html" title="class in robocode">TurnCompleteCondition</a></li>
<li><a href="robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events">TurnEndedEvent</a></li>
<li><a href="robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events">TurnStartedEvent</a></li>
<li><a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></li>
<li><a href="robocode/WinEvent.html" title="class in robocode">WinEvent</a></li>
<li><a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></li>
<li><a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></li>
<li><a href="robocode/_Robot.html" title="class in robocode">_Robot</a></li>
<li><a href="robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></li>
</ul>
</div>
</body>
</html>
