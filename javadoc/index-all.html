<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Jun 04 17:28:34 CEST 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Index (Robocode 1.10.0 API)</title>
<meta name="date" content="2025-06-04">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Index (Robocode 1.10.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<a href="#I:Y">Y</a>&nbsp;<a href="#I:Z:Z_">_</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#abortCurrentBattle--">abortCurrentBattle()</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Aborts the current battle if it is running and waits for the end.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#abortCurrentBattle-boolean-">abortCurrentBattle(boolean)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Aborts the current battle if it is running.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#abortCurrentBattle--">abortCurrentBattle()</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Aborts the current battle if it is running and waits for the end.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#abortCurrentBattle-boolean-">abortCurrentBattle(boolean)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Aborts the current battle if it is running.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#ACCELERATION">ACCELERATION</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The acceleration of a robot, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-">addBattleListener(IBattleListener)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Adds a battle listener that must receive events occurring in battles.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-">addBattleListener(IBattleListener)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Adds a battle listener that must receive events occurring in battles.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-">addCustomEvent(Condition)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Registers a custom event to be called when a condition is met.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#addCustomEvent-robocode.Condition-">addCustomEvent(Condition)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Registers a custom event to be called when a condition is met.</div>
</dd>
<dt><a href="robocode/AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">AdvancedRobot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A more advanced type of robot than Robot that allows non-blocking calls,
 custom events, and writes to the filesystem.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#AdvancedRobot--">AdvancedRobot()</a></span> - Constructor for class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#after--">after()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#afterInit--">afterInit()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Called after the engine is initialized to perform additional setup.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#ahead-int-">ahead(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Moves this robot forward by pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#ahead-double-">ahead(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately moves your robot ahead (forward) by distance measured in
 pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#angleToApproximateDirection-double-">angleToApproximateDirection(double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Returns approximate cardinal direction for absolute angle in radians, like N,NE,E,SE,S,SW,W,NW</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#assertEquals-java.lang.String-java.lang.Object-java.lang.Object-">assertEquals(String, Object, Object)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Throws AssertionError when the params expected and actual do not equal each other.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#assertNear-java.lang.String-double-double-">assertNear(String, double, double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Throws AssertionError when the params expected and actual do not within .00001 difference.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#assertNotNull-java.lang.String-java.lang.Object-">assertNotNull(String, Object)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Throws AssertionError when the param value is null.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#assertTrue-java.lang.String-boolean-">assertTrue(String, boolean)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Throws AssertionError when the assertion is false.</div>
</dd>
</dl>
<a name="I:B">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#back-int-">back(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Moves this robot backward by pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#back-double-">back(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately moves your robot backward by distance measured in pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeListener.html#battleAborted-robocode.control.BattleSpecification-">battleAborted(BattleSpecification)</a></span> - Method in interface robocode.control.<a href="robocode/control/RobocodeListener.html" title="interface in robocode.control">RobocodeListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>IBattleListener.onBattleFinished()</code></a> instead.
 <p>
 This method is called when a battle has been aborted.</span></div>
</div>
</dd>
<dt><a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events"><span class="typeNameLink">BattleAdaptor</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">An abstract adapter class for receiving battle events by implementing the <a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#BattleAdaptor--">BattleAdaptor()</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">Creates a BattleAdaptor.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeListener.html#battleComplete-robocode.control.BattleSpecification-robocode.control.RobotResults:A-">battleComplete(BattleSpecification, RobotResults[])</a></span> - Method in interface robocode.control.<a href="robocode/control/RobocodeListener.html" title="interface in robocode.control">RobocodeListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>IBattleListener.onBattleCompleted()</code></a> instead.
 <p>
 This method is called when a battle completes successfully.</span></div>
</div>
</dd>
<dt><a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleCompletedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A BattleCompletedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-"><code>onBattleCompleted()</code></a> when the battle is completed successfully and results are available.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleCompletedEvent.html#BattleCompletedEvent-robocode.BattleRules-robocode.BattleResults:A-">BattleCompletedEvent(BattleRules, BattleResults[])</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattleCompletedEvent.</div>
</dd>
<dt><a href="robocode/BattleEndedEvent.html" title="class in robocode"><span class="typeNameLink">BattleEndedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A BattleEndedEvent is sent to <a href="robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-"><code>onBattleEnded()</code></a> when the battle is ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleEndedEvent.html#BattleEndedEvent-boolean-robocode.BattleResults-">BattleEndedEvent(boolean, BattleResults)</a></span> - Constructor for class robocode.<a href="robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattleEndedEvent.</div>
</dd>
<dt><a href="robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleErrorEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A BattleErrorEvent is sent to <a href="robocode/control/events/IBattleListener.html#onBattleError-robocode.control.events.BattleErrorEvent-"><code>onBattleError()</code></a> when an error message is sent from the game in the during the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleErrorEvent.html#BattleErrorEvent-java.lang.String-java.lang.Throwable-">BattleErrorEvent(String, Throwable)</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events">BattleErrorEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattleErrorEvent.</div>
</dd>
<dt><a href="robocode/control/events/BattleEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">This is the base class of all battle events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleEvent.html#BattleEvent--">BattleEvent()</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleEvent.html" title="class in robocode.control.events">BattleEvent</a></dt>
<dd>
<div class="block">Creates a new BattleEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#battleFieldSpec">battleFieldSpec</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">The battlefield specification, which is the default.</div>
</dd>
<dt><a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control"><span class="typeNameLink">BattlefieldSpecification</span></a> - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">Defines the size of a battlefield, which is a part of the <a href="robocode/control/BattleSpecification.html" title="class in robocode.control"><code>BattleSpecification</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattlefieldSpecification.html#BattlefieldSpecification--">BattlefieldSpecification()</a></span> - Constructor for class robocode.control.<a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></dt>
<dd>
<div class="block">Creates a standard 800 x 600 battlefield.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattlefieldSpecification.html#BattlefieldSpecification-int-int-">BattlefieldSpecification(int, int)</a></span> - Constructor for class robocode.control.<a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></dt>
<dd>
<div class="block">Creates a battlefield of the specified width and height.</div>
</dd>
<dt><a href="robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleFinishedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A BattleFinishedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-"><code>onBattleFinished()</code></a> when the battle is finished.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleFinishedEvent.html#BattleFinishedEvent-boolean-">BattleFinishedEvent(boolean)</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events">BattleFinishedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattleFinishedEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeListener.html#battleMessage-java.lang.String-">battleMessage(String)</a></span> - Method in interface robocode.control.<a href="robocode/control/RobocodeListener.html" title="interface in robocode.control">RobocodeListener</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the
 <a href="robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-"><code>IBattleListener.onBattleMessage()</code></a> instead.
 <p>
 This method is called when the game logs messages that is normally
 written out to the console.</span></div>
</div>
</dd>
<dt><a href="robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleMessageEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A BattleMessageEvent is sent to <a href="robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-"><code>onBattleMessage()</code></a> when an informal message is sent from the game in the during the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleMessageEvent.html#BattleMessageEvent-java.lang.String-">BattleMessageEvent(String)</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events">BattleMessageEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattleMessageEvent.</div>
</dd>
<dt><a href="robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattlePausedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A BattlePausedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onBattlePaused-robocode.control.events.BattlePausedEvent-"><code>onBattlePaused()</code></a> when a battle has been paused.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattlePausedEvent.html#BattlePausedEvent--">BattlePausedEvent()</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattlePausedEvent.html" title="class in robocode.control.events">BattlePausedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattlePausedEvent.</div>
</dd>
<dt><a href="robocode/BattleResults.html" title="class in robocode"><span class="typeNameLink">BattleResults</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Contains the battle results returned by <a href="robocode/BattleEndedEvent.html#getResults--"><code>BattleEndedEvent.getResults()</code></a>
 when a battle has ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#BattleResults-java.lang.String-int-double-double-double-double-double-double-double-int-int-int-">BattleResults(String, int, double, double, double, double, double, double, double, int, int, int)</a></span> - Constructor for class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Constructs this BattleResults object.</div>
</dd>
<dt><a href="robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleResumedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A BattleResumedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onBattleResumed-robocode.control.events.BattleResumedEvent-"><code>onBattleResumed()</code></a> when a battle has been resumed (after having been paused).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleResumedEvent.html#BattleResumedEvent--">BattleResumedEvent()</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleResumedEvent.html" title="class in robocode.control.events">BattleResumedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattleResumedEvent.</div>
</dd>
<dt><a href="robocode/BattleRules.html" title="class in robocode"><span class="typeNameLink">BattleRules</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Contains the battle rules returned by <a href="robocode/control/events/BattleStartedEvent.html#getBattleRules--"><code>BattleStartedEvent.getBattleRules()</code></a> when a battle is started and
 <a href="robocode/control/events/BattleCompletedEvent.html#getBattleRules--"><code>BattleCompletedEvent.getBattleRules()</code></a>
 when a battle is completed.</div>
</dd>
<dt><a href="robocode/control/BattleSpecification.html" title="class in robocode.control"><span class="typeNameLink">BattleSpecification</span></a> - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">A BattleSpecification defines a battle configuration used by the <a href="robocode/control/RobocodeEngine.html" title="class in robocode.control"><code>RobocodeEngine</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#BattleSpecification-int-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">BattleSpecification(int, BattlefieldSpecification, RobotSpecification[])</a></span> - Constructor for class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Creates a new BattleSpecification with the given number of rounds, battlefield size, and robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#BattleSpecification-int-long-double-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">BattleSpecification(int, long, double, BattlefieldSpecification, RobotSpecification[])</a></span> - Constructor for class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#BattleSpecification-int-long-double-boolean-robocode.control.BattlefieldSpecification-robocode.control.RobotSpecification:A-">BattleSpecification(int, long, double, boolean, BattlefieldSpecification, RobotSpecification[])</a></span> - Constructor for class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#BattleSpecification-robocode.control.BattlefieldSpecification-int-long-double-int-boolean-robocode.control.RobotSpecification:A-">BattleSpecification(BattlefieldSpecification, int, long, double, int, boolean, RobotSpecification[])</a></span> - Constructor for class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#BattleSpecification-robocode.control.BattlefieldSpecification-int-long-double-int-boolean-robocode.control.RobotSpecification:A-robocode.control.RobotSetup:A-">BattleSpecification(BattlefieldSpecification, int, long, double, int, boolean, RobotSpecification[], RobotSetup[])</a></span> - Constructor for class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Creates a new BattleSpecification with the given settings.</div>
</dd>
<dt><a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">BattleStartedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A BattleStartedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onBattleStarted-robocode.control.events.BattleStartedEvent-"><code>onBattleStarted()</code></a> when a new battle is started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleStartedEvent.html#BattleStartedEvent-robocode.BattleRules-int-boolean-java.util.UUID-">BattleStartedEvent(BattleRules, int, boolean, UUID)</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new BattleStartedEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#bearGunTo-int-">bearGunTo(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Turns the gun to the specified angle (in degrees) relative to body of this robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#before--">before()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">The setup method run before each test, which sets up the listener on the engine for testing.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#beforeInit--">beforeInit()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#black">black</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color black (0x000000)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#blue">blue</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color blue (0x0000FF)</div>
</dd>
<dt><a href="robocode/BorderSentry.html" title="interface in robocode"><span class="typeNameLink">BorderSentry</span></a> - Interface in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A robot that implements BorderSentry is a robot type used for keeping other robots away from the borders,
 i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html#broadcastMessage-java.io.Serializable-">broadcastMessage(Serializable)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dt>
<dd>
<div class="block">Broadcasts a message to all teammates.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#broadcastMessage-java.io.Serializable-">broadcastMessage(Serializable)</a></span> - Method in class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>
<div class="block">Broadcasts a message to all teammates.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#brown">brown</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color brown (0x8B4513)</div>
</dd>
<dt><a href="robocode/Bullet.html" title="class in robocode"><span class="typeNameLink">Bullet</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Represents a bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#Bullet-double-double-double-double-java.lang.String-java.lang.String-boolean-int-">Bullet(double, double, double, double, String, String, boolean, int)</a></span> - Constructor for class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Called by the game to create a new <code>Bullet</code> object</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#bulletDamage">bulletDamage</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#bulletDamageBonus">bulletDamageBonus</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/BulletHitBulletEvent.html" title="class in robocode"><span class="typeNameLink">BulletHitBulletEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-"><code>onBulletHitBullet</code></a> when one of your bullets has hit another bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitBulletEvent.html#BulletHitBulletEvent-robocode.Bullet-robocode.Bullet-">BulletHitBulletEvent(Bullet, Bullet)</a></span> - Constructor for class robocode.<a href="robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new <code>BulletHitEvent</code>.</div>
</dd>
<dt><a href="robocode/BulletHitEvent.html" title="class in robocode"><span class="typeNameLink">BulletHitEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-"><code>onBulletHit</code></a>
 when one of your bullets has hit another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitEvent.html#BulletHitEvent-java.lang.String-double-robocode.Bullet-">BulletHitEvent(String, double, Bullet)</a></span> - Constructor for class robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new <code>BulletHitEvent</code>.</div>
</dd>
<dt><a href="robocode/BulletMissedEvent.html" title="class in robocode"><span class="typeNameLink">BulletMissedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-"><code>onBulletMissed</code></a> when one of your bullets has missed, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletMissedEvent.html#BulletMissedEvent-robocode.Bullet-">BulletMissedEvent(Bullet)</a></span> - Constructor for class robocode.<a href="robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new <code>BulletMissedEvent</code>.</div>
</dd>
<dt><a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">BulletState</span></a> - Enum in <a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a></dt>
<dd>
<div class="block">Defines a bullet state, which can be: just fired, moving somewhere, hitting a victim,
 hitting another bullet, hitting the wall, exploded, or inactive.</div>
</dd>
</dl>
<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#cleanup--">cleanup()</a></span> - Method in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Called by the system in order to clean up references to internal objects.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#cleanup--">cleanup()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Releases any resources used by this test bed.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/GunTurnCompleteCondition.html#cleanup--">cleanup()</a></span> - Method in class robocode.<a href="robocode/GunTurnCompleteCondition.html" title="class in robocode">GunTurnCompleteCondition</a></dt>
<dd>
<div class="block">Called by the system in order to clean up references to internal objects.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MoveCompleteCondition.html#cleanup--">cleanup()</a></span> - Method in class robocode.<a href="robocode/MoveCompleteCondition.html" title="class in robocode">MoveCompleteCondition</a></dt>
<dd>
<div class="block">Called by the system in order to clean up references to internal objects.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RadarTurnCompleteCondition.html#cleanup--">cleanup()</a></span> - Method in class robocode.<a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode">RadarTurnCompleteCondition</a></dt>
<dd>
<div class="block">Called by the system in order to clean up references to internal objects.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TurnCompleteCondition.html#cleanup--">cleanup()</a></span> - Method in class robocode.<a href="robocode/TurnCompleteCondition.html" title="class in robocode">TurnCompleteCondition</a></dt>
<dd>
<div class="block">Called by the system in order to clean up references to internal objects.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#clearAllEvents--">clearAllEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Clears out any pending events in the robot's event queue immediately.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#clearAllEvents--">clearAllEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Clears out any pending events in the robot's event queue immediately.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#close--">close()</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Closes the RobocodeEngine and releases any allocated resources it holds.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#close--">close()</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Closes the RobocodeEngine and releases any allocated resources it holds.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#close--">close()</a></span> - Method in class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Closes this output stream.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#compareTo-robocode.BattleResults-">compareTo(BattleResults)</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dt><span class="memberNameLink"><a href="robocode/CustomEvent.html#compareTo-robocode.Event-">compareTo(Event)</a></span> - Method in class robocode.<a href="robocode/CustomEvent.html" title="class in robocode">CustomEvent</a></dt>
<dd>
<div class="block">Compares this event to another event regarding precedence.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Event.html#compareTo-robocode.Event-">compareTo(Event)</a></span> - Method in class robocode.<a href="robocode/Event.html" title="class in robocode">Event</a></dt>
<dd>
<div class="block">Compares this event to another event regarding precedence.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#compareTo-robocode.Event-">compareTo(Event)</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block">Compares this event to another event regarding precedence.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#compareTo-robocode.Event-">compareTo(Event)</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Compares this event to another event regarding precedence.</div>
</dd>
<dt><a href="robocode/Condition.html" title="class in robocode"><span class="typeNameLink">Condition</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Condition is used to define custom  <a href="robocode/AdvancedRobot.html#waitFor-robocode.Condition-"><code>waitFor(Condition)</code></a> and custom events for an <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#Condition--">Condition()</a></span> - Constructor for class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Creates a new, unnamed Condition with the default priority, which is 80.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#Condition-java.lang.String-">Condition(String)</a></span> - Constructor for class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Creates a new Condition with the specified name, and default priority,
 which is 80.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#Condition-java.lang.String-int-">Condition(String, int)</a></span> - Constructor for class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Creates a new Condition with the specified name and priority.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotResults.html#convertResults-robocode.BattleResults:A-">convertResults(BattleResults[])</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></dt>
<dd>
<div class="block">Converts an array of <a href="robocode/BattleResults.html" title="class in robocode"><code>BattleResults</code></a> into an array of <a href="robocode/control/RobotResults.html" title="class in robocode.control"><code>RobotResults</code></a>.</div>
</dd>
<dt><a href="robocode/CustomEvent.html" title="class in robocode"><span class="typeNameLink">CustomEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/AdvancedRobot.html#onCustomEvent-robocode.CustomEvent-"><code>onCustomEvent()</code></a> when a custom condition is met.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/CustomEvent.html#CustomEvent-robocode.Condition-">CustomEvent(Condition)</a></span> - Constructor for class robocode.<a href="robocode/CustomEvent.html" title="class in robocode">CustomEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new CustomEvent when a condition is met.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/CustomEvent.html#CustomEvent-robocode.Condition-int-">CustomEvent(Condition, int)</a></span> - Constructor for class robocode.<a href="robocode/CustomEvent.html" title="class in robocode">CustomEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new CustomEvent when a condition is met.</div>
</dd>
</dl>
<a name="I:D">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><a href="robocode/DeathEvent.html" title="class in robocode"><span class="typeNameLink">DeathEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/Robot.html#onDeath-robocode.DeathEvent-"><code>onDeath()</code></a> when your
 robot dies.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/DeathEvent.html#DeathEvent--">DeathEvent()</a></span> - Constructor for class robocode.<a href="robocode/DeathEvent.html" title="class in robocode">DeathEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new DeathEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#DECELERATION">DECELERATION</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The deceleration of a robot, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#doNothing--">doNothing()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Skips a turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#doNothing-int-">doNothing(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Skips the specified number of turns.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#doNothing--">doNothing()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Do nothing this turn, meaning that the robot will skip it's turn.</div>
</dd>
<dt><a href="robocode/Droid.html" title="interface in robocode"><span class="typeNameLink">Droid</span></a> - Interface in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A Droid is an interface used on a <a href="robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a> to create a specialized team robot,
 i.e.</div>
</dd>
</dl>
<a name="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#endTurn--">endTurn()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#execute--"><code>execute</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#energy">energy</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current energy of this robot, where 100 means full energy and 0 means no energy (dead).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#engine">engine</a></span> - Static variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">The Robocode game engine instance used for this test.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#engineErrorsListener">engineErrorsListener</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotResults.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class robocode.control.<a href="robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#errors">errors</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">The number of errors generated during this battle so far.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#errorText">errorText</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/Event.html" title="class in robocode"><span class="typeNameLink">Event</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">The superclass of all Robocode events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Event.html#Event--">Event()</a></span> - Constructor for class robocode.<a href="robocode/Event.html" title="class in robocode">Event</a></dt>
<dd>
<div class="block">Default constructor used by the game to create a new event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#execute--">execute()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#execute--">execute()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--">execute()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Executes any pending actions, or continues executing actions that are
 in process.</div>
</dd>
</dl>
<a name="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#fieldHeight">fieldHeight</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Contains the height of the battlefield.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#fieldWidth">fieldWidth</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Contains the width of the battlefield.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_RobotBase.html#finalize--">finalize()</a></span> - Method in class robocode.<a href="robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></dt>
<dd>
<div class="block">Called by the system to 'clean up' after your robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#finalize--">finalize()</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#fire--">fire()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Fires a bullet with the default power of 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#fire-double-">fire(double)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Fires a bullet with the specified bullet power, which is between 0.1 and 3
 where 3 is the maximum bullet power.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#fire-double-">fire(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately fires a bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#fire-double-">fire(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Immediately fires a bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#fireBullet-double-">fireBullet(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately fires a bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#firsts">firsts</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#flush--">flush()</a></span> - Method in class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Flushes this output stream.</div>
</dd>
</dl>
<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IAdvancedRobot.html#getAdvancedEventListener--">getAdvancedEventListener()</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces">IAdvancedRobot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about advanced
 robot event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getAllEvents--">getAllEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all events currently in the robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getAllEvents--">getAllEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all events currently in the robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getAuthorName--">getAuthorName()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the name of the author of this robot or team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#getBasicEventListener--">getBasicEventListener()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getBasicEventListener--">getBasicEventListener()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about basic
 robot event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicRobot.html#getBasicEventListener--">getBasicEventListener()</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about basic
 robot event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getBattlefield--">getBattlefield()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the battlefield size for this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleRules.html#getBattlefieldHeight--">getBattlefieldHeight()</a></span> - Method in class robocode.<a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></dt>
<dd>
<div class="block">Returns the battlefield height.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getBattleFieldHeight--">getBattleFieldHeight()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the height of the current battlefield measured in pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldHeight--">getBattleFieldHeight()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the height of the current battlefield measured in pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleRules.html#getBattlefieldWidth--">getBattlefieldWidth()</a></span> - Method in class robocode.<a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></dt>
<dd>
<div class="block">Returns the battlefield width.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getBattleFieldWidth--">getBattleFieldWidth()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the width of the current battlefield measured in pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBattleFieldWidth--">getBattleFieldWidth()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the width of the current battlefield measured in pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleStartedEvent.html#getBattleId--">getBattleId()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a></dt>
<dd>
<div class="block">Unique ID of the battle</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#getBattleNum--">getBattleNum()</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getRoundNum--"><code>getRoundNum()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleCompletedEvent.html#getBattleRules--">getBattleRules()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a></dt>
<dd>
<div class="block">Returns the rules used in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleStartedEvent.html#getBattleRules--">getBattleRules()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a></dt>
<dd>
<div class="block">Returns the rules that will be used in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getBearing--">getBearing()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the bullet, relative to your robot's heading,
 in degrees (-180 &lt; getBearing() &lt;= 180).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#getBearing--">getBearing()</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the robot you hit, relative to your robot's
 heading, in degrees (-180 &lt;= getBearing() &lt; 180)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitWallEvent.html#getBearing--">getBearing()</a></span> - Method in class robocode.<a href="robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the wall you hit, relative to your robot's
 heading, in degrees (-180 &lt;= getBearing() &lt; 180)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getBearing--">getBearing()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the robot you scanned, relative to your robot's
 heading, in degrees (-180 &lt;= getBearing() &lt; 180)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#getBearingDegrees--">getBearingDegrees()</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitRobotEvent.html#getBearing--"><code>HitRobotEvent.getBearing()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitWallEvent.html#getBearingDegrees--">getBearingDegrees()</a></span> - Method in class robocode.<a href="robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitWallEvent.html#getBearing--"><code>HitWallEvent.getBearing()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getBearingRadians--">getBearingRadians()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the bullet, relative to your robot's heading,
 in radians (-Math.PI &lt; getBearingRadians() &lt;= Math.PI).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#getBearingRadians--">getBearingRadians()</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the robot you hit, relative to your robot's
 heading, in radians (-PI &lt;= getBearingRadians() &lt; PI)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitWallEvent.html#getBearingRadians--">getBearingRadians()</a></span> - Method in class robocode.<a href="robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the wall you hit, relative to your robot's
 heading, in radians (-PI &lt;= getBearingRadians() &lt; PI)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getBearingRadians--">getBearingRadians()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the bearing to the robot you scanned, relative to your robot's
 heading, in radians (-PI &lt;= getBearingRadians() &lt; PI)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getBodyColor--">getBodyColor()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the color of the body.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getBodyHeading--">getBodyHeading()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the body heading of the robot in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyHeading--">getBodyHeading()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the direction that the robot's body is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getBodyTurnRemaining--">getBodyTurnRemaining()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the angle remaining in the robot's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitBulletEvent.html#getBullet--">getBullet()</a></span> - Method in class robocode.<a href="robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a></dt>
<dd>
<div class="block">Returns your bullet that hit another bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitEvent.html#getBullet--">getBullet()</a></span> - Method in class robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></dt>
<dd>
<div class="block">Returns the bullet of yours that hit the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletMissedEvent.html#getBullet--">getBullet()</a></span> - Method in class robocode.<a href="robocode/BulletMissedEvent.html" title="class in robocode">BulletMissedEvent</a></dt>
<dd>
<div class="block">Returns the bullet that missed.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getBullet--">getBullet()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the bullet that hit your robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getBulletDamage--">getBulletDamage()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the bullet damage score of this robot in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#getBulletDamage-double-">getBulletDamage(double)</a></span> - Static method in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">Returns the amount of damage of a bullet given a specific bullet power.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getBulletDamageBonus--">getBulletDamageBonus()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the bullet damage bonus of this robot in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#getBulletHitBonus-double-">getBulletHitBonus(double)</a></span> - Static method in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">Returns the amount of bonus given when a robot's bullet hits an opponent
 robot given a specific bullet power.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getBulletHitBulletEvents--">getBulletHitBulletEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all BulletHitBulletEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitBulletEvents--">getBulletHitBulletEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all BulletHitBulletEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getBulletHitEvents--">getBulletHitEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all BulletHitEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletHitEvents--">getBulletHitEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all BulletHitEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getBulletId--">getBulletId()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the ID of the bullet used for identifying the bullet in a collection of bullets.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getBulletMissedEvents--">getBulletMissedEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all BulletMissedEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getBulletMissedEvents--">getBulletMissedEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all BulletMissedEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/ITurnSnapshot.html#getBullets--">getBullets()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></dt>
<dd>
<div class="block">Returns a list of snapshots for the bullets that are currently on the battlefield.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#getBulletSpeed-double-">getBulletSpeed(double)</a></span> - Static method in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">Returns the speed of a bullet given a specific bullet power measured in pixels/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getCall--">getCall()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">This call <em>must</em> be made from a robot call to inform the game
 that the robot made a <code>get*</code> call like e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getClassName--">getClassName()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the full class name of this robot or team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getColor--">getColor()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the color of the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/CustomEvent.html#getCondition--">getCondition()</a></span> - Method in class robocode.<a href="robocode/CustomEvent.html" title="class in robocode">CustomEvent</a></dt>
<dd>
<div class="block">Returns the condition that fired, causing this event to be generated.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getContestantIndex--">getContestantIndex()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the contestant index, which is unique for each robot or team participating in a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getCurrentBulletDamageScore--">getCurrentBulletDamageScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the current bullet damage score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getCurrentBulletKillBonus--">getCurrentBulletKillBonus()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the current bullet kill bonus.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getCurrentRammingDamageScore--">getCurrentRammingDamageScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the current ramming damage score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getCurrentRammingKillBonus--">getCurrentRammingKillBonus()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the current ramming kill bonus.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getCurrentScore--">getCurrentScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the current score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getCurrentSurvivalBonus--">getCurrentSurvivalBonus()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the current survival bonus.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getCurrentSurvivalScore--">getCurrentSurvivalScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the current survival score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#getCurrentWorkingDir--">getCurrentWorkingDir()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Returns the current working directory.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getDataDirectory--">getDataDirectory()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a file representing a data directory for the robot, which can be
 written to using <a href="robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or
 <a href="robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataDirectory--">getDataDirectory()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a file representing a data directory for the robot, which can be
 written to using <a href="robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or
 <a href="robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getDataFile-java.lang.String-">getDataFile(String)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a file in your data directory that you can write to using
 <a href="robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or <a href="robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataFile-java.lang.String-">getDataFile(String)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a file in your data directory that you can write to using
 <a href="robocode/RobocodeFileOutputStream.html" title="class in robocode"><code>RobocodeFileOutputStream</code></a> or <a href="robocode/RobocodeFileWriter.html" title="class in robocode"><code>RobocodeFileWriter</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getDataQuotaAvailable--">getDataQuotaAvailable()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the data quota available in your data directory, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getDataQuotaAvailable--">getDataQuotaAvailable()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns the data quota available in your data directory, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getDebugProperties--">getDebugProperties()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns a snapshot of debug properties.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getDescription--">getDescription()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the description provided by the author of this robot or team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getDistance--">getDistance()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the distance to the robot (your center to his center).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getDistanceRemaining--">getDistanceRemaining()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getDistanceRemaining--">getDistanceRemaining()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getDistanceRemaining--">getDistanceRemaining()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the distance remaining in the robot's current move measured in
 pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#getEnemyName--">getEnemyName()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Must return a fully qualified enemy robot name to be in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitEvent.html#getEnergy--">getEnergy()</a></span> - Method in class robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></dt>
<dd>
<div class="block">Returns the remaining energy of the robot your bullet has hit (after the
 damage done by your bullet).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getEnergy--">getEnergy()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the energy level of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#getEnergy--">getEnergy()</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block">Returns the amount of energy of the robot you hit.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getEnergy--">getEnergy()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the robot's current energy.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getEnergy--">getEnergy()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the robot's current energy.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getEnergy--">getEnergy()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the robot's current energy.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getEnergy--">getEnergy()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the energy of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleErrorEvent.html#getError--">getError()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events">BattleErrorEvent</a></dt>
<dd>
<div class="block">Returns the error message.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleErrorEvent.html#getErrorInstance--">getErrorInstance()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleErrorEvent.html" title="class in robocode.control.events">BattleErrorEvent</a></dt>
<dd>
<div class="block">Returns the error instance when available.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getEventPriority-java.lang.String-">getEventPriority(String)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the current priority of a class of events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getEventPriority-java.lang.String-">getEventPriority(String)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns the current priority of a class of events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#getExpectedErrors--">getExpectedErrors()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#getExpectedRobotCount-java.lang.String-">getExpectedRobotCount(String)</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Provides the number of robots in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getExplosionImageIndex--">getExplosionImageIndex()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the explosion image index, which is different depending on the type of explosion.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getFirsts--">getFirsts()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the number of rounds this robot placed first in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getFrame--">getFrame()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the current frame number to display, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getGraphics--">getGraphics()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns a graphics context used for painting graphical items for the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGraphics--">getGraphics()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns a graphics context used for painting graphical items for the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#getGunCharge--">getGunCharge()</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getGunHeat--"><code>getGunHeat()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getGunColor--">getGunColor()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the color of the gun.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleRules.html#getGunCoolingRate--">getGunCoolingRate()</a></span> - Method in class robocode.<a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></dt>
<dd>
<div class="block">Returns the rate at which the gun will cool down, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getGunCoolingRate--">getGunCoolingRate()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the gun cooling rate of the robots in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getGunCoolingRate--">getGunCoolingRate()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the rate at which the gun will cool down, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunCoolingRate--">getGunCoolingRate()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the rate at which the gun will cool down, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getGunHeading--">getGunHeading()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the gun heading of the robot in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getGunHeading--">getGunHeading()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the direction that the robot's gun is facing, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeading--">getGunHeading()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the direction that the robot's gun is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getGunHeading--">getGunHeading()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the direction that the robot's gun is facing, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#getGunHeadingDegrees--">getGunHeadingDegrees()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getGunHeading--"><code>getGunHeading()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#getGunHeadingRadians--">getGunHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getGunHeadingRadians--">getGunHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the direction that the robot's gun is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getGunHeadingRadians--">getGunHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the direction that the robot's gun is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getGunHeat--">getGunHeat()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the gun heat of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getGunHeat--">getGunHeat()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the current heat of the gun.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunHeat--">getGunHeat()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the current heat of the gun.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getGunHeat--">getGunHeat()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the current heat of the gun.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#getGunHeat-double-">getGunHeat(double)</a></span> - Static method in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">Returns the heat produced by firing the gun given a specific bullet
 power.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#getGunImageName--">getGunImageName()</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#getGunRotationRate--">getGunRotationRate()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Gets the gun's clockwise rotation per turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#getGunRotationRateRadians--">getGunRotationRateRadians()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Gets the gun's clockwise rotation per turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getGunTurnRemaining--">getGunTurnRemaining()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the angle remaining in the gun's turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getGunTurnRemaining--">getGunTurnRemaining()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the angle remaining in the gun's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getGunTurnRemaining--">getGunTurnRemaining()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the angle remaining in the gun's turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the angle remaining in the gun's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getGunTurnRemainingRadians--">getGunTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the angle remaining in the gun's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getHeading--">getHeading()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the direction the bullet is/was heading, in degrees
 (0 &lt;= getHeading() &lt; 360).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSetup.html#getHeading--">getHeading()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a></dt>
<dd>
<div class="block">Returns the body, gun, and radar heading (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getHeading--">getHeading()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getHeading--">getHeading()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the heading of the bullet when it hit you, in degrees
 (0 &lt;= getHeading() &lt; 360).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getHeading--">getHeading()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the direction that the robot's body is facing, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getHeading--">getHeading()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the direction that the robot's body is facing, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getHeading--">getHeading()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the heading of the robot, in degrees (0 &lt;= getHeading() &lt; 360)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#getHeadingDegrees--">getHeadingDegrees()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getHeading--"><code>getHeading()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getHeadingDegrees--">getHeadingDegrees()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitByBulletEvent.html#getHeading--"><code>HitByBulletEvent.getHeading()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#getHeadingRadians--">getHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getHeadingRadians--">getHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the direction that the robot's body is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getHeadingRadians--">getHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the direction the bullet is/was heading, in radians
 (0 &lt;= getHeadingRadians() &lt; 2 * Math.PI).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getHeadingRadians--">getHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the heading of the bullet when it hit you, in radians
 (0 &lt;= getHeadingRadians() &lt; 2 * PI).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getHeadingRadians--">getHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the direction that the robot's body is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getHeadingRadians--">getHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the heading of the robot, in radians (0 &lt;= getHeading() &lt; 2 * PI)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattlefieldSpecification.html#getHeight--">getHeight()</a></span> - Method in class robocode.control.<a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></dt>
<dd>
<div class="block">Returns the height of this battlefield.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getHeight--">getHeight()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the height of the robot measured in pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleRules.html#getHideEnemyNames--">getHideEnemyNames()</a></span> - Method in class robocode.<a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getHideEnemyNames--">getHideEnemyNames()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the flag specifying if the enemy names must be hidden from events sent to robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitBulletEvent.html#getHitBullet--">getHitBullet()</a></span> - Method in class robocode.<a href="robocode/BulletHitBulletEvent.html" title="class in robocode">BulletHitBulletEvent</a></dt>
<dd>
<div class="block">Returns the bullet that was hit by your bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getHitByBulletEvents--">getHitByBulletEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all HitByBulletEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitByBulletEvents--">getHitByBulletEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all HitByBulletEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getHitRobotEvents--">getHitRobotEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all HitRobotEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitRobotEvents--">getHitRobotEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all HitRobotEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getHitWallEvents--">getHitWallEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all HitWallEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getHitWallEvents--">getHitWallEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all HitWallEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleRules.html#getInactivityTime--">getInactivityTime()</a></span> - Method in class robocode.<a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></dt>
<dd>
<div class="block">Returns the allowed inactivity time, where the robot is not taking any action, before will begin to be zapped.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getInactivityTime--">getInactivityTime()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the allowed inactivity time for the robots in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleCompletedEvent.html#getIndexedResults--">getIndexedResults()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a></dt>
<dd>
<div class="block">Returns the battle results that can be used to determine the score for the individual robot based
 on the robot index.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/ITurnSnapshot.html#getIndexedTeamScores--">getIndexedTeamScores()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></dt>
<dd>
<div class="block">Returns an array of indexed scores grouped by teams that can be used to determine the score
 for the individual team based on the team index.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#getInitialPositions--">getInitialPositions()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Returns a comma or space separated list like: x1,y1,heading1, x2,y2,heading2, which are the
 coordinates and heading of robot #1 and #2.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getInitialSetups--">getInitialSetups()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the initial position and heading of each robot participating in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getInteractiveEventListener--">getInteractiveEventListener()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about interactive
 events, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveRobot.html#getInteractiveEventListener--">getInteractiveEventListener()</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces">IInteractiveRobot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about interactive
 events, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getJarFile--">getJarFile()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the JAR file containing this robot or team, or <code>null</code> if it
 does not come from a JAR file (could be class files instead).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IDebugProperty.html#getKey--">getKey()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot">IDebugProperty</a></dt>
<dd>
<div class="block">Returns the key of the property.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getLastSurvivorBonus--">getLastSurvivorBonus()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the last survivor score of this robot in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#getLife--">getLife()</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getEnergy--"><code>getEnergy()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitEvent.html#getLife--">getLife()</a></span> - Method in class robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/BulletHitEvent.html#getEnergy--"><code>BulletHitEvent.getEnergy()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getLife--">getLife()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getEnergy--"><code>ScannedRobotEvent.getEnergy()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#getLocalRepository--">getLocalRepository()</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Returns all robots available from the local robot repository of Robocode.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#getLocalRepository-java.lang.String-">getLocalRepository(String)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Returns a selection of robots available from the local robot repository
 of Robocode.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#getLocalRepository--">getLocalRepository()</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Returns all robots available from the local robot repository of Robocode.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#getLocalRepository-java.lang.String-">getLocalRepository(String)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Returns a selection of robots available from the local robot repository
 of Robocode.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#getMaxWaitCount--">getMaxWaitCount()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is no longer functional.
             Use <a href="robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-"><code>AdvancedRobot.onSkippedTurn(SkippedTurnEvent)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleMessageEvent.html#getMessage--">getMessage()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleMessageEvent.html" title="class in robocode.control.events">BattleMessageEvent</a></dt>
<dd>
<div class="block">Returns the informal message.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MessageEvent.html#getMessage--">getMessage()</a></span> - Method in class robocode.<a href="robocode/MessageEvent.html" title="class in robocode">MessageEvent</a></dt>
<dd>
<div class="block">Returns the message itself.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html#getMessageEvents--">getMessageEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all MessageEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#getMessageEvents--">getMessageEvents()</a></span> - Method in class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all MessageEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the name of the robot that fired this bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitEvent.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></dt>
<dd>
<div class="block">Returns the name of the robot your bullet hit.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Returns the name of this condition.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getName--">getName()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the name of this robot or team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getName--">getName()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the name of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getName--">getName()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the name of the contestant, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the name of the robot that fired the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block">Returns the name of the robot you hit.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Returns the filename of this output stream.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the robot's name.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotDeathEvent.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a></dt>
<dd>
<div class="block">Returns the name of the robot that died.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getName--">getName()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the robot's name.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getName--">getName()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the name of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getNameAndVersion--">getNameAndVersion()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the name and version of this robot or team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#getNumBattles--">getNumBattles()</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getNumRounds--"><code>getNumRounds()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleRules.html#getNumRounds--">getNumRounds()</a></span> - Method in class robocode.<a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></dt>
<dd>
<div class="block">Returns the number of rounds.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getNumRounds--">getNumRounds()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the number of rounds in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#getNumRounds--">getNumRounds()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Provides the number of rounds in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getNumRounds--">getNumRounds()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the number of rounds in the current battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--">getNumRounds()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the number of rounds in the current battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getNumRounds--">getNumRounds()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the number of rounds in the current battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getNumSentries--">getNumSentries()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumSentries--">getNumSentries()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getNumSentries--">getNumSentries()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns how many sentry robots that are left in the current round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getOthers--">getOthers()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns how many opponents that are left in the current round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getOthers--">getOthers()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns how many opponents that are left in the current round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getOthers--">getOthers()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns how many opponents that are left in the current round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getOutputStreamSnapshot--">getOutputStreamSnapshot()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns a snapshot of the output print stream for this robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getOwnerIndex--">getOwnerIndex()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getPaintEventListener--">getPaintEventListener()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about painting
 events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IPaintRobot.html#getPaintEventListener--">getPaintEventListener()</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces">IPaintRobot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about painting
 events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getPaintX--">getPaintX()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the X painting position of the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getPaintY--">getPaintY()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the Y painting position of the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getPower--">getPower()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the power of this bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getPower--">getPower()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the bullet power.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getPower--">getPower()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the power of this bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleEndedEvent.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a></dt>
<dd>
<div class="block">Returns the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Returns the priority of this condition.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/CustomEvent.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/CustomEvent.html" title="class in robocode">CustomEvent</a></dt>
<dd>
<div class="block">Returns the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/DeathEvent.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/DeathEvent.html" title="class in robocode">DeathEvent</a></dt>
<dd>
<div class="block">Returns the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Event.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/Event.html" title="class in robocode">Event</a></dt>
<dd>
<div class="block">Returns the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RoundEndedEvent.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a></dt>
<dd>
<div class="block">Returns the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/SkippedTurnEvent.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/SkippedTurnEvent.html" title="class in robocode">SkippedTurnEvent</a></dt>
<dd>
<div class="block">Returns the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/WinEvent.html#getPriority--">getPriority()</a></span> - Method in class robocode.<a href="robocode/WinEvent.html" title="class in robocode">WinEvent</a></dt>
<dd>
<div class="block">Returns the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getRadarColor--">getRadarColor()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the color of the radar.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getRadarHeading--">getRadarHeading()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the radar heading of the robot in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getRadarHeading--">getRadarHeading()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the direction that the robot's radar is facing, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarHeading--">getRadarHeading()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the direction that the robot's radar is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getRadarHeading--">getRadarHeading()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the direction that the robot's radar is facing, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#getRadarHeadingDegrees--">getRadarHeadingDegrees()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#getRadarHeading--"><code>getRadarHeading()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#getRadarHeadingRadians--">getRadarHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getRadarHeadingRadians--">getRadarHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the direction that the robot's radar is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getRadarHeadingRadians--">getRadarHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the direction that the robot's radar is facing, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#getRadarImageName--">getRadarImageName()</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#getRadarRotationRate--">getRadarRotationRate()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Gets the radar's clockwise rotation per turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#getRadarRotationRateRadians--">getRadarRotationRateRadians()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Gets the radar's clockwise rotation per turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getRadarTurnRemaining--">getRadarTurnRemaining()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the angle remaining in the radar's turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRadarTurnRemaining--">getRadarTurnRemaining()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the angle remaining in the radar's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getRadarTurnRemaining--">getRadarTurnRemaining()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the angle remaining in the radar's turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the angle remaining in the radar's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getRadarTurnRemainingRadians--">getRadarTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the angle remaining in the radar's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getRamDamage--">getRamDamage()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the ram damage score of this robot in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getRamDamageBonus--">getRamDamageBonus()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the ram damage bonus of this robot in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RandomFactory.html#getRandom--">getRandom()</a></span> - Static method in class robocode.control.<a href="robocode/control/RandomFactory.html" title="class in robocode.control">RandomFactory</a></dt>
<dd>
<div class="block">Returns the random number generator used for generating a stream of random
 numbers.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#getRandom--">getRandom()</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Returns random number generator.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getRank--">getRank()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the rank of this robot in the battle results.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleEndedEvent.html#getResults--">getResults()</a></span> - Method in class robocode.<a href="robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a></dt>
<dd>
<div class="block">Returns the battle results.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getRobocodeVersion--">getRobocodeVersion()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the version of Robocode this robot or team was build with.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotResults.html#getRobot--">getRobot()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></dt>
<dd>
<div class="block">Returns the robot these results are meant for.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotBearing--">getRobotBearing()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getBearing--"><code>ScannedRobotEvent.getBearing()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotBearingDegrees--">getRobotBearingDegrees()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getBearing--"><code>ScannedRobotEvent.getBearing()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotBearingRadians--">getRobotBearingRadians()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getBearingRadians--"><code>ScannedRobotEvent.getBearingRadians()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getRobotDeathEvents--">getRobotDeathEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all RobotDeathEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getRobotDeathEvents--">getRobotDeathEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all RobotDeathEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotDistance--">getRobotDistance()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getDistance--"><code>ScannedRobotEvent.getDistance()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotHeading--">getRobotHeading()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getHeading--"><code>ScannedRobotEvent.getHeading()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotHeadingDegrees--">getRobotHeadingDegrees()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getHeading--"><code>ScannedRobotEvent.getHeading()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotHeadingRadians--">getRobotHeadingRadians()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getHeadingRadians--"><code>ScannedRobotEvent.getHeadingRadians()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#getRobotImageName--">getRobotImageName()</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getRobotIndex--">getRobotIndex()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the index of the robot, which is unique for the specific robot and constant during a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitEvent.html#getRobotLife--">getRobotLife()</a></span> - Method in class robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/BulletHitEvent.html#getEnergy--"><code>BulletHitEvent.getEnergy()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotLife--">getRobotLife()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getEnergy--"><code>ScannedRobotEvent.getEnergy()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BulletHitEvent.html#getRobotName--">getRobotName()</a></span> - Method in class robocode.<a href="robocode/BulletHitEvent.html" title="class in robocode">BulletHitEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/BulletHitEvent.html#getName--"><code>BulletHitEvent.getName()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#getRobotName--">getRobotName()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Must return a fully qualified robot name to be in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#getRobotName--">getRobotName()</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/HitRobotEvent.html#getName--"><code>HitRobotEvent.getName()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotDeathEvent.html#getRobotName--">getRobotName()</a></span> - Method in class robocode.<a href="robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/RobotDeathEvent.html#getName--"><code>RobotDeathEvent.getName()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotName--">getRobotName()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getName--"><code>ScannedRobotEvent.getName()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundStartedEvent.html#getRobotObjects--">getRobotObjects()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#getRobotRunnable--">getRobotRunnable()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getRobotRunnable--">getRobotRunnable()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called by the game to invoke the
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang"><code>run()</code></a> method of your robot, where the program
 of your robot is implemented.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicRobot.html#getRobotRunnable--">getRobotRunnable()</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></dt>
<dd>
<div class="block">This method is called by the game to invoke the
 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Runnable.html?is-external=true#run--" title="class or interface in java.lang"><code>run()</code></a> method of your robot, where the program
 of your robot is implemented.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getRobots--">getRobots()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the specifications of the robots participating in this battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/ITurnSnapshot.html#getRobots--">getRobots()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></dt>
<dd>
<div class="block">Returns a list of snapshots for the robots participating in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleStartedEvent.html#getRobotsCount--">getRobotsCount()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a></dt>
<dd>
<div class="block">Returns the number of robots participating in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#getRobotsDir--">getRobotsDir()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Returns the directory containing the robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getRobotVelocity--">getRobotVelocity()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#getVelocity--"><code>ScannedRobotEvent.getVelocity()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundEndedEvent.html#getRound--">getRound()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a></dt>
<dd>
<div class="block">Returns the round number that has ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundStartedEvent.html#getRound--">getRound()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a></dt>
<dd>
<div class="block">Returns the round number.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/ITurnSnapshot.html#getRound--">getRound()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></dt>
<dd>
<div class="block">Returns the current round of the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RoundEndedEvent.html#getRound--">getRound()</a></span> - Method in class robocode.<a href="robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a></dt>
<dd>
<div class="block">Returns the round that ended (zero-indexed).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getRoundNum--">getRoundNum()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the current round number (0 to <a href="robocode/Robot.html#getNumRounds--"><code>Robot.getNumRounds()</code></a> - 1) of
 the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getRoundNum--">getRoundNum()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the number of the current round (0 to <a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getNumRounds--"><code>IBasicRobotPeer.getNumRounds()</code></a> - 1)
 in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getRoundNum--">getRoundNum()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the current round number (0 to <a href="robocode/RobotStatus.html#getNumRounds--"><code>RobotStatus.getNumRounds()</code></a> - 1) of
 the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getScanColor--">getScanColor()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the color of the scan arc.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getScannedRobotEvents--">getScannedRobotEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all ScannedRobotEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getScannedRobotEvents--">getScannedRobotEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all ScannedRobotEvents currently in the
 robot's queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getScore--">getScore()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the total score of this robot in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getScoreSnapshot--">getScoreSnapshot()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns a snapshot of the current score for this robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getSeconds--">getSeconds()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the number of rounds this robot placed second in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MessageEvent.html#getSender--">getSender()</a></span> - Method in class robocode.<a href="robocode/MessageEvent.html" title="class in robocode">MessageEvent</a></dt>
<dd>
<div class="block">Returns the name of the sending robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleRules.html#getSentryBorderSize--">getSentryBorderSize()</a></span> - Method in class robocode.<a href="robocode/BattleRules.html" title="class in robocode">BattleRules</a></dt>
<dd>
<div class="block">Returns the sentry border size for a <a href="robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattleSpecification.html#getSentryBorderSize--">getSentryBorderSize()</a></span> - Method in class robocode.control.<a href="robocode/control/BattleSpecification.html" title="class in robocode.control">BattleSpecification</a></dt>
<dd>
<div class="block">Returns the sentry border size for a <a href="robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 border sentry robots cannot leave (they must stay in the border area), but it also define the
 distance from the border edges where border sentry robots are allowed/able to make damage to robots entering this
 border area.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getSentryBorderSize--">getSentryBorderSize()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the sentry border size for a <a href="robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getSentryBorderSize--">getSentryBorderSize()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the sentry border size for a <a href="robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a> that defines the how
 far a BorderSentry is allowed to move from the border edges measured in units.<br>
 Hence, the sentry border size defines the width/range of the border area surrounding the battlefield that
 BorderSentrys cannot leave (sentry robots robots must stay in the border area), but it also define the
 distance from the border edges where BorderSentrys are allowed/able to make damage to robots entering this
 border area.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getShortName--">getShortName()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the short name of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/SkippedTurnEvent.html#getSkippedTurn--">getSkippedTurn()</a></span> - Method in class robocode.<a href="robocode/SkippedTurnEvent.html" title="class in robocode">SkippedTurnEvent</a></dt>
<dd>
<div class="block">Returns the turn that was skipped.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleCompletedEvent.html#getSortedResults--">getSortedResults()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleCompletedEvent.html" title="class in robocode.control.events">BattleCompletedEvent</a></dt>
<dd>
<div class="block">Returns the battle results sorted on score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/ITurnSnapshot.html#getSortedTeamScores--">getSortedTeamScores()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></dt>
<dd>
<div class="block">Returns an array of sorted scores grouped by teams, ordered by position.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/KeyEvent.html#getSourceEvent--">getSourceEvent()</a></span> - Method in class robocode.<a href="robocode/KeyEvent.html" title="class in robocode">KeyEvent</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseEvent.html#getSourceEvent--">getSourceEvent()</a></span> - Method in class robocode.<a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundStartedEvent.html#getStartSnapshot--">getStartSnapshot()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a></dt>
<dd>
<div class="block">Returns the start snapshot of the participating robots, initial starting positions etc.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getState--">getState()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the bullet state.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getState--">getState()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the robot state.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/StatusEvent.html#getStatus--">getStatus()</a></span> - Method in class robocode.<a href="robocode/StatusEvent.html" title="class in robocode">StatusEvent</a></dt>
<dd>
<div class="block">Returns the <a href="robocode/RobotStatus.html" title="class in robocode"><code>RobotStatus</code></a> at the time defined by <a href="robocode/Robot.html#getTime--"><code>Robot.getTime()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getStatusEvents--">getStatusEvents()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns a vector containing all StatusEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#getStatusEvents--">getStatusEvents()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Returns a vector containing all StatusEvents currently in the robot's
 queue.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getSurvival--">getSurvival()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the survival score of this robot in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/ITeamRobot.html#getTeamEventListener--">getTeamEventListener()</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces">ITeamRobot</a></dt>
<dd>
<div class="block">This method is called by the game to notify this robot about team events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#getTeamEventListener--">getTeamEventListener()</a></span> - Method in class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getTeamId--">getTeamId()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns id of the team in current battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getTeamIndex--">getTeamIndex()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the index of the team that this robot is a member of, which is unique for the specific team and constant
 during a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getTeamLeaderName--">getTeamLeaderName()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the name of the team leader in the team or the name of the
 robot if the robot is not participating in a team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html#getTeammates--">getTeammates()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dt>
<dd>
<div class="block">Returns the names of all teammates, or <code>null</code> there is no
 teammates.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#getTeammates--">getTeammates()</a></span> - Method in class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>
<div class="block">Returns the names of all teammates, or <code>null</code> there is no
 teammates.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getTeamName--">getTeamName()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the name of the team, which can be the name of a robot if the contestant is not a team, but a robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#getThirds--">getThirds()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>
<div class="block">Returns the number of rounds this robot placed third in the battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Event.html#getTime--">getTime()</a></span> - Method in class robocode.<a href="robocode/Event.html" title="class in robocode">Event</a></dt>
<dd>
<div class="block">Returns the time when this event occurred.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getTime--">getTime()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the game time of the current round, where the time is equal to
 the current turn in the round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getTime--">getTime()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the game time of the current round, where the time is equal to
 the current turn in the round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getTime--">getTime()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the game time of the round, where the time is equal to the current turn in the round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalBulletDamageScore--">getTotalBulletDamageScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total bullet damage score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalBulletKillBonus--">getTotalBulletKillBonus()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total bullet kill bonus.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalFirsts--">getTotalFirsts()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total number of first places.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalLastSurvivorBonus--">getTotalLastSurvivorBonus()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total last survivor score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalRammingDamageScore--">getTotalRammingDamageScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total ramming damage score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalRammingKillBonus--">getTotalRammingKillBonus()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total ramming kill bonus.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalScore--">getTotalScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalSeconds--">getTotalSeconds()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total number of second places.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalSurvivalScore--">getTotalSurvivalScore()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total survival score.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IScoreSnapshot.html#getTotalThirds--">getTotalThirds()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot">IScoreSnapshot</a></dt>
<dd>
<div class="block">Returns the total number of third places.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundEndedEvent.html#getTotalTurns--">getTotalTurns()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a></dt>
<dd>
<div class="block">Returns the total number of turns reached in the battle when this round ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RoundEndedEvent.html#getTotalTurns--">getTotalTurns()</a></span> - Method in class robocode.<a href="robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a></dt>
<dd>
<div class="block">Returns the total number of turns reached in the battle when this round ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/ITurnSnapshot.html#getTPS--">getTPS()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></dt>
<dd>
<div class="block">Returns the current TPS (turns per second) rate.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/ITurnSnapshot.html#getTurn--">getTurn()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot">ITurnSnapshot</a></dt>
<dd>
<div class="block">Returns the current turn in the battle round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#getTurnRate--">getTurnRate()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Gets the robot's clockwise rotation per turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#getTurnRate-double-">getTurnRate(double)</a></span> - Static method in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">Returns the turn rate of a robot given a specific velocity measured in
 degrees/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#getTurnRateRadians--">getTurnRateRadians()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Gets the robot's clockwise rotation per turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#getTurnRateRadians-double-">getTurnRateRadians(double)</a></span> - Static method in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">Returns the turn rate of a robot given a specific velocity measured in
 radians/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getTurnRemaining--">getTurnRemaining()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the angle remaining in the robots's turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getTurnRemaining--">getTurnRemaining()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the angle remaining in the robots's turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#getTurnRemainingRadians--">getTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#getTurnRemainingRadians--">getTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Returns the angle remaining in the robot's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getTurnRemainingRadians--">getTurnRemainingRadians()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the angle remaining in the robots's turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundEndedEvent.html#getTurns--">getTurns()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a></dt>
<dd>
<div class="block">Returns the number of turns that this round reached.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RoundEndedEvent.html#getTurns--">getTurns()</a></span> - Method in class robocode.<a href="robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a></dt>
<dd>
<div class="block">Returns the number of turns that this round reached.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/TurnEndedEvent.html#getTurnSnapshot--">getTurnSnapshot()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events">TurnEndedEvent</a></dt>
<dd>
<div class="block">Returns a snapshot of the turn that has ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/BulletState.html#getValue--">getValue()</a></span> - Method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></dt>
<dd>
<div class="block">Returns the state as an integer value.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IDebugProperty.html#getValue--">getValue()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot">IDebugProperty</a></dt>
<dd>
<div class="block">Returns the value of the property.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#getValue--">getValue()</a></span> - Method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Returns the state as an integer value.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getVelocity--">getVelocity()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the velocity of this bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getVelocity--">getVelocity()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the velocity of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#getVelocity--">getVelocity()</a></span> - Method in class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Returns the velocity of this bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getVelocity--">getVelocity()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the velocity of the robot measured in pixels/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getVelocity--">getVelocity()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the velocity of the robot measured in pixels/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getVelocity--">getVelocity()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the velocity of the robot measured in pixels/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#getVelocity--">getVelocity()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Returns the velocity of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#getVelocityRate--">getVelocityRate()</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Returns the speed the robot will move, in pixels per turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#getVersion--">getVersion()</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Returns the installed version of Robocode controlled by this RobocodeEngine.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#getVersion--">getVersion()</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Returns the installed version of Robocode controlled by this RobocodeEngine.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getVersion--">getVersion()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the version of this robot or team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getVeryShortName--">getVeryShortName()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the very short name of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getVictim--">getVictim()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the name of the robot that this bullet hit, or <code>null</code> if
 the bullet has not hit a robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getVictimIndex--">getVictimIndex()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#getWaitCount--">getWaitCount()</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is no longer functional.
             Use <a href="robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-"><code>AdvancedRobot.onSkippedTurn(SkippedTurnEvent)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#getWallHitDamage-double-">getWallHitDamage(double)</a></span> - Static method in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">Returns the amount of damage taken when robot hits a wall with a
 specific velocity.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSpecification.html#getWebpage--">getWebpage()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSpecification.html" title="class in robocode.control">RobotSpecification</a></dt>
<dd>
<div class="block">Returns the link to the web page for this robot or team.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/BattlefieldSpecification.html#getWidth--">getWidth()</a></span> - Method in class robocode.control.<a href="robocode/control/BattlefieldSpecification.html" title="class in robocode.control">BattlefieldSpecification</a></dt>
<dd>
<div class="block">Returns the width of this battlefield.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getWidth--">getWidth()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the width of the robot measured in pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getX--">getX()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the X position of the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSetup.html#getX--">getX()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a></dt>
<dd>
<div class="block">Returns the x coordinate.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getX--">getX()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the X position of the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getX--">getX()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the X position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getX--">getX()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the X position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getX--">getX()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the X position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getX--">getX()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the X position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#getY--">getY()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Returns the Y position of the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSetup.html#getY--">getY()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a></dt>
<dd>
<div class="block">Returns the y coordinate.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#getY--">getY()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Returns the Y position of the bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#getY--">getY()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Returns the Y position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#getY--">getY()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Returns the Y position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#getY--">getY()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Returns the Y position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotStatus.html#getY--">getY()</a></span> - Method in class robocode.<a href="robocode/RobotStatus.html" title="class in robocode">RobotStatus</a></dt>
<dd>
<div class="block">Returns the Y position of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#gray">gray</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color gray (0x808080)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#green">green</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color green (0x008000)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#GUN_TURN_RATE">GUN_TURN_RATE</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The turning rate of the gun measured in degrees, which is
 20 degrees/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#GUN_TURN_RATE_RADIANS">GUN_TURN_RATE_RADIANS</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The turning rate of the gun measured in radians instead of degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#gunBearing">gunBearing</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current gun heading angle of this robot compared to its body (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#gunHeading">gunHeading</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current gun heading angle of this robot (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#gunReady">gunReady</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Flag specifying if the gun is ready to fire, i.e.</div>
</dd>
<dt><a href="robocode/GunTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">GunTurnCompleteCondition</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A prebuilt condition you can use that indicates your gun has finished
 turning.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/GunTurnCompleteCondition.html#GunTurnCompleteCondition-robocode.AdvancedRobot-">GunTurnCompleteCondition(AdvancedRobot)</a></span> - Constructor for class robocode.<a href="robocode/GunTurnCompleteCondition.html" title="class in robocode">GunTurnCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new GunTurnCompleteCondition with default priority.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/GunTurnCompleteCondition.html#GunTurnCompleteCondition-robocode.AdvancedRobot-int-">GunTurnCompleteCondition(AdvancedRobot, int)</a></span> - Constructor for class robocode.<a href="robocode/GunTurnCompleteCondition.html" title="class in robocode">GunTurnCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new GunTurnCompleteCondition with a specific priority.</div>
</dd>
</dl>
<a name="I:H">
<!--   -->
</a>
<h2 class="title">H</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#hashCode--">hashCode()</a></span> - Method in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#hashCode--">hashCode()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotResults.html#hashCode--">hashCode()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#heading">heading</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current heading angle of this robot (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#hitByBulletAngle">hitByBulletAngle</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Latest angle from where this robot was hit by a bullet (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#hitByBulletBearing">hitByBulletBearing</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Latest angle from where this robot was hit by a bullet (in degrees)
 compared to the body of this robot.</div>
</dd>
<dt><a href="robocode/HitByBulletEvent.html" title="class in robocode"><span class="typeNameLink">HitByBulletEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A HitByBulletEvent is sent to <a href="robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-"><code>onHitByBullet()</code></a> when your robot has been hit by a bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitByBulletEvent.html#HitByBulletEvent-double-robocode.Bullet-">HitByBulletEvent(double, Bullet)</a></span> - Constructor for class robocode.<a href="robocode/HitByBulletEvent.html" title="class in robocode">HitByBulletEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new HitByBulletEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#hitRobotAngle">hitRobotAngle</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Latest angle where this robot has hit another robot (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#hitRobotBearing">hitRobotBearing</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Latest angle where this robot has hit another robot (in degrees)
 compared to the body of this robot.</div>
</dd>
<dt><a href="robocode/HitRobotEvent.html" title="class in robocode"><span class="typeNameLink">HitRobotEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A HitRobotEvent is sent to <a href="robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-"><code>onHitRobot()</code></a>
 when your robot collides with another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#HitRobotEvent-java.lang.String-double-double-boolean-">HitRobotEvent(String, double, double, boolean)</a></span> - Constructor for class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new HitRobotEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#hitWallAngle">hitWallAngle</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Latest angle where this robot has hit a wall (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#hitWallBearing">hitWallBearing</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Latest angle where this robot has hit a wall (in degrees)
 compared to the body of this robot.</div>
</dd>
<dt><a href="robocode/HitWallEvent.html" title="class in robocode"><span class="typeNameLink">HitWallEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A HitWallEvent is sent to <a href="robocode/Robot.html#onHitWall-robocode.HitWallEvent-"><code>onHitWall()</code></a>
 when you collide a wall.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitWallEvent.html#HitWallEvent-double-">HitWallEvent(double)</a></span> - Constructor for class robocode.<a href="robocode/HitWallEvent.html" title="class in robocode">HitWallEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new HitWallEvent.</div>
</dd>
</dl>
<a name="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><a href="robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IAdvancedEvents</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">An event interface for receiving advanced robot events with an
 <a href="robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><code>IAdvancedRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IAdvancedRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IAdvancedRobot</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">A robot interface for creating a more advanced type of robot like
 <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> that is able to handle advanced robot events.</div>
</dd>
<dt><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IAdvancedRobotPeer</span></a> - Interface in <a href="robocode/robotinterfaces/peer/package-summary.html">robocode.robotinterfaces.peer</a></dt>
<dd>
<div class="block">The advanced robot peer for advanced robot types like
 <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> and <a href="robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicEvents</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">An event interface for receiving basic robot events with an
 <a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><code>IBasicRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicEvents2</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">First extended version of the <a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><code>IBasicEvents</code></a> interface.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicEvents3</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">Second extended version of the <a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces"><code>IBasicEvents</code></a> interface.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IBasicRobot</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">A robot interface for creating a basic type of robot like <a href="robocode/Robot.html" title="class in robocode"><code>Robot</code></a>
 that is able to receive common robot events, but not interactive events as
 with the <a href="robocode/Robot.html" title="class in robocode"><code>Robot</code></a> class.</div>
</dd>
<dt><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IBasicRobotPeer</span></a> - Interface in <a href="robocode/robotinterfaces/peer/package-summary.html">robocode.robotinterfaces.peer</a></dt>
<dd>
<div class="block">The basic robot peer for all robot types.</div>
</dd>
<dt><a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><span class="typeNameLink">IBattleListener</span></a> - Interface in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">The listener interface for receiving "interesting" battle events from the game, e.g.</div>
</dd>
<dt><a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IBulletSnapshot</span></a> - Interface in <a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a></dt>
<dd>
<div class="block">Interface of a bullet snapshot at a specific time in a battle.</div>
</dd>
<dt><a href="robocode/control/snapshot/IDebugProperty.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IDebugProperty</span></a> - Interface in <a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a></dt>
<dd>
<div class="block">Interface of a debug property, which is a key-value pair.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IInteractiveEvents</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">An event interface for receiving interactive events with an
 <a href="robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><code>IInteractiveRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IInteractiveRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IInteractiveRobot</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">A robot interface for creating an interactive type of robot like
 <a href="robocode/Robot.html" title="class in robocode"><code>Robot</code></a> and <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> that is able to
 receive interactive events from the keyboard or mouse.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IJuniorRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IJuniorRobot</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">A robot interface for creating the most primitive robot type, which is a
 <a href="robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IJuniorRobotPeer</span></a> - Interface in <a href="robocode/robotinterfaces/peer/package-summary.html">robocode.robotinterfaces.peer</a></dt>
<dd>
<div class="block">The junior robot peer for junior robot types like <a href="robocode/JuniorRobot.html" title="class in robocode"><code>JuniorRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IPaintEvents</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">An event interface for receiving paint events with an
 <a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><code>IPaintRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">IPaintRobot</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">A robot interface that makes it possible for a robot to receive paint events.</div>
</dd>
<dt><a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control"><span class="typeNameLink">IRobocodeEngine</span></a> - Interface in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">Interface for the RobocodeEngine.</div>
</dd>
<dt><a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IRobotSnapshot</span></a> - Interface in <a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a></dt>
<dd>
<div class="block">Interface of a robot snapshot at a specific time in a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleEndedEvent.html#isAborted--">isAborted()</a></span> - Method in class robocode.<a href="robocode/BattleEndedEvent.html" title="class in robocode">BattleEndedEvent</a></dt>
<dd>
<div class="block">Checks if this battle was aborted.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleFinishedEvent.html#isAborted--">isAborted()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleFinishedEvent.html" title="class in robocode.control.events">BattleFinishedEvent</a></dt>
<dd>
<div class="block">Checks if the battle was aborted.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#isActive--">isActive()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>
<div class="block">Checks if this bullet is still active on the battlefield.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/BulletState.html#isActive--">isActive()</a></span> - Method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustGunForBodyTurn--">isAdjustGunForBodyTurn()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Checks if the gun is set to adjust for the robot turning, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#isAdjustGunForRobotTurn--">isAdjustGunForRobotTurn()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Checks if the gun is set to adjust for the robot turning, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForBodyTurn--">isAdjustRadarForBodyTurn()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Checks if the radar is set to adjust for the gun turning, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#isAdjustRadarForGunTurn--">isAdjustRadarForGunTurn()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Checks if the radar is set to adjust for the gun turning, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#isAdjustRadarForGunTurn--">isAdjustRadarForGunTurn()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Checks if the radar is set to adjust for the robot turning, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#isAdjustRadarForRobotTurn--">isAdjustRadarForRobotTurn()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Checks if the radar is set to adjust for the robot turning, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#isAlive--">isAlive()</a></span> - Method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Checks if the robot is alive.</div>
</dd>
<dt><a href="robocode/control/snapshot/IScoreSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">IScoreSnapshot</span></a> - Interface in <a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a></dt>
<dd>
<div class="block">Interface of a score snapshot at a specific time in a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#isDead--">isDead()</a></span> - Method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Checks if the robot is dead.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RandomFactory.html#isDeterministic--">isDeterministic()</a></span> - Method in class robocode.control.<a href="robocode/control/RandomFactory.html" title="class in robocode.control">RandomFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isDeterministic--">isDeterministic()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Defaults to true, indicating that the battle is deterministic and robots will always start
 in the same position each time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#isDroid--">isDroid()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Checks if this robot is a <a href="robocode/Droid.html" title="interface in robocode"><code>Droid</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isDumpingErrors--">isDumpingErrors()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Gets whether error messages should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isDumpingMessages--">isDumpingMessages()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Gets whether robot messages should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isDumpingOutput--">isDumpingOutput()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Gets whether robot output should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isDumpingPositions--">isDumpingPositions()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Gets whether robot positions should be printed during each turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isDumpingTurns--">isDumpingTurns()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Gets whether each turn should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isEnableRecording--">isEnableRecording()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#isEnableScreenshots--">isEnableScreenshots()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IBulletSnapshot.html#isExplosion--">isExplosion()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IBulletSnapshot.html" title="interface in robocode.control.snapshot">IBulletSnapshot</a></dt>
<dd>
<div class="block">Checks if the bullet has become an explosion, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#isHitRobot--">isHitRobot()</a></span> - Method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Checks if the robot has hit another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#isHitWall--">isHitWall()</a></span> - Method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Checks if the robot has hit the wall, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/HitRobotEvent.html#isMyFault--">isMyFault()</a></span> - Method in class robocode.<a href="robocode/HitRobotEvent.html" title="class in robocode">HitRobotEvent</a></dt>
<dd>
<div class="block">Checks if your robot was moving towards the robot that was hit.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#isNear-double-double-">isNear(double, double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Tests if the two <code>double</code> values are near to each other.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#isPaintEnabled--">isPaintEnabled()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Checks if painting is enabled for this robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#isPaintRobot--">isPaintRobot()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Checks if this robot is a <a href="robocode/robotinterfaces/IPaintRobot.html" title="interface in robocode.robotinterfaces"><code>IPaintRobot</code></a> or is invoking getGraphics()</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleStartedEvent.html#isReplay--">isReplay()</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleStartedEvent.html" title="class in robocode.control.events">BattleStartedEvent</a></dt>
<dd>
<div class="block">Checks if this battle is a replay or a new battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#isSentryRobot--">isSentryRobot()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Checks if this robot is a <a href="robocode/BorderSentry.html" title="interface in robocode"><code>BorderSentry</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#isSentryRobot--">isSentryRobot()</a></span> - Method in class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Checks if the scanned robot is a sentry robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/IRobotSnapshot.html#isSGPaintEnabled--">isSGPaintEnabled()</a></span> - Method in interface robocode.control.snapshot.<a href="robocode/control/snapshot/IRobotSnapshot.html" title="interface in robocode.control.snapshot">IRobotSnapshot</a></dt>
<dd>
<div class="block">Checks if RobocodeSG painting (the point (0,0) is in the upper left corner) is enabled for this robot.</div>
</dd>
<dt><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">IStandardRobotPeer</span></a> - Interface in <a href="robocode/robotinterfaces/peer/package-summary.html">robocode.robotinterfaces.peer</a></dt>
<dd>
<div class="block">The standard robot peer for standard robot types like <a href="robocode/Robot.html" title="class in robocode"><code>Robot</code></a>,
 <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, and <a href="robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html#isTeammate-java.lang.String-">isTeammate(String)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dt>
<dd>
<div class="block">Checks if a given robot name is the name of one of your teammates.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#isTeammate-java.lang.String-">isTeammate(String)</a></span> - Method in class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>
<div class="block">Checks if a given robot name is the name of one of your teammates.</div>
</dd>
<dt><a href="robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">ITeamEvents</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">An event interface for receiving robot team events with an
 <a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces"><code>ITeamRobot</code></a>.</div>
</dd>
<dt><a href="robocode/robotinterfaces/ITeamRobot.html" title="interface in robocode.robotinterfaces"><span class="typeNameLink">ITeamRobot</span></a> - Interface in <a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a></dt>
<dd>
<div class="block">A robot interface for creating a team robot like <a href="robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>
 that is able to receive team events.</div>
</dd>
<dt><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer"><span class="typeNameLink">ITeamRobotPeer</span></a> - Interface in <a href="robocode/robotinterfaces/peer/package-summary.html">robocode.robotinterfaces.peer</a></dt>
<dd>
<div class="block">The team robot peer for team robots like <a href="robocode/TeamRobot.html" title="class in robocode"><code>TeamRobot</code></a>.</div>
</dd>
<dt><a href="robocode/control/snapshot/ITurnSnapshot.html" title="interface in robocode.control.snapshot"><span class="typeNameLink">ITurnSnapshot</span></a> - Interface in <a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a></dt>
<dd>
<div class="block">Interface of a battle turn snapshot at a specific time in a battle.</div>
</dd>
</dl>
<a name="I:J">
<!--   -->
</a>
<h2 class="title">J</h2>
<dl>
<dt><a href="robocode/JuniorRobot.html" title="class in robocode"><span class="typeNameLink">JuniorRobot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This is the simplest robot type, which is simpler than the <a href="robocode/Robot.html" title="class in robocode"><code>Robot</code></a> and
 <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a> classes.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#JuniorRobot--">JuniorRobot()</a></span> - Constructor for class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:K">
<!--   -->
</a>
<h2 class="title">K</h2>
<dl>
<dt><a href="robocode/KeyEvent.html" title="class in robocode"><span class="typeNameLink">KeyEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Super class of all events that originates from the keyboard.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/KeyEvent.html#KeyEvent-java.awt.event.KeyEvent-">KeyEvent(KeyEvent)</a></span> - Constructor for class robocode.<a href="robocode/KeyEvent.html" title="class in robocode">KeyEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new KeyEvent.</div>
</dd>
<dt><a href="robocode/KeyPressedEvent.html" title="class in robocode"><span class="typeNameLink">KeyPressedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A KeyPressedEvent is sent to <a href="robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-"><code>onKeyPressed()</code></a> when a key has been pressed on the keyboard.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/KeyPressedEvent.html#KeyPressedEvent-java.awt.event.KeyEvent-">KeyPressedEvent(KeyEvent)</a></span> - Constructor for class robocode.<a href="robocode/KeyPressedEvent.html" title="class in robocode">KeyPressedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new KeyPressedEvent.</div>
</dd>
<dt><a href="robocode/KeyReleasedEvent.html" title="class in robocode"><span class="typeNameLink">KeyReleasedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A KeyReleasedEvent is sent to <a href="robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-"><code>onKeyReleased()</code></a> when a key has been released on the keyboard.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/KeyReleasedEvent.html#KeyReleasedEvent-java.awt.event.KeyEvent-">KeyReleasedEvent(KeyEvent)</a></span> - Constructor for class robocode.<a href="robocode/KeyReleasedEvent.html" title="class in robocode">KeyReleasedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new KeyReleasedEvent.</div>
</dd>
<dt><a href="robocode/KeyTypedEvent.html" title="class in robocode"><span class="typeNameLink">KeyTypedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A KeyTypedEvent is sent to <a href="robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-"><code>onKeyTyped()</code></a> when a key has been typed (pressed and released) on the keyboard.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/KeyTypedEvent.html#KeyTypedEvent-java.awt.event.KeyEvent-">KeyTypedEvent(KeyEvent)</a></span> - Constructor for class robocode.<a href="robocode/KeyTypedEvent.html" title="class in robocode">KeyTypedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new KeyTypedEvent.</div>
</dd>
</dl>
<a name="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#lastError">lastError</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#lastSurvivorBonus">lastSurvivorBonus</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:M">
<!--   -->
</a>
<h2 class="title">M</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/Robocode.html#main-java.lang.String:A-">main(String[])</a></span> - Static method in class robocode.<a href="robocode/Robocode.html" title="class in robocode">Robocode</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#MAX_BULLET_POWER">MAX_BULLET_POWER</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The maximum bullet power, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#MAX_TURN_RATE">MAX_TURN_RATE</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The maximum turning rate of the robot, in degrees, which is
 10 degress/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#MAX_TURN_RATE_RADIANS">MAX_TURN_RATE_RADIANS</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The maximum turning rate of the robot measured in radians instead of
 degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#MAX_VELOCITY">MAX_VELOCITY</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The maximum velocity of a robot, which is 8 pixels/turn.</div>
</dd>
<dt><a href="robocode/MessageEvent.html" title="class in robocode"><span class="typeNameLink">MessageEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MessageEvent is sent to <a href="robocode/TeamRobot.html#onMessageReceived-robocode.MessageEvent-"><code>onMessageReceived()</code></a> when a teammate sends a message to your robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MessageEvent.html#MessageEvent-java.lang.String-java.io.Serializable-">MessageEvent(String, Serializable)</a></span> - Constructor for class robocode.<a href="robocode/MessageEvent.html" title="class in robocode">MessageEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MessageEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#messages">messages</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">The number of messages generated during this battle so far.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#MIN_BULLET_POWER">MIN_BULLET_POWER</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The minimum bullet power, i.e the amount of energy required for firing a
 bullet, which is 0.1 energy points.</div>
</dd>
<dt><a href="robocode/MouseClickedEvent.html" title="class in robocode"><span class="typeNameLink">MouseClickedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MouseClickedEvent is sent to <a href="robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-"><code>onMouseClicked()</code></a> when the mouse is clicked inside the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseClickedEvent.html#MouseClickedEvent-java.awt.event.MouseEvent-">MouseClickedEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseClickedEvent.html" title="class in robocode">MouseClickedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseClickedEvent.</div>
</dd>
<dt><a href="robocode/MouseDraggedEvent.html" title="class in robocode"><span class="typeNameLink">MouseDraggedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MouseDraggedEvent is sent to <a href="robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-"><code>onMouseDragged()</code></a> when the mouse is dragged inside the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseDraggedEvent.html#MouseDraggedEvent-java.awt.event.MouseEvent-">MouseDraggedEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseDraggedEvent.html" title="class in robocode">MouseDraggedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseDraggedEvent.</div>
</dd>
<dt><a href="robocode/MouseEnteredEvent.html" title="class in robocode"><span class="typeNameLink">MouseEnteredEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MouseEnteredEvent is sent to <a href="robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-"><code>onMouseEntered()</code></a> when the mouse has entered the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseEnteredEvent.html#MouseEnteredEvent-java.awt.event.MouseEvent-">MouseEnteredEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseEnteredEvent.html" title="class in robocode">MouseEnteredEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseEnteredEvent.</div>
</dd>
<dt><a href="robocode/MouseEvent.html" title="class in robocode"><span class="typeNameLink">MouseEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Super class of all events that originates from the mouse.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseEvent.html#MouseEvent-java.awt.event.MouseEvent-">MouseEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseEvent.html" title="class in robocode">MouseEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseEvent.</div>
</dd>
<dt><a href="robocode/MouseExitedEvent.html" title="class in robocode"><span class="typeNameLink">MouseExitedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MouseExitedEvent is sent to <a href="robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-"><code>onMouseExited()</code></a> when the mouse has exited the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseExitedEvent.html#MouseExitedEvent-java.awt.event.MouseEvent-">MouseExitedEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseExitedEvent.html" title="class in robocode">MouseExitedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseExitedEvent.</div>
</dd>
<dt><a href="robocode/MouseMovedEvent.html" title="class in robocode"><span class="typeNameLink">MouseMovedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MouseMovedEvent is sent to <a href="robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-"><code>onMouseMoved()</code></a> when the mouse has moved inside the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseMovedEvent.html#MouseMovedEvent-java.awt.event.MouseEvent-">MouseMovedEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseMovedEvent.html" title="class in robocode">MouseMovedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseMovedEvent.</div>
</dd>
<dt><a href="robocode/MousePressedEvent.html" title="class in robocode"><span class="typeNameLink">MousePressedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MousePressedEvent is sent to <a href="robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-"><code>onMousePressed()</code></a> when the mouse is pressed inside the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MousePressedEvent.html#MousePressedEvent-java.awt.event.MouseEvent-">MousePressedEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MousePressedEvent.html" title="class in robocode">MousePressedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MousePressedEvent.</div>
</dd>
<dt><a href="robocode/MouseReleasedEvent.html" title="class in robocode"><span class="typeNameLink">MouseReleasedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MouseReleasedEvent is sent to <a href="robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-"><code>onMouseReleased()</code></a> when the mouse is released inside the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseReleasedEvent.html#MouseReleasedEvent-java.awt.event.MouseEvent-">MouseReleasedEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseReleasedEvent.html" title="class in robocode">MouseReleasedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseReleasedEvent.</div>
</dd>
<dt><a href="robocode/MouseWheelMovedEvent.html" title="class in robocode"><span class="typeNameLink">MouseWheelMovedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A MouseWheelMovedEvent is sent to <a href="robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-"><code>onMouseWheelMoved()</code></a> when the mouse wheel is rotated inside the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MouseWheelMovedEvent.html#MouseWheelMovedEvent-java.awt.event.MouseEvent-">MouseWheelMovedEvent(MouseEvent)</a></span> - Constructor for class robocode.<a href="robocode/MouseWheelMovedEvent.html" title="class in robocode">MouseWheelMovedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new MouseWheelMovedEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#move-double-">move(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Immediately moves your robot forward or backward by distance measured in
 pixels.</div>
</dd>
<dt><a href="robocode/MoveCompleteCondition.html" title="class in robocode"><span class="typeNameLink">MoveCompleteCondition</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A prebuilt condition you can use that indicates your robot has finished
 moving.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MoveCompleteCondition.html#MoveCompleteCondition-robocode.AdvancedRobot-">MoveCompleteCondition(AdvancedRobot)</a></span> - Constructor for class robocode.<a href="robocode/MoveCompleteCondition.html" title="class in robocode">MoveCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new MoveCompleteCondition with default priority.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MoveCompleteCondition.html#MoveCompleteCondition-robocode.AdvancedRobot-int-">MoveCompleteCondition(AdvancedRobot, int)</a></span> - Constructor for class robocode.<a href="robocode/MoveCompleteCondition.html" title="class in robocode">MoveCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new MoveCompleteCondition with the specified priority.</div>
</dd>
</dl>
<a name="I:N">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#name">name</a></span> - Variable in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">The name of this condition.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#NEAR_DELTA">NEAR_DELTA</a></span> - Static variable in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#normalAbsoluteAngle-double-">normalAbsoluteAngle(double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Normalizes an angle to an absolute angle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#normalAbsoluteAngleDegrees-double-">normalAbsoluteAngleDegrees(double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Normalizes an angle to an absolute angle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#normalNearAbsoluteAngle-double-">normalNearAbsoluteAngle(double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Normalizes an angle to be near an absolute angle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#normalNearAbsoluteAngleDegrees-double-">normalNearAbsoluteAngleDegrees(double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Normalizes an angle to be near an absolute angle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#normalRelativeAngle-double-">normalRelativeAngle(double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Normalizes an angle to a relative angle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/util/Utils.html#normalRelativeAngleDegrees-double-">normalRelativeAngleDegrees(double)</a></span> - Static method in class robocode.util.<a href="robocode/util/Utils.html" title="class in robocode.util">Utils</a></dt>
<dd>
<div class="block">Normalizes an angle to a relative angle.</div>
</dd>
</dl>
<a name="I:O">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-">onBattleCompleted(BattleCompletedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the battle has completed successfully and results are available.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onBattleCompleted-robocode.control.events.BattleCompletedEvent-">onBattleCompleted(BattleCompletedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the battle has completed successfully and results are available.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded(BattleEndedEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called after the end of the battle, even when the battle is aborted.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents2.html#onBattleEnded-robocode.BattleEndedEvent-">onBattleEnded(BattleEndedEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents2.html" title="interface in robocode.robotinterfaces">IBasicEvents2</a></dt>
<dd>
<div class="block">This method is called after the end of the battle, even when the battle is aborted.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onBattleError-robocode.control.events.BattleErrorEvent-">onBattleError(BattleErrorEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the game has sent an error message.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onBattleError-robocode.control.events.BattleErrorEvent-">onBattleError(BattleErrorEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the game has sent an error message.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-">onBattleFinished(BattleFinishedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the battle has finished.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onBattleFinished-robocode.control.events.BattleFinishedEvent-">onBattleFinished(BattleFinishedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the battle has finished.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onBattleMessage-robocode.control.events.BattleMessageEvent-">onBattleMessage(BattleMessageEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the game has sent a new information message.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onBattleMessage-robocode.control.events.BattleMessageEvent-">onBattleMessage(BattleMessageEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the game has sent a new information message.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onBattlePaused-robocode.control.events.BattlePausedEvent-">onBattlePaused(BattlePausedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the battle has been paused, either by the user or the game.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onBattlePaused-robocode.control.events.BattlePausedEvent-">onBattlePaused(BattlePausedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the battle has been paused, either by the user or the game.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onBattleResumed-robocode.control.events.BattleResumedEvent-">onBattleResumed(BattleResumedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the battle has been resumed (after having been paused).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onBattleResumed-robocode.control.events.BattleResumedEvent-">onBattleResumed(BattleResumedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the battle has been resumed (after having been paused).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onBattleStarted-robocode.control.events.BattleStartedEvent-">onBattleStarted(BattleStartedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when a new battle has started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onBattleStarted-robocode.control.events.BattleStartedEvent-">onBattleStarted(BattleStartedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when a new battle has started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit(BulletHitEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when one of your bullets hits another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onBulletHit-robocode.BulletHitEvent-">onBulletHit(BulletHitEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when one of your bullets hits another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet(BulletHitBulletEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when one of your bullets hits another bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onBulletHitBullet-robocode.BulletHitBulletEvent-">onBulletHitBullet(BulletHitBulletEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when one of your bullets hits another bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed(BulletMissedEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when one of your bullets misses, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onBulletMissed-robocode.BulletMissedEvent-">onBulletMissed(BulletMissedEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when one of your bullets misses, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#onCustomEvent-robocode.CustomEvent-">onCustomEvent(CustomEvent)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">This method is called when a custom condition is met.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IAdvancedEvents.html#onCustomEvent-robocode.CustomEvent-">onCustomEvent(CustomEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a></dt>
<dd>
<div class="block">This method is called when a custom condition is met.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#onDeath-robocode.DeathEvent-">onDeath(DeathEvent)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">This method is called if your robot dies.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onDeath-robocode.DeathEvent-">onDeath(DeathEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called if your robot dies.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onDeath-robocode.DeathEvent-">onDeath(DeathEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called if your robot dies.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#onHitByBullet--">onHitByBullet()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">This event methods is called from the game when this robot has been hit
 by another robot's bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet(HitByBulletEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when your robot is hit by a bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onHitByBullet-robocode.HitByBulletEvent-">onHitByBullet(HitByBulletEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when your robot is hit by a bullet.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#onHitRobot--">onHitRobot()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">This event methods is called from the game when a bullet from this robot
 has hit another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot(HitRobotEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when your robot collides with another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onHitRobot-robocode.HitRobotEvent-">onHitRobot(HitRobotEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when your robot collides with another robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#onHitWall--">onHitWall()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">This event methods is called from the game when this robot has hit a wall.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onHitWall-robocode.HitWallEvent-">onHitWall(HitWallEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when your robot collides with a wall.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onHitWall-robocode.HitWallEvent-">onHitWall(HitWallEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when your robot collides with a wall.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed(KeyEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when a key has been pressed.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onKeyPressed-java.awt.event.KeyEvent-">onKeyPressed(KeyEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when a key has been pressed.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased(KeyEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when a key has been released.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onKeyReleased-java.awt.event.KeyEvent-">onKeyReleased(KeyEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when a key has been released.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped(KeyEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when a key has been typed (pressed and released).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onKeyTyped-java.awt.event.KeyEvent-">onKeyTyped(KeyEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when a key has been typed (pressed and released).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/ITeamEvents.html#onMessageReceived-robocode.MessageEvent-">onMessageReceived(MessageEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/ITeamEvents.html" title="interface in robocode.robotinterfaces">ITeamEvents</a></dt>
<dd>
<div class="block">This method is called when your robot receives a message from a teammate.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#onMessageReceived-robocode.MessageEvent-">onMessageReceived(MessageEvent)</a></span> - Method in class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>
<div class="block">This method is called when your robot receives a message from a teammate.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked(MouseEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been clicked (pressed and
 released).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMouseClicked-java.awt.event.MouseEvent-">onMouseClicked(MouseEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been clicked (pressed and
 released).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged(MouseEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been pressed and then
 dragged.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMouseDragged-java.awt.event.MouseEvent-">onMouseDragged(MouseEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been pressed and then
 dragged.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered(MouseEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when the mouse has entered the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMouseEntered-java.awt.event.MouseEvent-">onMouseEntered(MouseEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when the mouse has entered the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited(MouseEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when the mouse has exited the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMouseExited-java.awt.event.MouseEvent-">onMouseExited(MouseEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when the mouse has exited the battle view.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved(MouseEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when the mouse has been moved.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMouseMoved-java.awt.event.MouseEvent-">onMouseMoved(MouseEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when the mouse has been moved.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed(MouseEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been pressed.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMousePressed-java.awt.event.MouseEvent-">onMousePressed(MouseEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been pressed.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased(MouseEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been released.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMouseReleased-java.awt.event.MouseEvent-">onMouseReleased(MouseEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when a mouse button has been released.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved(MouseWheelEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when the mouse wheel has been rotated.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IInteractiveEvents.html#onMouseWheelMoved-java.awt.event.MouseWheelEvent-">onMouseWheelMoved(MouseWheelEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IInteractiveEvents.html" title="interface in robocode.robotinterfaces">IInteractiveEvents</a></dt>
<dd>
<div class="block">This method is called when the mouse wheel has been rotated.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onPaint-java.awt.Graphics2D-">onPaint(Graphics2D)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called every time the robot is painted.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IPaintEvents.html#onPaint-java.awt.Graphics2D-">onPaint(Graphics2D)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IPaintEvents.html" title="interface in robocode.robotinterfaces">IPaintEvents</a></dt>
<dd>
<div class="block">This method is called every time the robot is painted.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath(RobotDeathEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when another robot dies.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onRobotDeath-robocode.RobotDeathEvent-">onRobotDeath(RobotDeathEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when another robot dies.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onRoundEnded-robocode.control.events.RoundEndedEvent-">onRoundEnded(RoundEndedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the current round of a battle has ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onRoundEnded-robocode.control.events.RoundEndedEvent-">onRoundEnded(RoundEndedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the current round of a battle has ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded(RoundEndedEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called after the end of a round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents3.html#onRoundEnded-robocode.RoundEndedEvent-">onRoundEnded(RoundEndedEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents3.html" title="interface in robocode.robotinterfaces">IBasicEvents3</a></dt>
<dd>
<div class="block">This method is called after the end of a round.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onRoundStarted-robocode.control.events.RoundStartedEvent-">onRoundStarted(RoundStartedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when a new round in a battle has started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onRoundStarted-robocode.control.events.RoundStartedEvent-">onRoundStarted(RoundStartedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when a new round in a battle has started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#onScannedRobot--">onScannedRobot()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">This event method is called from the game when the radar detects another
 robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot(ScannedRobotEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called when your robot sees another robot, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onScannedRobot-robocode.ScannedRobotEvent-">onScannedRobot(ScannedRobotEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called when your robot sees another robot, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-">onSkippedTurn(SkippedTurnEvent)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">This method is called if the robot is using too much time between
 actions.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IAdvancedEvents.html#onSkippedTurn-robocode.SkippedTurnEvent-">onSkippedTurn(SkippedTurnEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IAdvancedEvents.html" title="interface in robocode.robotinterfaces">IAdvancedEvents</a></dt>
<dd>
<div class="block">This method is called if the robot is using too much time between
 actions.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onStatus-robocode.StatusEvent-">onStatus(StatusEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called every turn in a battle round in order to provide
 the robot status as a complete snapshot of the robot's current state at
 that specific time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onStatus-robocode.StatusEvent-">onStatus(StatusEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called every turn in a battle round in order to provide
 the robot status as a complete snapshot of the robot's current state at
 that specific time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onTurnEnded-robocode.control.events.TurnEndedEvent-">onTurnEnded(TurnEndedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when the current turn in a battle round is ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onTurnEnded-robocode.control.events.TurnEndedEvent-">onTurnEnded(TurnEndedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when the current turn in a battle round is ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/BattleAdaptor.html#onTurnStarted-robocode.control.events.TurnStartedEvent-">onTurnStarted(TurnStartedEvent)</a></span> - Method in class robocode.control.events.<a href="robocode/control/events/BattleAdaptor.html" title="class in robocode.control.events">BattleAdaptor</a></dt>
<dd>
<div class="block">This method is called when a new turn in a battle round has started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/IBattleListener.html#onTurnStarted-robocode.control.events.TurnStartedEvent-">onTurnStarted(TurnStartedEvent)</a></span> - Method in interface robocode.control.events.<a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events">IBattleListener</a></dt>
<dd>
<div class="block">This method is called when a new turn in a battle round has started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#onWin-robocode.WinEvent-">onWin(WinEvent)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">This method is called if your robot wins a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicEvents.html#onWin-robocode.WinEvent-">onWin(WinEvent)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicEvents.html" title="interface in robocode.robotinterfaces">IBasicEvents</a></dt>
<dd>
<div class="block">This method is called if your robot wins a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#orange">orange</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color orange (0xFFA500)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#others">others</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current number of other robots on the battle field.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_RobotBase.html#out">out</a></span> - Variable in class robocode.<a href="robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></dt>
<dd>
<div class="block">The output stream your robot should use to print.</div>
</dd>
</dl>
<a name="I:P">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><a href="robocode/PaintEvent.html" title="class in robocode"><span class="typeNameLink">PaintEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event occurs when your robot should paint, where the <a href="robocode/Robot.html#onPaint-java.awt.Graphics2D-"><code>onPaint()</code></a> is called on your robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/PaintEvent.html#PaintEvent--">PaintEvent()</a></span> - Constructor for class robocode.<a href="robocode/PaintEvent.html" title="class in robocode">PaintEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new PaintEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#printRunningThreads--">printRunningThreads()</a></span> - Static method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Prints out all running threads to standard system out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#priority">priority</a></span> - Variable in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">The priority of this condition.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#purple">purple</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color purple (0x800080)</div>
</dd>
</dl>
<a name="I:R">
<!--   -->
</a>
<h2 class="title">R</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#RADAR_SCAN_RADIUS">RADAR_SCAN_RADIUS</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The radar scan radius, which is 1200 pixels.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#RADAR_TURN_RATE">RADAR_TURN_RATE</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The turning rate of the radar measured in degrees, which is
 45 degrees/turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#RADAR_TURN_RATE_RADIANS">RADAR_TURN_RATE_RADIANS</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The turning rate of the radar measured in radians instead of degrees.</div>
</dd>
<dt><a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">RadarTurnCompleteCondition</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A prebuilt condition you can use that indicates your radar has finished
 turning.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RadarTurnCompleteCondition.html#RadarTurnCompleteCondition-robocode.AdvancedRobot-">RadarTurnCompleteCondition(AdvancedRobot)</a></span> - Constructor for class robocode.<a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode">RadarTurnCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new RadarTurnCompleteCondition with default priority.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RadarTurnCompleteCondition.html#RadarTurnCompleteCondition-robocode.AdvancedRobot-int-">RadarTurnCompleteCondition(AdvancedRobot, int)</a></span> - Constructor for class robocode.<a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode">RadarTurnCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new RadarTurnCompleteCondition with the specified priority.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#ramDamage">ramDamage</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#ramDamageBonus">ramDamageBonus</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/control/RandomFactory.html" title="class in robocode.control"><span class="typeNameLink">RandomFactory</span></a> - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">The RandomFactory is used for controlling the generation of random numbers,
 and supports generating random numbers that are deterministic, which is
 useful for testing purposes.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RandomFactory.html#RandomFactory--">RandomFactory()</a></span> - Constructor for class robocode.control.<a href="robocode/control/RandomFactory.html" title="class in robocode.control">RandomFactory</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#rank">rank</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/RateControlRobot.html" title="class in robocode"><span class="typeNameLink">RateControlRobot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This advanced robot type allows you to set a rate for each of the robot's movements.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#RateControlRobot--">RateControlRobot()</a></span> - Constructor for class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#red">red</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color red  (0xFF0000)</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#removeBattleListener-robocode.control.events.IBattleListener-">removeBattleListener(IBattleListener)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Removes a battle listener that has previously been added to this object.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#removeBattleListener-robocode.control.events.IBattleListener-">removeBattleListener(IBattleListener)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Removes a battle listener that has previously been added to this object.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#removeCustomEvent-robocode.Condition-">removeCustomEvent(Condition)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Removes a custom event that was previously added by calling
 <a href="robocode/AdvancedRobot.html#addCustomEvent-robocode.Condition-"><code>AdvancedRobot.addCustomEvent(Condition)</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#removeCustomEvent-robocode.Condition-">removeCustomEvent(Condition)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Removes a custom event that was previously added by calling
 <a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#addCustomEvent-robocode.Condition-"><code>IAdvancedRobotPeer.addCustomEvent(Condition)</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#rescan--">rescan()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Rescan for other robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RandomFactory.html#resetDeterministic-long-">resetDeterministic(long)</a></span> - Static method in class robocode.control.<a href="robocode/control/RandomFactory.html" title="class in robocode.control">RandomFactory</a></dt>
<dd>
<div class="block">Resets the random number generator instance to be deterministic when
 generating random numbers.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#resume--">resume()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately resumes the movement you stopped by <a href="robocode/Robot.html#stop--"><code>Robot.stop()</code></a>, if any.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--">resume()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dt>
<dd>
<div class="block">Immediately resumes the movement you stopped by <a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>IStandardRobotPeer.stop(boolean)</code></a>, if
 any.</div>
</dd>
<dt><a href="robocode/package-summary.html">robocode</a> - package robocode</dt>
<dd>
<div class="block">Robot API used for writing robots for Robocode.</div>
</dd>
<dt><a href="robocode/Robocode.html" title="class in robocode"><span class="typeNameLink">Robocode</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Robocode - A programming game involving battling AI tanks.<br>
 Copyright (c) 2001-2025 Mathew A.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robocode.html#Robocode--">Robocode()</a></span> - Constructor for class robocode.<a href="robocode/Robocode.html" title="class in robocode">Robocode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/annotation/package-summary.html">robocode.annotation</a> - package robocode.annotation</dt>
<dd>
<div class="block">Contains annotations that can be used with Robocode.</div>
</dd>
<dt><a href="robocode/control/package-summary.html">robocode.control</a> - package robocode.control</dt>
<dd>
<div class="block">The Robocode Control API is used for controlling the Robocode
 application from another external application.</div>
</dd>
<dt><a href="robocode/control/events/package-summary.html">robocode.control.events</a> - package robocode.control.events</dt>
<dd>
<div class="block">Battle events that occurs during a game, and which are used for the
 robocode.control.IBattleListener class.</div>
</dd>
<dt><a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a> - package robocode.control.snapshot</dt>
<dd>
<div class="block">Snapshots of the battle turns, robots, bullets, scores etc.</div>
</dd>
<dt><a href="robocode/robotinterfaces/package-summary.html">robocode.robotinterfaces</a> - package robocode.robotinterfaces</dt>
<dd>
<div class="block">Robot Interfaces used for creating new robot types, e.g.</div>
</dd>
<dt><a href="robocode/robotinterfaces/peer/package-summary.html">robocode.robotinterfaces.peer</a> - package robocode.robotinterfaces.peer</dt>
<dd>
<div class="block">Robot peers available for implementing new robot types based on the Robot Interfaces.</div>
</dd>
<dt><a href="robocode/util/package-summary.html">robocode.util</a> - package robocode.util</dt>
<dd>
<div class="block">Utility classes that can be used when writing robots.</div>
</dd>
<dt><a href="robocode/control/RobocodeEngine.html" title="class in robocode.control"><span class="typeNameLink">RobocodeEngine</span></a> - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">The RobocodeEngine is the interface provided for external applications
 in order to let these applications run battles within the Robocode application,
 and to get the results from these battles.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#RobocodeEngine--">RobocodeEngine()</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Creates a new RobocodeEngine for controlling Robocode.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#RobocodeEngine-java.io.File-">RobocodeEngine(File)</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Creates a new RobocodeEngine for controlling Robocode.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#RobocodeEngine-java.io.File-robocode.control.RobocodeListener-">RobocodeEngine(File, RobocodeListener)</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use <a href="robocode/control/RobocodeEngine.html#RobocodeEngine-java.io.File-"><code>RobocodeEngine.RobocodeEngine(File)</code></a> and
 <a href="robocode/control/RobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-"><code>addBattleListener()</code></a> instead.
 <p>
 Creates a new RobocodeEngine for controlling Robocode.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#RobocodeEngine-robocode.control.RobocodeListener-">RobocodeEngine(RobocodeListener)</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use <a href="robocode/control/RobocodeEngine.html#RobocodeEngine--"><code>RobocodeEngine.RobocodeEngine()</code></a> and
 <a href="robocode/control/RobocodeEngine.html#addBattleListener-robocode.control.events.IBattleListener-"><code>addBattleListener()</code></a> instead.
 <p>
 Creates a new RobocodeEngine for controlling Robocode. The JAR file of
 Robocode is used to determine the root directory of Robocode.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#RobocodeEngine-robocode.control.events.IBattleListener-">RobocodeEngine(IBattleListener)</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/RobocodeFileOutputStream.html" title="class in robocode"><span class="typeNameLink">RobocodeFileOutputStream</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">RobocodeFileOutputStream is similar to a <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileOutputStream.html?is-external=true" title="class or interface in java.io"><code>FileOutputStream</code></a>
 and is used for streaming/writing data out to a file, which you got
 previously by calling <a href="robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>getDataFile()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#RobocodeFileOutputStream-java.io.File-">RobocodeFileOutputStream(File)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Constructs a new RobocodeFileOutputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#RobocodeFileOutputStream-java.io.FileDescriptor-">RobocodeFileOutputStream(FileDescriptor)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#RobocodeFileOutputStream-java.lang.String-">RobocodeFileOutputStream(String)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Constructs a new RobocodeFileOutputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#RobocodeFileOutputStream-java.lang.String-boolean-">RobocodeFileOutputStream(String, boolean)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Constructs a new RobocodeFileOutputStream.</div>
</dd>
<dt><a href="robocode/RobocodeFileWriter.html" title="class in robocode"><span class="typeNameLink">RobocodeFileWriter</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">RobocodeFileWriter is similar to a <a href="https://docs.oracle.com/javase/8/docs/api/java/io/FileWriter.html?is-external=true" title="class or interface in java.io"><code>FileWriter</code></a> and is used for
 writing data out to a file, which you got by calling <a href="robocode/AdvancedRobot.html#getDataFile-java.lang.String-"><code>getDataFile()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.io.File-">RobocodeFileWriter(File)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileWriter.html" title="class in robocode">RobocodeFileWriter</a></dt>
<dd>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.io.FileDescriptor-">RobocodeFileWriter(FileDescriptor)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileWriter.html" title="class in robocode">RobocodeFileWriter</a></dt>
<dd>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.lang.String-">RobocodeFileWriter(String)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileWriter.html" title="class in robocode">RobocodeFileWriter</a></dt>
<dd>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileWriter.html#RobocodeFileWriter-java.lang.String-boolean-">RobocodeFileWriter(String, boolean)</a></span> - Constructor for class robocode.<a href="robocode/RobocodeFileWriter.html" title="class in robocode">RobocodeFileWriter</a></dt>
<dd>
<div class="block">Constructs a new RobocodeFileWriter.</div>
</dd>
<dt><a href="robocode/control/RobocodeListener.html" title="interface in robocode.control"><span class="typeNameLink">RobocodeListener</span></a> - Interface in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Since 1.6.2. Use the <a href="robocode/control/events/IBattleListener.html" title="interface in robocode.control.events"><code>IBattleListener</code></a> instead.
 <p>
 A listener interface for receiving callbacks from the <a href="robocode/control/RobocodeEngine.html" title="class in robocode.control"><code>RobocodeEngine</code></a>.</span></div>
</div>
</dd>
<dt><a href="robocode/Robot.html" title="class in robocode"><span class="typeNameLink">Robot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">The basic robot class that you will extend to create your own robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#Robot--">Robot()</a></span> - Constructor for class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Constructs a new robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#ROBOT_HIT_BONUS">ROBOT_HIT_BONUS</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The amount of bonus damage dealt by a robot ramming an opponent by moving forward into it,
 which is 2 x <a href="robocode/Rules.html#ROBOT_HIT_DAMAGE"><code>Rules.ROBOT_HIT_DAMAGE</code></a> = 1.2 energy points.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Rules.html#ROBOT_HIT_DAMAGE">ROBOT_HIT_DAMAGE</a></span> - Static variable in class robocode.<a href="robocode/Rules.html" title="class in robocode">Rules</a></dt>
<dd>
<div class="block">The amount of damage taken when a robot hits or is hit by another robot,
 which is 0.6 energy points.</div>
</dd>
<dt><a href="robocode/RobotDeathEvent.html" title="class in robocode"><span class="typeNameLink">RobotDeathEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/Robot.html#onRobotDeath-robocode.RobotDeathEvent-"><code>onRobotDeath()</code></a>
 when another robot (not your robot) dies.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobotDeathEvent.html#RobotDeathEvent-java.lang.String-">RobotDeathEvent(String)</a></span> - Constructor for class robocode.<a href="robocode/RobotDeathEvent.html" title="class in robocode">RobotDeathEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new RobotDeathEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#robotObject">robotObject</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Instance of tested robot</div>
</dd>
<dt><a href="robocode/control/RobotResults.html" title="class in robocode.control"><span class="typeNameLink">RobotResults</span></a> - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">Contains the battle results for an individual robot</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotResults.html#RobotResults-robocode.control.RobotSpecification-java.lang.String-int-double-double-double-double-double-double-double-int-int-int-">RobotResults(RobotSpecification, String, int, double, double, double, double, double, double, double, int, int, int)</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></dt>
<dd>
<div class="block">Constructs a new RobotResults.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotResults.html#RobotResults-robocode.control.RobotSpecification-robocode.BattleResults-">RobotResults(RobotSpecification, BattleResults)</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobotResults.html" title="class in robocode.control">RobotResults</a></dt>
<dd>
<div class="block">Constructs new RobotResults based on a <a href="robocode/control/RobotSpecification.html" title="class in robocode.control"><code>RobotSpecification</code></a> and <a href="robocode/BattleResults.html" title="class in robocode"><code>BattleResults</code></a>.</div>
</dd>
<dt><a href="robocode/control/RobotSetup.html" title="class in robocode.control"><span class="typeNameLink">RobotSetup</span></a> - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">Contains the initial position and heading for a robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotSetup.html#RobotSetup-java.lang.Double-java.lang.Double-java.lang.Double-">RobotSetup(Double, Double, Double)</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobotSetup.html" title="class in robocode.control">RobotSetup</a></dt>
<dd>
<div class="block">Constructs a new RobotSetup.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#robotsPath">robotsPath</a></span> - Static variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/control/RobotSpecification.html" title="class in robocode.control"><span class="typeNameLink">RobotSpecification</span></a> - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">Defines the properties of a robot, which is returned from
 <a href="robocode/control/RobocodeEngine.html#getLocalRepository--"><code>RobocodeEngine.getLocalRepository()</code></a>.</div>
</dd>
<dt><a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot"><span class="typeNameLink">RobotState</span></a> - Enum in <a href="robocode/control/snapshot/package-summary.html">robocode.control.snapshot</a></dt>
<dd>
<div class="block">Defines a robot state, which can be: active on the battlefield, hitting a wall or robot this turn, or dead.</div>
</dd>
<dt><a href="robocode/RobotStatus.html" title="class in robocode"><span class="typeNameLink">RobotStatus</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Contains the status of a robot for a specific time/turn returned by
 <a href="robocode/StatusEvent.html#getStatus--"><code>StatusEvent.getStatus()</code></a>.</div>
</dd>
<dt><a href="robocode/control/RobotTestBed.html" title="class in robocode.control"><span class="typeNameLink">RobotTestBed</span></a>&lt;<a href="robocode/control/RobotTestBed.html" title="type parameter in RobotTestBed">R</a> extends <a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a>&gt; - Class in <a href="robocode/control/package-summary.html">robocode.control</a></dt>
<dd>
<div class="block">RobotTestBed provides a superclass that can be extended in order to implement JUnit tests
 for Robocode robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#RobotTestBed--">RobotTestBed()</a></span> - Constructor for class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#robotX">robotX</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current horizontal location of this robot (in pixels).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#robotY">robotY</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current vertical location of this robot (in pixels).</div>
</dd>
<dt><a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">RoundEndedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A RoundEndedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onRoundEnded-robocode.control.events.RoundEndedEvent-"><code>onRoundEnded()</code></a> when the current round of a battle has ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundEndedEvent.html#RoundEndedEvent-int-int-int-">RoundEndedEvent(int, int, int)</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/RoundEndedEvent.html" title="class in robocode.control.events">RoundEndedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new RoundEndedEvent.</div>
</dd>
<dt><a href="robocode/RoundEndedEvent.html" title="class in robocode"><span class="typeNameLink">RoundEndedEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A RoundEndedEvent is sent to <a href="robocode/Robot.html#onRoundEnded-robocode.RoundEndedEvent-"><code>onRoundEnded()</code></a> when a round has ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RoundEndedEvent.html#RoundEndedEvent-int-int-int-">RoundEndedEvent(int, int, int)</a></span> - Constructor for class robocode.<a href="robocode/RoundEndedEvent.html" title="class in robocode">RoundEndedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new RoundEndedEvent.</div>
</dd>
<dt><a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">RoundStartedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A RoundStartedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onRoundStarted-robocode.control.events.RoundStartedEvent-"><code>onRoundStarted()</code></a> when a new round in a battle is started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/RoundStartedEvent.html#RoundStartedEvent-robocode.control.snapshot.ITurnSnapshot-int-java.util.List-">RoundStartedEvent(ITurnSnapshot, int, List&lt;IBasicRobot&gt;)</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/RoundStartedEvent.html" title="class in robocode.control.events">RoundStartedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new RoundStartedEvent.</div>
</dd>
<dt><a href="robocode/Rules.html" title="class in robocode"><span class="typeNameLink">Rules</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">Constants and methods that defines the rules of Robocode.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#run-java.lang.String-java.lang.String-">run(String, String)</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#run-java.lang.String-">run(String)</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#run--">run()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#run--">run()</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The main method in every robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#run--">run()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">The main method in every robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-">runBattle(BattleSpecification)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Runs the specified battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-boolean-">runBattle(BattleSpecification, boolean)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Runs the specified battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-">runBattle(BattleSpecification, String, boolean)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Runs the specified battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-boolean-">runBattle(BattleSpecification, String, boolean, boolean)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#runBattle-robocode.control.BattleSpecification-">runBattle(BattleSpecification)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Runs the specified battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#runBattle-robocode.control.BattleSpecification-boolean-">runBattle(BattleSpecification, boolean)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Runs the specified battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-">runBattle(BattleSpecification, String, boolean)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Runs the specified battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#runBattle-robocode.control.BattleSpecification-java.lang.String-boolean-boolean-">runBattle(BattleSpecification, String, boolean, boolean)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#runBattle-java.lang.String-int-java.lang.String-">runBattle(String, int, String)</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#runSetup--">runSetup()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#runTeardown--">runTeardown()</a></span> - Method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><a href="robocode/annotation/SafeStatic.html" title="annotation in robocode.annotation"><span class="typeNameLink">SafeStatic</span></a> - Annotation Type in <a href="robocode/annotation/package-summary.html">robocode.annotation</a></dt>
<dd>
<div class="block">Annotation used for marking a static field as being safe so that Robocode should
 not print out warnings at runtime when this annotation is being used.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#scan--">scan()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Scans for other robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#scannedAngle">scannedAngle</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current angle to the scanned nearest other robot (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#scannedBearing">scannedBearing</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current angle to the scanned nearest other robot (in degrees) compared to
 the body of this robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#scannedDistance">scannedDistance</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current distance to the scanned nearest other robot (in pixels).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#scannedEnergy">scannedEnergy</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current energy of scanned nearest other robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#scannedHeading">scannedHeading</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current heading of the scanned nearest other robot (in degrees).</div>
</dd>
<dt><a href="robocode/ScannedRobotEvent.html" title="class in robocode"><span class="typeNameLink">ScannedRobotEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A ScannedRobotEvent is sent to <a href="robocode/Robot.html#onScannedRobot-robocode.ScannedRobotEvent-"><code>onScannedRobot()</code></a> when you scan a robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent--">ScannedRobotEvent()</a></span> - Constructor for class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent-java.lang.String-double-double-double-double-double-boolean-"><code>ScannedRobotEvent.ScannedRobotEvent(String, double, double, double, double, double, boolean)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent-java.lang.String-double-double-double-double-double-">ScannedRobotEvent(String, double, double, double, double, double)</a></span> - Constructor for class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent-java.lang.String-double-double-double-double-double-boolean-"><code>ScannedRobotEvent.ScannedRobotEvent(String, double, double, double, double, double, boolean)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/ScannedRobotEvent.html#ScannedRobotEvent-java.lang.String-double-double-double-double-double-boolean-">ScannedRobotEvent(String, double, double, double, double, double, boolean)</a></span> - Constructor for class robocode.<a href="robocode/ScannedRobotEvent.html" title="class in robocode">ScannedRobotEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new ScannedRobotEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#scannedVelocity">scannedVelocity</a></span> - Variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Current velocity of the scanned nearest other robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#score">score</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#seconds">seconds</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html#sendMessage-java.lang.String-java.io.Serializable-">sendMessage(String, Serializable)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/ITeamRobotPeer.html" title="interface in robocode.robotinterfaces.peer">ITeamRobotPeer</a></dt>
<dd>
<div class="block">Sends a message to one (or more) teammates.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#sendMessage-java.lang.String-java.io.Serializable-">sendMessage(String, Serializable)</a></span> - Method in class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>
<div class="block">Sends a message to one (or more) teammates.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#serialVersionUID">serialVersionUID</a></span> - Static variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustGunForBodyTurn-boolean-">setAdjustGunForBodyTurn(boolean)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dt>
<dd>
<div class="block">Sets the gun to adjust for the bot's turn, so the gun behaves like it is
 turning independent of the bot's turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setAdjustGunForRobotTurn-boolean-">setAdjustGunForRobotTurn(boolean)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the gun to turn independent from the robot's turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForBodyTurn-boolean-">setAdjustRadarForBodyTurn(boolean)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dt>
<dd>
<div class="block">Sets the radar to turn independent from the robot's turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn(boolean)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the radar to turn independent from the gun's turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#setAdjustRadarForGunTurn-boolean-">setAdjustRadarForGunTurn(boolean)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dt>
<dd>
<div class="block">Sets the radar to adjust for the gun's turn, so the radar behaves like it is
 turning independent of the gun's turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setAdjustRadarForRobotTurn-boolean-">setAdjustRadarForRobotTurn(boolean)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the radar to turn independent from the robot's turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setAhead-double-">setAhead(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot to move ahead (forward) by distance measured in pixels
 when the next execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setAllColors-java.awt.Color-">setAllColors(Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets all the robot's color to the same color in the same time, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setBack-double-">setBack(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot to move back by distance measured in pixels when the next
 execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setBodyColor-java.awt.Color-">setBodyColor(Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the color of the robot's body.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBodyColor-java.awt.Color-">setBodyColor(Color)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Sets the color of the robot's body.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setBulletColor-java.awt.Color-">setBulletColor(Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the color of the robot's bullets.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setBulletColor-java.awt.Color-">setBulletColor(Color)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Sets the color of the robot's bullets.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setCall--">setCall()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">This call <em>must</em> be made from a robot call to inform the game
 that the robot made a <code>set*</code> call like e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#setColors-int-int-int-">setColors(int, int, int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Sets the colors of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#setColors-int-int-int-int-int-">setColors(int, int, int, int, int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Sets the colors of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-">setColors(Color, Color, Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the color of the robot's body, gun, and radar in the same time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setColors-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-java.awt.Color-">setColors(Color, Color, Color, Color, Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the color of the robot's body, gun, radar, bullet, and scan arc in
 the same time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty(String, String)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the debug property with the specified key to the specified value.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setDebugProperty-java.lang.String-java.lang.String-">setDebugProperty(String, String)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Sets the debug property with the specified key to the specified value.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setDumpingErrors-boolean-">setDumpingErrors(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Sets whether error messages should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setDumpingMessages-boolean-">setDumpingMessages(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Sets whether robot messages should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setDumpingOutput-boolean-">setDumpingOutput(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Sets whether robot output should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setDumpingPositions-boolean-">setDumpingPositions(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Sets whether robot positions should be printed during each turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setDumpingTurns-boolean-">setDumpingTurns(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Sets whether each turn should be printed out.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setErrorDumping-boolean-">setErrorDumping(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Thread-safe method to modify the error dumping flag.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setEventPriority-java.lang.String-int-">setEventPriority(String, int)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the priority of a class of events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setEventPriority-java.lang.String-int-">setEventPriority(String, int)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the priority of a class of events.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setFire-double-">setFire(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setFire-double-">setFire(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setFireBullet-double-">setFireBullet(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the gun to fire a bullet when the next execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setGunColor-java.awt.Color-">setGunColor(Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the color of the robot's gun.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setGunColor-java.awt.Color-">setGunColor(Color)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Sets the color of the robot's gun.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#setGunImageName-java.lang.String-">setGunImageName(String)</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#setGunRotationRate-double-">setGunRotationRate(double)</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Sets the gun's clockwise (right) rotation per turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#setGunRotationRateRadians-double-">setGunRotationRateRadians(double)</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Sets the gun's clockwise (right) rotation per turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#setInterruptible-boolean-">setInterruptible(boolean)</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block">This call has moved to <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>, and will no longer function in
 the <a href="robocode/Robot.html" title="class in robocode"><code>Robot</code></a> class.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setInterruptible-boolean-">setInterruptible(boolean)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Call this during an event handler to allow new events of the same
 priority to restart the event handler.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setInterruptible-boolean-">setInterruptible(boolean)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Call this during an event handler to allow new events of the same
 priority to restart the event handler.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#setLogErrorsEnabled-boolean-">setLogErrorsEnabled(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Enables or disables errors logged to <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/System.html?is-external=true#err" title="class or interface in java.lang"><code>System.err</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#setLogMessagesEnabled-boolean-">setLogMessagesEnabled(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Enables or disables messages and warnings logged to <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/System.html?is-external=true#out" title="class or interface in java.lang"><code>System.out</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setMaxTurnRate-double-">setMaxTurnRate(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the maximum turn rate of the robot measured in degrees if the robot
 should turn slower than <a href="robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a> (10 degress/turn).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxTurnRate-double-">setMaxTurnRate(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the maximum turn rate of the robot measured in degrees if the robot
 should turn slower than <a href="robocode/Rules.html#MAX_TURN_RATE"><code>Rules.MAX_TURN_RATE</code></a> (10 degress/turn).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setMaxVelocity-double-">setMaxVelocity(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the maximum velocity of the robot measured in pixels/turn if the
 robot should move slower than <a href="robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a> (8 pixels/turn).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMaxVelocity-double-">setMaxVelocity(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the maximum velocity of the robot measured in pixels/turn if the
 robot should move slower than <a href="robocode/Rules.html#MAX_VELOCITY"><code>Rules.MAX_VELOCITY</code></a> (8 pixels/turn).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setMessageDumping-boolean-">setMessageDumping(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Thread-safe method to modify the message dumping flag.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setMove-double-">setMove(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the robot to move forward or backward by distance measured in pixels
 when the next execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#setName-java.lang.String-">setName(String)</a></span> - Method in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Sets the name of this condition.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_RobotBase.html#setOut-java.io.PrintStream-">setOut(PrintStream)</a></span> - Method in class robocode.<a href="robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicRobot.html#setOut-java.io.PrintStream-">setOut(PrintStream)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></dt>
<dd>
<div class="block">Do not call this method!</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setOutputDumping-boolean-">setOutputDumping(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Thread-safe method to modify the output dumping flag.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_RobotBase.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer(IBasicRobotPeer)</a></span> - Method in class robocode.<a href="robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></dt>
<dd>
<div class="block">Do not call this method! Your robot will simply stop interacting with
 the game.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/IBasicRobot.html#setPeer-robocode.robotinterfaces.peer.IBasicRobotPeer-">setPeer(IBasicRobotPeer)</a></span> - Method in interface robocode.robotinterfaces.<a href="robocode/robotinterfaces/IBasicRobot.html" title="interface in robocode.robotinterfaces">IBasicRobot</a></dt>
<dd>
<div class="block">Do not call this method! Your robot will simply stop interacting with
 the game.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setPositionDumping-boolean-">setPositionDumping(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Thread-safe method to modify the position dumping flag.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#setPriority-int-">setPriority(int)</a></span> - Method in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Sets the priority of this condition.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Event.html#setPriority-int-">setPriority(int)</a></span> - Method in class robocode.<a href="robocode/Event.html" title="class in robocode">Event</a></dt>
<dd>
<div class="block">Changes the priority of this event.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setRadarColor-java.awt.Color-">setRadarColor(Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the color of the robot's radar.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setRadarColor-java.awt.Color-">setRadarColor(Color)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Sets the color of the robot's radar.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#setRadarImageName-java.lang.String-">setRadarImageName(String)</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#setRadarRotationRate-double-">setRadarRotationRate(double)</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Sets the radar's clockwise (right) rotation per turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#setRadarRotationRateRadians-double-">setRadarRotationRateRadians(double)</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Sets the radar's clockwise (right) rotation per turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RandomFactory.html#setRandom-java.util.Random-">setRandom(Random)</a></span> - Static method in class robocode.control.<a href="robocode/control/RandomFactory.html" title="class in robocode.control">RandomFactory</a></dt>
<dd>
<div class="block">Sets the random number generator instance used for generating a stream of
 random numbers.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setResume--">setResume()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot to resume the movement stopped by <a href="robocode/Robot.html#stop--"><code>stop()</code></a>
 or <a href="robocode/AdvancedRobot.html#setStop--"><code>AdvancedRobot.setStop()</code></a>, if any.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setResume--">setResume()</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the robot to resume the movement stopped by
 <a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a> or
 <a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setStop-boolean-"><code>IAdvancedRobotPeer.setStop(boolean)</code></a>, if any.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_Robot.html#setRobotImageName-java.lang.String-">setRobotImageName(String)</a></span> - Method in class robocode.<a href="robocode/_Robot.html" title="class in robocode">_Robot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This call is not used.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#setScanColor-java.awt.Color-">setScanColor(Color)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Sets the color of the robot's scan arc.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#setScanColor-java.awt.Color-">setScanColor(Color)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Sets the color of the robot's scan arc.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setStop--">setStop()</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">This call is identical to <a href="robocode/Robot.html#stop--"><code>stop()</code></a>, but returns immediately, and
 will not execute until you call <a href="robocode/AdvancedRobot.html#execute--"><code>AdvancedRobot.execute()</code></a> or take an action that
 executes.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setStop-boolean-">setStop(boolean)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">This call is identical to <a href="robocode/Robot.html#stop-boolean-"><code>stop(boolean)</code></a>, but
 returns immediately, and will not execute until you call
 <a href="robocode/AdvancedRobot.html#execute--"><code>AdvancedRobot.execute()</code></a> or take an action that executes.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setStop-boolean-">setStop(boolean)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">This call is identical to <a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-"><code>stop(boolean)</code></a>, but returns immediately, and will not execute until you
 call <a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#execute--"><code>execute()</code></a> or take an action that executes.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Event.html#setTime-long-">setTime(long)</a></span> - Method in class robocode.<a href="robocode/Event.html" title="class in robocode">Event</a></dt>
<dd>
<div class="block">Changes the time when this event occurred.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnBody-double-">setTurnBody(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the robot's body to turn right or left by radians when the next
 execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#setTurnDumping-boolean-">setTurnDumping(boolean)</a></span> - Static method in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>
<div class="block">Thread-safe method to modify the turn dumping flag.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnGun-double-">setTurnGun(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the robot's gun to turn right or left by radians when the next
 execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnGunLeft-double-">setTurnGunLeft(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's gun to turn left by degrees when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#setTurnGunLeftDegrees-double-">setTurnGunLeftDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnGunLeft-double-"><code>setTurnGunLeft</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#setTurnGunLeftRadians-double-">setTurnGunLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnGunLeftRadians-double-">setTurnGunLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's gun to turn left by radians when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnGunRight-double-">setTurnGunRight(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's gun to turn right by degrees when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#setTurnGunRightDegrees-double-">setTurnGunRightDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnGunRight-double-"><code>setTurnGunRight</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#setTurnGunRightRadians-double-">setTurnGunRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnGunRightRadians-double-">setTurnGunRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's gun to turn right by radians when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnLeft-double-">setTurnLeft(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's body to turn left by degrees when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#setTurnLeftDegrees-double-">setTurnLeftDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnLeft-double-"><code>setTurnLeft(double)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#setTurnLeftRadians-double-">setTurnLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnLeftRadians-double-">setTurnLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's body to turn left by radians when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#setTurnRadar-double-">setTurnRadar(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Sets the robot's radar to turn right or left by radians when the next
 execution takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnRadarLeft-double-">setTurnRadarLeft(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's radar to turn left by degrees when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#setTurnRadarLeftDegrees-double-">setTurnRadarLeftDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnRadarLeft-double-"><code>setTurnRadarLeft(double)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#setTurnRadarLeftRadians-double-">setTurnRadarLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnRadarLeftRadians-double-">setTurnRadarLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's radar to turn left by radians when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnRadarRight-double-">setTurnRadarRight(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's radar to turn right by degrees when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#setTurnRadarRightDegrees-double-">setTurnRadarRightDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnRadarRight-double-"><code>setTurnRadarRight</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#setTurnRadarRightRadians-double-">setTurnRadarRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnRadarRightRadians-double-">setTurnRadarRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's radar to turn right by radians when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#setTurnRate-double-">setTurnRate(double)</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Sets the robot's clockwise (right) rotation per turn, in degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#setTurnRateRadians-double-">setTurnRateRadians(double)</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Sets the robot's clockwise (right) rotation per turn, in radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnRight-double-">setTurnRight(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's body to turn right by degrees when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#setTurnRightDegrees-double-">setTurnRightDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/AdvancedRobot.html#setTurnRight-double-"><code>setTurnRight(double)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#setTurnRightRadians-double-">setTurnRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#setTurnRightRadians-double-">setTurnRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Sets the robot's body to turn right by radians when the next execution
 takes place.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RateControlRobot.html#setVelocityRate-double-">setVelocityRate(double)</a></span> - Method in class robocode.<a href="robocode/RateControlRobot.html" title="class in robocode">RateControlRobot</a></dt>
<dd>
<div class="block">Sets the speed the robot will move (forward), in pixels per turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#setVisible-boolean-">setVisible(boolean)</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Shows or hides the Robocode window.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#setVisible-boolean-">setVisible(boolean)</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Shows or hides the Robocode window.</div>
</dd>
<dt><a href="robocode/SkippedTurnEvent.html" title="class in robocode"><span class="typeNameLink">SkippedTurnEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A SkippedTurnEvent is sent to <a href="robocode/AdvancedRobot.html#onSkippedTurn-robocode.SkippedTurnEvent-"><code>onSkippedTurn()</code></a> when your robot is forced to skipping a turn.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/SkippedTurnEvent.html#SkippedTurnEvent-long-">SkippedTurnEvent(long)</a></span> - Constructor for class robocode.<a href="robocode/SkippedTurnEvent.html" title="class in robocode">SkippedTurnEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new SkippedTurnEvent.</div>
</dd>
<dt><a href="robocode/StatusEvent.html" title="class in robocode"><span class="typeNameLink">StatusEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/Robot.html#onStatus-robocode.StatusEvent-"><code>onStatus()</code></a> every
 turn in a battle to provide the status of the robot.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/StatusEvent.html#StatusEvent-robocode.RobotStatus-">StatusEvent(RobotStatus)</a></span> - Constructor for class robocode.<a href="robocode/StatusEvent.html" title="class in robocode">StatusEvent</a></dt>
<dd>
<div class="block">This constructor is called internally from the game in order to create
 a new <a href="robocode/RobotStatus.html" title="class in robocode"><code>RobotStatus</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#stop--">stop()</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately stops all movement, and saves it for a call to
 <a href="robocode/Robot.html#resume--"><code>Robot.resume()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#stop-boolean-">stop(boolean)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately stops all movement, and saves it for a call to
 <a href="robocode/Robot.html#resume--"><code>Robot.resume()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#stop-boolean-">stop(boolean)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dt>
<dd>
<div class="block">Immediately stops all movement, and saves it for a call to <a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#resume--"><code>IStandardRobotPeer.resume()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#survival">survival</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:T">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#takeScreenshot--">takeScreenshot()</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Saves screenshot to disk, if the UI is initialized</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#takeScreenshot--">takeScreenshot()</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Saves screenshot to disk, if the UI is initialized</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#teamLeaderName">teamLeaderName</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><a href="robocode/TeamRobot.html" title="class in robocode"><span class="typeNameLink">TeamRobot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A TeamRobot is a robot that is made for battles between teams of robots.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TeamRobot.html#TeamRobot--">TeamRobot()</a></span> - Constructor for class robocode.<a href="robocode/TeamRobot.html" title="class in robocode">TeamRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/Condition.html#test--">test()</a></span> - Method in class robocode.<a href="robocode/Condition.html" title="class in robocode">Condition</a></dt>
<dd>
<div class="block">Overriding the test() method is the point of a Condition.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/GunTurnCompleteCondition.html#test--">test()</a></span> - Method in class robocode.<a href="robocode/GunTurnCompleteCondition.html" title="class in robocode">GunTurnCompleteCondition</a></dt>
<dd>
<div class="block">Tests if the gun has stopped turning.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/MoveCompleteCondition.html#test--">test()</a></span> - Method in class robocode.<a href="robocode/MoveCompleteCondition.html" title="class in robocode">MoveCompleteCondition</a></dt>
<dd>
<div class="block">Tests if the robot has stopped moving.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RadarTurnCompleteCondition.html#test--">test()</a></span> - Method in class robocode.<a href="robocode/RadarTurnCompleteCondition.html" title="class in robocode">RadarTurnCompleteCondition</a></dt>
<dd>
<div class="block">Tests if the radar has stopped turning.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TurnCompleteCondition.html#test--">test()</a></span> - Method in class robocode.<a href="robocode/TurnCompleteCondition.html" title="class in robocode">TurnCompleteCondition</a></dt>
<dd>
<div class="block">Tests if the robot has finished turning.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobotTestBed.html#testErrorListener">testErrorListener</a></span> - Variable in class robocode.control.<a href="robocode/control/RobotTestBed.html" title="class in robocode.control">RobotTestBed</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/BattleResults.html#thirds">thirds</a></span> - Variable in class robocode.<a href="robocode/BattleResults.html" title="class in robocode">BattleResults</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/BulletState.html#toState-int-">toState(int)</a></span> - Static method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></dt>
<dd>
<div class="block">Returns a BulletState based on an integer value that represents a BulletState.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#toState-int-">toState(int)</a></span> - Static method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Returns a RobotState based on an integer value that represents a RobotState.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_RobotBase.html#toString--">toString()</a></span> - Method in class robocode.<a href="robocode/_RobotBase.html" title="class in robocode">_RobotBase</a></dt>
<dt><span class="memberNameLink"><a href="robocode/Bullet.html#toString--">toString()</a></span> - Method in class robocode.<a href="robocode/Bullet.html" title="class in robocode">Bullet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnAheadLeft-int-int-">turnAheadLeft(int, int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Moves this robot forward by pixels and turns this robot left by degrees
 at the same time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnAheadRight-int-int-">turnAheadRight(int, int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Moves this robot forward by pixels and turns this robot right by degrees
 at the same time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IJuniorRobotPeer.html#turnAndMove-double-double-">turnAndMove(double, double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IJuniorRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IJuniorRobotPeer</a></dt>
<dd>
<div class="block">Moves this robot forward or backwards by pixels and turns this robot
 right or left by degrees at the same time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnBackLeft-int-int-">turnBackLeft(int, int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Moves this robot backward by pixels and turns this robot left by degrees
 at the same time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnBackRight-int-int-">turnBackRight(int, int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Moves this robot backward by pixels and turns this robot right by degrees
 at the same time.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnBody-double-">turnBody(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Immediately turns the robot's body to the right or left by radians.</div>
</dd>
<dt><a href="robocode/TurnCompleteCondition.html" title="class in robocode"><span class="typeNameLink">TurnCompleteCondition</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">A prebuilt condition you can use that indicates your robot has finished
 turning.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TurnCompleteCondition.html#TurnCompleteCondition-robocode.AdvancedRobot-">TurnCompleteCondition(AdvancedRobot)</a></span> - Constructor for class robocode.<a href="robocode/TurnCompleteCondition.html" title="class in robocode">TurnCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new TurnCompleteCondition with default priority.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/TurnCompleteCondition.html#TurnCompleteCondition-robocode.AdvancedRobot-int-">TurnCompleteCondition(AdvancedRobot, int)</a></span> - Constructor for class robocode.<a href="robocode/TurnCompleteCondition.html" title="class in robocode">TurnCompleteCondition</a></dt>
<dd>
<div class="block">Creates a new TurnCompleteCondition with the specified priority.</div>
</dd>
<dt><a href="robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">TurnEndedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A TurnEndedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onTurnEnded-robocode.control.events.TurnEndedEvent-"><code>onTurnEnded()</code></a> when the current turn in a battle round is ended.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/TurnEndedEvent.html#TurnEndedEvent-robocode.control.snapshot.ITurnSnapshot-">TurnEndedEvent(ITurnSnapshot)</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/TurnEndedEvent.html" title="class in robocode.control.events">TurnEndedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new TurnEndedEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html#turnGun-double-">turnGun(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IBasicRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IBasicRobotPeer</a></dt>
<dd>
<div class="block">Immediately turns the robot's gun to the right or left by radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnGunLeft-int-">turnGunLeft(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Turns the gun left by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#turnGunLeft-double-">turnGunLeft(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately turns the robot's gun to the left by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#turnGunLeftDegrees-double-">turnGunLeftDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnGunLeft-double-"><code>turnGunLeft</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#turnGunLeftRadians-double-">turnGunLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#turnGunLeftRadians-double-">turnGunLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Immediately turns the robot's gun to the left by radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnGunRight-int-">turnGunRight(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Turns the gun right by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#turnGunRight-double-">turnGunRight(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately turns the robot's gun to the right by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#turnGunRightDegrees-double-">turnGunRightDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnGunRight-double-"><code>turnGunRight</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#turnGunRightRadians-double-">turnGunRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#turnGunRightRadians-double-">turnGunRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Immediately turns the robot's gun to the right by radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnGunTo-int-">turnGunTo(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Turns the gun to the specified angle (in degrees).</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnLeft-int-">turnLeft(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Turns this robot left by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#turnLeft-double-">turnLeft(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately turns the robot's body to the left by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#turnLeftDegrees-double-">turnLeftDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnLeft-double-"><code>turnLeft(double)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#turnLeftRadians-double-">turnLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#turnLeftRadians-double-">turnLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Immediately turns the robot's body to the left by radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html#turnRadar-double-">turnRadar(double)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IStandardRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IStandardRobotPeer</a></dt>
<dd>
<div class="block">Immediately turns the robot's radar to the right or left by radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#turnRadarLeft-double-">turnRadarLeft(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately turns the robot's radar to the left by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#turnRadarLeftDegrees-double-">turnRadarLeftDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnRadarLeft-double-"><code>turnRadarLeft</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#turnRadarLeftRadians-double-">turnRadarLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#turnRadarLeftRadians-double-">turnRadarLeftRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Immediately turns the robot's radar to the left by radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#turnRadarRight-double-">turnRadarRight(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately turns the robot's radar to the right by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#turnRadarRightDegrees-double-">turnRadarRightDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnRadarRight-double-"><code>turnRadarRight</code></a>
             instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#turnRadarRightRadians-double-">turnRadarRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#turnRadarRightRadians-double-">turnRadarRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Immediately turns the robot's radar to the right by radians.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnRight-int-">turnRight(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Turns this robot right by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/Robot.html#turnRight-double-">turnRight(double)</a></span> - Method in class robocode.<a href="robocode/Robot.html" title="class in robocode">Robot</a></dt>
<dd>
<div class="block">Immediately turns the robot's body to the right by degrees.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRobot.html#turnRightDegrees-double-">turnRightDegrees(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRobot.html" title="class in robocode">_AdvancedRobot</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">Use <a href="robocode/Robot.html#turnRight-double-"><code>turnRight(double)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/_AdvancedRadiansRobot.html#turnRightRadians-double-">turnRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode">_AdvancedRadiansRobot</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#turnRightRadians-double-">turnRightRadians(double)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Immediately turns the robot's body to the right by radians.</div>
</dd>
<dt><a href="robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events"><span class="typeNameLink">TurnStartedEvent</span></a> - Class in <a href="robocode/control/events/package-summary.html">robocode.control.events</a></dt>
<dd>
<div class="block">A TurnStartedEvent is sent to <a href="robocode/control/events/IBattleListener.html#onTurnStarted-robocode.control.events.TurnStartedEvent-"><code>onTurnStarted()</code></a> when a new turn in a battle round is started.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/events/TurnStartedEvent.html#TurnStartedEvent--">TurnStartedEvent()</a></span> - Constructor for class robocode.control.events.<a href="robocode/control/events/TurnStartedEvent.html" title="class in robocode.control.events">TurnStartedEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new TurnStartedEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#turnTo-int-">turnTo(int)</a></span> - Method in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">Turns this robot to the specified angle (in degrees).</div>
</dd>
</dl>
<a name="I:U">
<!--   -->
</a>
<h2 class="title">U</h2>
<dl>
<dt><a href="robocode/util/Utils.html" title="class in robocode.util"><span class="typeNameLink">Utils</span></a> - Class in <a href="robocode/util/package-summary.html">robocode.util</a></dt>
<dd>
<div class="block">Utility class that provide methods for normalizing angles.</div>
</dd>
</dl>
<a name="I:V">
<!--   -->
</a>
<h2 class="title">V</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/BulletState.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/BulletState.html#values--">values()</a></span> - Static method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/BulletState.html" title="enum in robocode.control.snapshot">BulletState</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/snapshot/RobotState.html#values--">values()</a></span> - Static method in enum robocode.control.snapshot.<a href="robocode/control/snapshot/RobotState.html" title="enum in robocode.control.snapshot">RobotState</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
</dl>
<a name="I:W">
<!--   -->
</a>
<h2 class="title">W</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/AdvancedRobot.html#waitFor-robocode.Condition-">waitFor(Condition)</a></span> - Method in class robocode.<a href="robocode/AdvancedRobot.html" title="class in robocode">AdvancedRobot</a></dt>
<dd>
<div class="block">Does not return until a condition is met, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html#waitFor-robocode.Condition-">waitFor(Condition)</a></span> - Method in interface robocode.robotinterfaces.peer.<a href="robocode/robotinterfaces/peer/IAdvancedRobotPeer.html" title="interface in robocode.robotinterfaces.peer">IAdvancedRobotPeer</a></dt>
<dd>
<div class="block">Does not return until a condition is met, i.e.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/IRobocodeEngine.html#waitTillBattleOver--">waitTillBattleOver()</a></span> - Method in interface robocode.control.<a href="robocode/control/IRobocodeEngine.html" title="interface in robocode.control">IRobocodeEngine</a></dt>
<dd>
<div class="block">Will block caller until current battle is over.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/control/RobocodeEngine.html#waitTillBattleOver--">waitTillBattleOver()</a></span> - Method in class robocode.control.<a href="robocode/control/RobocodeEngine.html" title="class in robocode.control">RobocodeEngine</a></dt>
<dd>
<div class="block">Will block caller until current battle is over.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#white">white</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color white (0xFFFFFF)</div>
</dd>
<dt><a href="robocode/WinEvent.html" title="class in robocode"><span class="typeNameLink">WinEvent</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This event is sent to <a href="robocode/Robot.html#onWin-robocode.WinEvent-"><code>onWin()</code></a> when your robot
 wins the round in a battle.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/WinEvent.html#WinEvent--">WinEvent()</a></span> - Constructor for class robocode.<a href="robocode/WinEvent.html" title="class in robocode">WinEvent</a></dt>
<dd>
<div class="block">Called by the game to create a new WinEvent.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#write-byte:A-">write(byte[])</a></span> - Method in class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Writes a byte array to this output stream.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#write-byte:A-int-int-">write(byte[], int, int)</a></span> - Method in class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Writes a byte array to this output stream.</div>
</dd>
<dt><span class="memberNameLink"><a href="robocode/RobocodeFileOutputStream.html#write-int-">write(int)</a></span> - Method in class robocode.<a href="robocode/RobocodeFileOutputStream.html" title="class in robocode">RobocodeFileOutputStream</a></dt>
<dd>
<div class="block">Writes a single byte to this output stream.</div>
</dd>
</dl>
<a name="I:Y">
<!--   -->
</a>
<h2 class="title">Y</h2>
<dl>
<dt><span class="memberNameLink"><a href="robocode/JuniorRobot.html#yellow">yellow</a></span> - Static variable in class robocode.<a href="robocode/JuniorRobot.html" title="class in robocode">JuniorRobot</a></dt>
<dd>
<div class="block">The color yellow (0xFFFF00)</div>
</dd>
</dl>
<a name="I:Z:Z_">
<!--   -->
</a>
<h2 class="title">_</h2>
<dl>
<dt><a href="robocode/_AdvancedRadiansRobot.html" title="class in robocode"><span class="typeNameLink">_AdvancedRadiansRobot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This class is used by the system as a placeholder for all *Radians calls in
 <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>.</div>
</dd>
<dt><a href="robocode/_AdvancedRobot.html" title="class in robocode"><span class="typeNameLink">_AdvancedRobot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This class is used by the system, as well as being a placeholder for all deprecated
 (meaning, you should not use them) calls for <a href="robocode/AdvancedRobot.html" title="class in robocode"><code>AdvancedRobot</code></a>.</div>
</dd>
<dt><a href="robocode/_Robot.html" title="class in robocode"><span class="typeNameLink">_Robot</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This class is used by the system, as well as being a placeholder for all deprecated
 (meaning, you should not use them) calls for <a href="robocode/Robot.html" title="class in robocode"><code>Robot</code></a>.</div>
</dd>
<dt><a href="robocode/_RobotBase.html" title="class in robocode"><span class="typeNameLink">_RobotBase</span></a> - Class in <a href="robocode/package-summary.html">robocode</a></dt>
<dd>
<div class="block">This is the base class of all robots used by the system.</div>
</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<a href="#I:Y">Y</a>&nbsp;<a href="#I:Z:Z_">_</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2001-2025 <a href="https://robocode.sourceforge.io">Robocode</a>. All Rights Reserved.</small></p>
</body>
</html>
