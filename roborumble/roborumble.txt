#===============================================================================
# roborumble.txt - Configuration file for RoboRumble (1v1 battles)
#===============================================================================

#-------------------------------------------------------------------------------
# Username. It's highly recommendable that you change this to use your name
#-------------------------------------------------------------------------------

# USER      The username used when uploading the results to the RoboRumble
#           server. Hence, you should replace 'Put_Your_Name_Here' with your
#           name, which could be the initials you use for your robot(s).

USER=Put_Your_Name_Here

#-------------------------------------------------------------------------------
# Exclude filter used for excluding participants. Use with care!
#-------------------------------------------------------------------------------

# EXCLUDE   A comma separated list of all the participants you want to exclude
#           from the competition. Excluded participants will not be downloaded
#           or take part in battles. This way you can exclude participants that
#           cannot be downloaded due to web servers that are down or hanging,
#           the repository is down, robots/teams that crashes for some reason or
#           cause other trouble.
#
#           You can use the filename wildcards * and ? in the filter. Example:
#
#           EXCLUDE=xyz.*v1.?, *Nano*

EXCLUDE=

#-------------------------------------------------------------------------------
# Properties for controlling the rumble. Use YES or NOT
#-------------------------------------------------------------------------------

# DOWNLOAD  Download data like participants, missing robots, rating files etc.
#           from Internet if these have not been downloaded for 2 hours.
#
# EXECUTE   Execute battles. Battles files are first created and old battles
#           files are deleted before the battles are executed.
#
# UPLOAD    Upload results to the RoboRumble server specified by the RESULTSURL
#           property.
#
# ITERATE   If set to NOT, the rumble will only execute battles once.
#           If set to YES, the rumble will restart with new battles every time
#           the battles have been executed, and it will run infinitely until
#           terminated.

DOWNLOAD=YES
EXECUTE=YES
UPLOAD=YES
ITERATE=YES

# MELEE     Must be set if this rumble is meant for melee battles.
#
# TEAMS     Must be set if this rumble is meant for team battles.
#           This flag is necessary, as jar files for robot teams are different
#           from jar files for ordinary robots.

# MELEE=NOT as this property file is not meant for melee battles.
# TEAM=NOT, as this property file is not meant for team battles.

# Do not modify these properties!

MELEE=NOT
TEAMS=NOT

#------------------------------------------------------------------------------
# Properties for the battle engine
#------------------------------------------------------------------------------

# FIELDL      Battlefield width measured in pixels.
# FIELDH      Battlefield height measured in pixels.
#
# NUMBATTLES  Number of battles performed per rumble.
# ROUNDS      Number of rounds per battle.

# These are standard values for the RoboRumble. Do not modify these properties!

FIELDL=800
FIELDH=600
NUMBATTLES=50
ROUNDS=35

# INPUT     Input battles file that is generated by the rumble automatically.
#           The rumble uses this file for selecting which robots that must
#           battle against each other.
#
# OUTPUT    Battle results file, which is the output of running the rumble.

INPUT=./roborumble/temp/battles1v1.txt
OUTPUT=./roborumble/files/results1v1.txt

#-------------------------------------------------------------------------------
# Properties for retrieving robots from Internet
#-------------------------------------------------------------------------------

# BOTSREP   The robot repository where downloaded robots are put.
#
# TEMP      Directory containing all temporary files for RoboRumble.

BOTSREP=./robots/
TEMP=./roborumble/temp/

#-------------------------------------------------------------------------------
# Properties for updating participants from Internet
#-------------------------------------------------------------------------------

# PARTICIPANTSURL
#           URL to the web page containing all participants of the competition,
#           which will be used for updating the participants file specified with
#           the PARTICIPANTSFILE property.
#
# PARTICIPANTSFILE
#           File containing all the participants for the competition.
#
# STARTAG   Tag marking the start and end of the participants on the web page
#           pointed to with the PARTICIPANTSURL property.
#
# UPDATEBOTSURL
#           URL used for removing old participants, which is used for updating
#           the participants file specified with the PARTICIPANTSFILE property.

PARTICIPANTSURL=https://robowiki.net/wiki/RoboRumble/Participants?action=raw
PARTICIPANTSFILE=./roborumble/files/particip1v1.txt

STARTAG=pre

UPDATEBOTSURL=https://literumble.appspot.com/RemoveOldParticipant

#-------------------------------------------------------------------------------
# Properties to control the way battles are run
#-------------------------------------------------------------------------------

# RUNONLY   If left black or set to GENERAL, a new battle file is created where
#           robots from the participants file are paired at random. The number
#           of robot pairs will match number of battles defined the NUMBATTLES
#           property.
#
#           If set to MINI, a new battle file is created similar to the one
#           for GENERAL, but it will only contains robots that are MiniBots,
#           which has a code size < 1500 bytes (calculated by the codesize tool).
#
#           If set to MICRO, a new battle file is created similar to the one
#           for GENERAL, but it will only contains robots that are MicroBots,
#           which has a code size < 750 bytes (calculated by the codesize tool).
#
#           If set to NANO, a new battle file is created similar to the one for
#           GENERAL, but it will only contains robots that are NanoBots, which
#           has a code size < 250 bytes (calculated by the codesize tool).
#
#           If set to SERVER (recommended), a new battle file is created which
#           will first of all contain priority battles for robots that that has
#           priority over other robots until they have fought a specific number
#           of battles specified by the BATTLESPERBOT property. The number of
#           battles fought by the individual robots are extracted from the
#           rating files, which are downloaded from the server if the DOWNLOAD
#           property is set to YES. When no more priority battles are left,
#           robots from the participants file are paired at random similar to
#           GENERAL.
#
# BATTLESPERBOT
#           The number of battles a robot has to fight before it will no longer
#           have priority over other robots in battles. Prioritizing a robot
#           makes its rating more reliable faster as its number of fought
#           battles increases faster than when it is not prioritized.
#
# PRIORITYBATTLESFILE
#           The priority battles file that is generated automatically by the
#           rumble when the RUNONLY property is set to SERVER.

RUNONLY=SERVER

BATTLESPERBOT=2000

PRIORITYBATTLESFILE=./roborumble/temp/priority1v1.txt

#-------------------------------------------------------------------------------
# Properties for uploading the results to the server
#-------------------------------------------------------------------------------

# RESULTSURL
#           URL used for uploading the results to the server.
#
# BATTLESNUMFILE
#           File containing the number of battles fought by the robots, which is
#           returned by the server when results are uploaded to the server.

RESULTSURL=https://literumble.appspot.com/UploadedResults

BATTLESNUMFILE=./roborumble/temp/battlesnumber.txt

#-------------------------------------------------------------------------------
# Properties for related competitions
#-------------------------------------------------------------------------------

# Related competitions are competitions where participants are a subset of the
# participants in the general competition.
#
# The MiniBots (code size < 1500) are participants of the MiniRumble.
# The MicroBots (code size < 750) are participants of the MicroRumble.
# The NanoBots (code size < 250) are participants of the NanoRumble.

# Do not modify these properties!

MINIBOTS=minirumble
MICROBOTS=microrumble
NANOBOTS=nanorumble

# CODESIZEFILE:
#           The code size file that is generated automatically by the rumble in
#           order determine the code size of each individual robot.

CODESIZEFILE=./roborumble/files/codesize1v1.txt

#-------------------------------------------------------------------------------
# Properties for URLs and file names for the rating files to download
#-------------------------------------------------------------------------------

# RATINGS.URL:
#           URL to where ratings files are located on Internet.
#
# RATINGS.GENERAL:
#           File name for the rating file of the general RoboRumble.
#
# RATINGS.MINIBOTS:
#           File name for the rating file of the MiniRumble.
#
# RATINGS.MICROBOTS:
#           File name for the rating file of the MicroRumble.
#
# RATINGS.NANOBOTS:
#           File name for the rating file of the NanoRumble.

RATINGS.URL=https://literumble.appspot.com/RatingsFile

RATINGS.GENERAL=./roborumble/temp/ratings_roborumble.txt
RATINGS.MINIBOTS=./roborumble/temp/ratings_minirumble.txt
RATINGS.MICROBOTS=./roborumble/temp/ratings_microrumble.txt
RATINGS.NANOBOTS=./roborumble/temp/ratings_nanorumble.txt
