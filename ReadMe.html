<!DOCTYPE html>
<html>
<head>
<link rel="stylesheet" type="text/css" href="/robocode.css"/>
</head>
<body>

<h2 id="readme-for-robocode">ReadMe for Robocode</h2>
<p>Updated 29-Jul-2021 by <PERSON><PERSON><PERSON><PERSON></p>
<p><a href="https://robocode.sourceforge.io/">Robocode Home Page</a></p>
<h3 id="table-of-contents">TABLE OF CONTENTS</h3>
<ol>
    <li><a href="#what-is-robocode">What is Robocode?</a></li>
    <li><a href="#history-of-robocode">History of Robocode</a></li>
    <li><a href="#system-requirements">System Requirements</a></li>
    <li><a href="#getting-started">Getting Started</a></li>
    <li><a href="#robocode-api">Robocode API</a></li>
    <li><a href="#robocode-repository">Robocode Repository</a></li>
    <li><a href="#community">Community</a></li>
    <li><a href="#challenges">Challenges</a></li>
    <li><a href="#enter-the-competition">Enter the Competition</a></li>
    <li><a href="#command-line">Command Line</a></li>
    <li><a href="#reporting-issues--bugs">Reporting Issues / Bugs</a></li>
    <li><a href="#feature-requests">Feature Requests</a></li>
    <li><a href="#news">News</a></li>
    <li><a href="#how-to-contribute">How to contribute</a></li>
</ol>
<h3 id="what-is-robocode-">WHAT IS ROBOCODE?</h3>
<p>Robocode is a programming game where the goal is to code a robot battle tank to compete against other robots in a battle
    arena. So the name Robocode is short for &quot;Robot code&quot;. The player is the programmer of the robot, who will have no
    direct influence on the game. Instead, the player must write the AI of the robot telling it how to behave and react to
    events occurring in the battle arena. Battles are running in real-time and on-screen.</p>
<p>The motto of Robocode is: <em>Build the best, destroy the rest!</em></p>
<p>Beside being a programming game, Robocode is used for learning how to program primarily in the Java language, but other
    languages like Kotlin and Scala is possible as well.</p>
<p>Schools and universities are using Robocode as part of teaching how to program, but also for studying artificial
    intelligence (AI). The concept of Robocode is easy to understand, and a fun way to learn how
    to program.</p>
<p>Robocode offers a complete development environment and comes with an installer, built-in robot editor and Java compiler.
    Robocode only requires that a JRE (Java Runtime Environment) exist already on the system where Robocode is installed.
    Hence, everything a Robocode developer (Robocoder) needs to get started is provided with the main Robocode distribution
    file (<code>robocode-xxx-setup.jar</code>). Robocode also supports developing robots using external IDEs like e.g.</p>
<ul>
    <li><a href="https://www.jetbrains.com/idea/">IntelliJ IDEA</a></li>
    <li><a href="https://www.eclipse.org/downloads/">Eclipse</a></li>
    <li><a href="https://netbeans.apache.org/">NetBeans</a>,</li>
    <li><a href="https://code.visualstudio.com/">Visual Studio Code</a></li>
</ul>
<p>An external IDE aids the developer much better than the build-in robot editor in Robocode, which is just meant for
    making quick small fixes.</p>
<p>The fact that Robocode runs on the Java platform makes it possible to run it on most operating systems supporting Java,
    meaning that it will be able to run on Windows, Linux, macOS, and other UNIX variants. Note that Java 8 is
    the minimum version required by Robocode, but you can use newer versions for running Robocode and when developing bots.
    See the <a href="#system-requirements">System Requirements</a> for more information.</p>
<p>Be aware that many users of Robocode (aka Robocoders) find Robocode to be very fun, but also very addictive. :-)</p>
<p>Robocode comes free of charge and is being developed as a leisure project where no money is involved. The developers of
    Robocode are developing on Robocode because they think it is fun to do.</p>
<p>Robocode is an <a href="https://en.wikipedia.org/wiki/Open_source">Open Source</a> project, which means that all sources are open
    to everybody.</p>
<p>Robocode is provided under the terms of <a href="https://www.eclipse.org/legal/epl-v10.html">EPL</a> (Eclipse Public License).</p>
<h3 id="history-of-robocode">HISTORY OF ROBOCODE</h3>
<p>The Robocode game was originally started by Mathew A. Nelson as a personal endeavour in late 2000 and became a
    professional one when he brought it to his job at IBM, in the form of an AlphaWorks download, in July 2001.</p>
<p>IBM was interested in Robocode, as they saw an opportunity to promote Robocode as a fun way to get started with learning
    how to program in Java, and IBM wanted to promote Java as well.</p>
<p>The inspiration for creating Robocode came from <a href="https://en.wikipedia.org/wiki/Robot_Battle">Robot Battle</a>, a
    programming game written by Brad Schick in 1994. Robot Battle was, in turn, inspired by
    <a href="https://en.wikipedia.org/wiki/RobotWar">RobotWar</a>, an Apple II+ game from the early 1980s.</p>
<p>Articles from IBM (&quot;Rock &#39;em, sock &#39;em Robocode&quot;, &quot;Robocode Rumble&quot;, and &quot;Secrets from the Robocode masters&quot;), and the
    Robocode community behind the RoboWiki made Robocode very popular as a programming game, and for many years Robocode has
    been used for education and research at schools and universities all over the world.</p>
<p>At the beginning of 2005, Mathew convinced IBM to release Robocode as
    <a href="https://en.wikipedia.org/wiki/Open_source">Open Source</a> on SourceForge. At this point, the development of Robocode had
    somewhat stopped. The community around Robocode began to develop their own versions of Robocode with bug fixes and new
    features, e.g. the &quot;Contributions for Open Source Robocode&quot; and later on the two projects, RobocodeNG and Robocode 2006,
    by Flemming N. Larsen.</p>
<p>Eventually, Flemming took over for Mathew on the Robocode project at SourceForge as administrator and developer in July
    2006 to continue development on the original Robocode game. Hence, the RobocodeNG project was dropped, and Robocode 2006
    was merged into the new official Robocode version 1.1 of the game containing lots of improvements. Since then, lots of
    new versions of Robocode have been released with more and more features and contributions from the community.</p>
<p>In May 2007, the <a href="https://robowiki.net/wiki/RoboRumble">RoboRumble</a> client got built into Robocode. RoboRumble is widely
    used by the Robocode community for creating up-to-date robot ranking lists for the 1-vs-1, Melee, Team, and Twin Dual
    competitions.</p>
<p>In 2012, the Robocode called Julian (&quot;Skilgannon&quot;) created <a href="https://robowiki.net/wiki/LiteRumble">LiteRumble</a>, which is
    intended to be a lightweight, easily deployable RoboRumble system designed to run on the Google App Engine.</p>
<p>In May 2010 a <strong>.Net plugin</strong> was provided for Robocode using a .NET / Java bridge that made it possible to develop
    robots for the <a href="https://dotnet.microsoft.com/download/dotnet-framework/">.Net Framework</a> version 3.5 beside developing
    robots in Java. This feature was provided by Pavel Savara, who is a major Robocode contributor.</p>
<p>In April 2021 the .Net plugin was discontinued as the .Net Framework and the required toolchain for building both the
    plugin, and the documentation files for it had got some serious issues that made it very difficult to build and maintain.</p>
<h3 id="system-requirements">SYSTEM REQUIREMENTS</h3>
<p>To run Robocode, Java 8 or a newer version must be installed on your system. Both the Java Runtime Environment (JRE) and
    the Java Developer Kit (JDK) can be used. Note that the JRE does not include the standard Java compiler (javac) which
    comes with the JDK. However, Robocode comes with a built-in compiler ECJ (Eclipse Compiler for Java).
    Hence, it is sufficient to run Robocode with the JRE only.</p>
<p>Also, note that it is important that these environment variables have been set up before running Robocode:</p>
<ul>
    <li><p><strong>JAVA_HOME</strong> must be set up to point at the home directory for Java
        (JDK or JRE).<br>Windows example: <code>JAVA_HOME=C:\Program Files\AdoptOpenJDK\jdk-*********-hotspot</code><br>Linux and macOS example: <code>JAVA_HOME=/usr/lib/jvm/adoptopenjdk-16-hotspot-amd64</code></p>
    </li>
    <li><p><strong>PATH</strong> must include the path to the <code>bin</code> of the Java home
        directory (<code>JAVA_HOME</code>) that includes the <code>java</code> executable for starting the Java Virtual Machine (JVM).<br>Windows example: <code>PATH=%PATH%;%JAVA_HOME%</code><br>Linux, macOS example: <code>PATH=${PATH}:${JAVA_HOME}/bin</code></p>
    </li>
</ul>
<p>You can read more details from here:</p>
<ul>
    <li><a href="https://robowiki.net/wiki/Robocode/System_Requirements">System Requirements</a></li>
</ul>
<h3 id="getting-started">GETTING STARTED</h3>
<p>Most documentation about Robocode is provided thru the <a href="https://robowiki.net/">RoboWiki</a> which is hosted by Julian
    (&quot;Skilgannon&quot;). The RoboWiki is an amazing source of information about Robocode development used by most Robocoders,
    and it also contains lots of official documentation for Robocode.</p>
<p>It is recommended to read the articles on the <a href="https://robowiki.net/">RoboWiki</a> for getting started with Robocode.
    The first article to start with is provided here:</p>
<ul>
    <li><a href="https://robowiki.net/wiki/Robocode/Getting_Started">Getting Started</a></li>
</ul>
<p>Make sure to should read about the anatomy of a robot, the game physics, scoring etc.</p>
<h3 id="robocode-api">ROBOCODE API</h3>
<p>The Robocode API is provided here:</p>
<ul>
    <li><a href="https://robocode.sourceforge.io/docs/robocode/">Java Robot API</a></li>
    <li><a href="https://robocode.sourceforge.io/docs/robocode/index.html?robocode/control/package-summary.htm">Java Control API</a></li>
</ul>
<p>The Robocode API consists of several APIs.</p>
<ul>
    <li><p><strong>Robot API</strong>: Within the Java package <code>robocode</code>.
        The Robot API is used for developing robots and is the only part of the API that robots are allowed to access.</p>
    </li>
    <li><p><strong>Robot Interfaces</strong>: Within the Java package <code>robocode.robotinterfaces</code>.
        The Robot Interfaces are used for developing new robot types with a different API than the standard Robot API.
        <strong>Note:</strong> The game rules and robot behaviours cannot be changed.</p>
    </li>
    <li><p><strong>Control API</strong>: Within the Java package <code>robocode.control</code>.
        The Control API is used for letting another application startup battles with selected robots in Robocode and retrieve
        the results. It is also possible to get snapshots of robots and bullets (like position, heading, energy level etc.) at
        a specific time in a battle.</p>
    </li>
</ul>
<h3 id="robocode-repository">ROBOCODE REPOSITORY</h3>
<p>If you want to try out new robots other than the sample robots that come with Robocode, you should visit the
    <a href="https://literumble.appspot.com/">LiteRumble home</a> that contains lots of bots.</p>
<h3 id="community">COMMUNITY</h3>
<p>The community around Robocode is using the <a href="https://robowiki.net/">RoboWiki</a> as a communication channel. At the
    RoboWiki, people share new ideas, code snippets, algorithms, strategies, and lots of other stuff about Robocode. New
    official documentation from the developers of Robocode will be put at the RoboWiki as well.</p>
<p>On the <a href="https://robowiki.net/">RoboWiki</a>, these strategies are provided:</p>
<ul>
    <li><a href="https://robowiki.net/wiki/Radarr">Radar</a></li>
    <li><a href="https://robowiki.net/wiki/Category:Movement">Movement</a></li>
    <li><a href="https://robowiki.net/wiki/Category:Targeting">Targeting</a></li>
</ul>
<p>The code snippets are also provided on the RoboWiki:</p>
<ul>
    <li><a href="https://robowiki.net/wiki/Category:Code_Snippets">Code Snippets</a></li>
</ul>
<p>You can also get in touch with other Robocoders on:</p>
<ul>
    <li>The <a href="https://groups.google.com/g/robocode">Robocode</a> group at Google.</li>
    <li>The <a href="https://www.facebook.com/groups/129627130234">Facebook</a> group.</li>
</ul>
<h3 id="challenges">CHALLENGES</h3>
<p>A good way to improve yourself as a robot developer is to try out some real challenges. On the
    <a href="https://robowiki.net/">RoboWiki</a>, two famous challenges exist for testing/studying a robot´s movement, targeting, and
    gun abilities:</p>
<ul>
    <li><a href="https://robowiki.net/wiki/Category:Movement_Challenges">Movement Challenges</a></li>
    <li><a href="https://robowiki.net/wiki/Category:Targeting_Challenges">Targeting Challenges</a></li>
    <li><a href="https://robowiki.net/wiki/RoboRumble_Gun_Challenge">RoboRumble Gun Challenge</a></li>
</ul>
<p>But there is a lot of other challenges available on RoboWiki besides the ones listed here.</p>
<h3 id="enter-the-competition">ENTER THE COMPETITION</h3>
<p>If you want to challenge your robot(s) and yourself as Robocoder, the <a href="https://robowiki.net/wiki/LiteRumble">LiteRumble</a>
    is the best way to do it. LiteRumble is the ultimate collaborative effort to have a live,
    <a href="https://literumble.appspot.com/">up-to-date ranking</a> of Robocode bots.</p>
<p>So don&#39;t hesitate to <a href="https://robowiki.net/wiki/RoboRumble/Enter_The_Competition">enter the RoboRumble competition</a>.</p>
<h3 id="command-line">COMMAND LINE</h3>
<p>It is possible to specify options and predefined properties from the command-line when running Robocode. The usage of
    these can be listed by writing this from a command prompt or shell:</p>
<pre><code>robocode -<span class="hljs-built_in">help</span>
</code></pre><p>For example, it is possible to:</p>
<ul>
    <li>Disable the graphical user interface (GUI).</li>
    <li>Disable security that is specific to Robocode (but does not override the security that comes with the JVM).</li>
    <li>Enable/disable the debugging mode, useful when debugging robots.</li>
    <li>Play a battle based on an existing Robocode .battle file.</li>
    <li>Replay a recorded battle visually.</li>
    <li>Save the results of battles in a comma-separated file.</li>
</ul>
<p>You can read more details here:</p>
<ul>
    <li><a href="https://robowiki.net/w/index.php?title=Robocode/Console_Usage">Console Usage</a></li>
</ul>
<h3 id="reporting-issues-bugs">REPORTING ISSUES / BUGS</h3>
<p>If you discover an issue with Robocode you are encouraged to report it as soon as you discover it. The sooner the better.</p>
<p>A bug report should be reported on the <a href="https://sourceforge.net/p/robocode/bugs/">Bugs</a> page on the SourceForge site for
    Robocode. Each bug report will be prioritized among other bug reports depending on its impact on the game.</p>
<p>It will be a great help if you describe the necessary steps to make to reproduce the issue. You are very welcome to
    provide a screenshot, source code or anything else that will show the bug. It is also a very good idea to write which OS
    and version of both Robocode and Java you are using.</p>
<p>If you are a registered user at SourceForge (register <a href="https://sourceforge.net/user/registration">here</a> you will be able
    to add a &quot;monitor&quot; to your bug report. This way you will be able to receive notifications when someone adds comments to
    your report, but will also be able to better track the current status of the bug, e.g. when the bug is fixed and with
    which version of Robocode the fix is available.</p>
<p>If you are a developer yourself and have a good idea of how the bug can be fixed, you are more than welcome to do so by
    providing a <a href="https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/about-pull-requests">pull request</a>
    at <a href="https://github.com/robo-code/robocode">GitHub</a>. By fixing the bug, you will become a contributor to Robocode
    yourself. You can learn more about how to contribute <a href="#how-to-contribute">here</a>. Note that we accept bug fixes under the
    terms of <a href="https://www.eclipse.org/legal/epl-v10.html">EPL</a>.</p>
<h3 id="feature-requests">FEATURE REQUESTS</h3>
<p>If you got an idea for a new feature or improvement for Robocode, you are very welcome to share your idea by summiting a
    feature request or start a discussion on the
    <a href="https://groups.google.com/g/robocode-developers">Robocode Application Developers</a> group.</p>
<p>A feature request should be put on the <a href="https://sourceforge.net/p/robocode/feature-requests/">Feature Requests</a> on the
    SourceForge site for Robocode. Each feature request will be prioritized among other feature requests.</p>
<p>Note that if the feature is a big change to the game, e.g. change the robot behaviour, it might not be accepted, as
    Robocode is being used for competitions like e.g. the <a href="https://literumble.appspot.com/">LiteRumble</a>.</p>
<p>It will be a great help if you describe your idea in detail, and how you think it could be implemented into Robocode.
    For example, will it be possible to extend an existing feature with your idea?</p>
<p>If you are a registered user at SourceForge (register <a href="https://sourceforge.net/user/registration">here</a> you will be able
    to add a &quot;monitor&quot; to your request. This way you will be able to receive notifications when someone adds comments to
    your request entry, but will also be able to better track the current status of your entry, e.g. when the feature has
    been implemented and with which version of Robocode it will be available.</p>
<p>If you are a developer yourself and have a good idea of how the feature could be implemented, you are more than welcome
    to do so if the feature is being accepted. By implementing the feature, you will become a contributor to Robocode
    yourself. You can learn more about how to contribute <a href="#how-to-contribute">here</a>. Note that we accept implementations
    under the terms of <a href="https://www.eclipse.org/legal/epl-v10.html">EPL</a>.</p>
<h3 id="news">NEWS</h3>
<p>News about Robocode is put on the blog spot for Robocode. Here it is possible to subscribe to an RSS feed to receive
    news about Robocode.</p>
<ul>
    <li><a href="https://robo-code.blogspot.com/">Robocode News</a></li>
</ul>
<p>You can also follow Robocode on Twitter and Facebook here:</p>
<ul>
    <li><a href="https://twitter.com/robocode">Twitter for Robocode</a></li>
    <li><a href="https://www.facebook.com/group.php?gid=129627130234">Robocode on Facebook</a></li>
</ul>
<p>The RoboWiki can be followed on Twitter as well:</p>
<ul>
    <li><a href="https://twitter.com/robowiki">Twitter for RoboRumble</a></li>
</ul>
<h3 id="how-to-contribute">HOW TO CONTRIBUTE</h3>
<p>If you want to contribute to Robocode with e.g. a new feature or bug fix, you should start by reading the
    <a href="https://robowiki.net/wiki/Robocode/Developers_Guide_for_building_Robocode">Developers Guide for building Robocode</a>.</p>
<p>Note that we accept code changes under the terms of <a href="https://www.eclipse.org/legal/epl-v10.html">EPL</a>.</p>
<p>There exist no or little documentation about the internals of Robocode, and the codebase will need to be examined as a
    contributor to get an insight into how Robocode is implemented. Thus, it required a skilled Java developer to figure out
    how Robocode is put together.</p>
<p>Robocode is divided into several modules. You can read Pavel Savara&#39;s blog post to get a good overview of Robocode here:</p>
<ul>
    <li><a href="https://zamboch.blogspot.com/2009/06/robocode-modules-as-in-version-17.html">Robocode modules</a></li>
</ul>
<p>Help for Robocode internals can be provided through the
    <a href="https://groups.google.com/g/robocode-developers">Robocode Application Developers</a> where you can register yourself, and
    start up a new topic. This is the best way of getting information and asking about details for the internals in Robocode.</p>
<p>If a contribution is a somewhat small change to involves under 10 files, then the preferred way is to do a
    <a href="https://docs.github.com/en/github/collaborating-with-issues-and-pull-requests/about-pull-requests">pull request</a> at
    <a href="https://github.com/robo-code/robocode">GitHub</a>.</p>
<p>Your pull request will be reviewed and tested out before being accepted and merged into Robocode. Also, note that
    additional work might be done by other Robocode developers to finalize the work or make some adjustments.</p>

</body>
</html>